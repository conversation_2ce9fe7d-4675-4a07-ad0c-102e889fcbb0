# Test script for updated synthetic population generator
# This validates the integration of stochastic platelet aggregation responses

# Load required libraries
suppressMessages({
  library(dplyr)
  library(MASS)
  library(truncnorm)
})

# Load stochastic model components
source("test_stochastic_model.r")

# Test the stochastic aggregation function
cat("=== TESTING STOCHASTIC AGGREGATION FUNCTION ===\n")

# Test parameters
clinical_conc <- 300  # nM
test_patients <- 10

# Test each agonist
agonists <- c("collagen", "ADP", "thrombin", "arachidonic_acid", "ristocetin")

cat("Testing stochastic responses for", test_patients, "patients at", clinical_conc, "nM:\n\n")

results_summary <- data.frame()

for (agonist in agonists) {
  responses <- numeric(test_patients)
  
  for (i in 1:test_patients) {
    remaining <- stochastic_aggregation_inhibition(clinical_conc, agonist, i, 1)
    responses[i] <- (1 - remaining) * 100
  }
  
  mean_response <- mean(responses)
  sd_response <- sd(responses)
  
  cat(sprintf("%s: %.1f%% ± %.1f%%\n", 
              agonist, mean_response, sd_response))
  
  results_summary <- rbind(results_summary, data.frame(
    agonist = agonist,
    mean_inhibition = mean_response,
    sd_inhibition = sd_response,
    min_inhibition = min(responses),
    max_inhibition = max(responses)
  ))
}

cat("\n=== DETAILED RESULTS SUMMARY ===\n")
print(results_summary)

# Test population generator components
cat("\n=== TESTING POPULATION GENERATOR COMPONENTS ===\n")

# Test demographics generation
cat("Testing demographics generation...\n")
test_demographics <- data.frame(
  patient_id = paste0("PT", sprintf("%04d", 1:test_patients)),
  age = sample(60:80, test_patients, replace = TRUE),
  sex = sample(c("Male", "Female"), test_patients, replace = TRUE)
)

# Test clinical variables
cat("Testing clinical variables...\n")
test_clinical <- data.frame(
  baseline_aggregation = rnorm(test_patients, 85, 10),
  antiplatelets = sample(c(TRUE, FALSE), test_patients, replace = TRUE, prob = c(0.3, 0.7))
)

# Test genetic variables
cat("Testing genetic variables...\n")
test_genetic <- data.frame(
  btk_c481s_mutation = sample(c(TRUE, FALSE), test_patients, replace = TRUE, prob = c(0.05, 0.95)),
  plcg2_variants = sample(c(TRUE, FALSE), test_patients, replace = TRUE, prob = c(0.08, 0.92)),
  metabolizer_phenotype = sample(c("Poor", "Intermediate", "Normal", "Ultrarapid"), 
                                test_patients, replace = TRUE, prob = c(0.1, 0.2, 0.6, 0.1))
)

# Test stochastic platelet response generation
cat("Testing stochastic platelet response generation...\n")

# Simulate the function logic
test_responses <- data.frame()

for (i in 1:test_patients) {
  # Generate responses for each agonist
  collagen_remaining <- stochastic_aggregation_inhibition(clinical_conc, "collagen", i, 1)
  collagen_inhibition <- (1 - collagen_remaining) * 100
  
  adp_remaining <- stochastic_aggregation_inhibition(clinical_conc, "ADP", i, 1)
  adp_inhibition <- (1 - adp_remaining) * 100
  
  thrombin_remaining <- stochastic_aggregation_inhibition(clinical_conc, "thrombin", i, 1)
  thrombin_inhibition <- (1 - thrombin_remaining) * 100
  
  arachidonic_remaining <- stochastic_aggregation_inhibition(clinical_conc, "arachidonic_acid", i, 1)
  arachidonic_inhibition <- (1 - arachidonic_remaining) * 100
  
  ristocetin_remaining <- stochastic_aggregation_inhibition(clinical_conc, "ristocetin", i, 1)
  ristocetin_inhibition <- (1 - ristocetin_remaining) * 100
  
  # Apply modifying factors (simplified)
  age_factor <- 1 - max(0, (test_demographics$age[i] - 65) * 0.002)
  btk_factor <- ifelse(test_genetic$btk_c481s_mutation[i], 0.3, 1.0)
  plcg2_factor <- ifelse(test_genetic$plcg2_variants[i], 0.85, 1.0)
  
  metabolizer_factor <- switch(test_genetic$metabolizer_phenotype[i],
    "Poor" = 1.3,
    "Intermediate" = 1.1,
    "Normal" = 1.0,
    "Ultrarapid" = 0.7,
    1.0
  )
  
  baseline_factor <- test_clinical$baseline_aggregation[i] / 100
  antiplatelet_factor <- ifelse(test_clinical$antiplatelets[i], 1.15, 1.0)
  
  combined_factor <- age_factor * btk_factor * plcg2_factor * 
                    metabolizer_factor * baseline_factor * antiplatelet_factor
  
  # Apply factors
  collagen_final <- max(0, min(100, collagen_inhibition * combined_factor))
  adp_final <- max(0, min(100, adp_inhibition * combined_factor))
  thrombin_final <- max(0, min(100, thrombin_inhibition * combined_factor))
  arachidonic_final <- max(0, min(100, arachidonic_inhibition * combined_factor))
  ristocetin_final <- max(0, min(100, ristocetin_inhibition * combined_factor))
  
  # Calculate composite scores
  overall_score <- (collagen_final * 0.6 + arachidonic_final * 0.3 + 
                   (adp_final + thrombin_final + ristocetin_final) * 0.1 / 3)
  
  test_responses <- rbind(test_responses, data.frame(
    patient_id = test_demographics$patient_id[i],
    collagen_inhibition_pct = round(collagen_final, 1),
    adp_inhibition_pct = round(adp_final, 1),
    thrombin_inhibition_pct = round(thrombin_final, 1),
    arachidonic_acid_inhibition_pct = round(arachidonic_final, 1),
    ristocetin_inhibition_pct = round(ristocetin_final, 1),
    overall_inhibition_score = round(overall_score, 1),
    combined_factor = round(combined_factor, 3)
  ))
}

cat("\n=== SAMPLE PATIENT RESPONSES ===\n")
print(test_responses[1:5, ])

cat("\n=== RESPONSE STATISTICS ===\n")
response_stats <- test_responses %>%
  summarise(
    collagen_mean = mean(collagen_inhibition_pct),
    collagen_sd = sd(collagen_inhibition_pct),
    adp_mean = mean(adp_inhibition_pct),
    adp_sd = sd(adp_inhibition_pct),
    arachidonic_mean = mean(arachidonic_acid_inhibition_pct),
    arachidonic_sd = sd(arachidonic_acid_inhibition_pct),
    overall_mean = mean(overall_inhibition_score),
    overall_sd = sd(overall_inhibition_score)
  )

print(response_stats)

# Validation checks
cat("\n=== VALIDATION CHECKS ===\n")

# Check if responses are within expected ranges
collagen_in_range <- all(test_responses$collagen_inhibition_pct >= 0 & 
                        test_responses$collagen_inhibition_pct <= 100)
cat("Collagen responses in valid range (0-100%):", collagen_in_range, "\n")

# Check if collagen shows highest inhibition (should be primary pathway)
collagen_highest <- mean(test_responses$collagen_inhibition_pct) > 
                   mean(test_responses$adp_inhibition_pct)
cat("Collagen shows higher inhibition than ADP:", collagen_highest, "\n")

# Check variability
collagen_cv <- sd(test_responses$collagen_inhibition_pct) / 
               mean(test_responses$collagen_inhibition_pct) * 100
cat("Collagen coefficient of variation:", round(collagen_cv, 1), "%\n")

# Check patient-specific consistency
patient_1_collagen <- test_responses$collagen_inhibition_pct[1]
patient_1_repeat <- (1 - stochastic_aggregation_inhibition(clinical_conc, "collagen", 1, 1)) * 100
cat("Patient 1 collagen response reproducible:", 
    abs(patient_1_collagen - patient_1_repeat) < 0.1, "\n")

cat("\n=== INTEGRATION TEST COMPLETED SUCCESSFULLY ===\n")
cat("The stochastic platelet aggregation model is ready for population integration.\n")
