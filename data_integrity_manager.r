# =============================================================================
# DATA INTEGRITY AND BACKUP MANAGEMENT MODULE
# =============================================================================
# This module provides advanced data integrity monitoring, backup management,
# and automated recovery systems for critical research data.
#
# Features:
# - Automated integrity monitoring
# - Backup rotation and cleanup
# - Data corruption detection
# - Automated recovery workflows
# - Integrity reporting and alerts
#
# Author: Data Integrity System
# Date: 2024
# =============================================================================

# Load required libraries
if (!require(digest, quietly = TRUE)) {
  install.packages("digest")
  library(digest)
}

if (!require(jsonlite, quietly = TRUE)) {
  install.packages("jsonlite")
  library(jsonlite)
}

source("secure_data_storage.r")

#' Create Data Integrity Manifest
#' 
#' Creates a manifest file tracking all stored data with checksums and metadata
#' 
#' @param data_files Vector of file paths to include in manifest
#' @param manifest_path Path for the manifest file (default: "data_integrity_manifest.json")
#' @param verbose Whether to print progress messages (default: TRUE)
#' @return List with manifest creation results
create_integrity_manifest <- function(data_files, manifest_path = "data_integrity_manifest.json", verbose = TRUE) {
  
  if (verbose) cat("\n=== CREATING DATA INTEGRITY MANIFEST ===\n")
  
  manifest <- list(
    created_at = Sys.time(),
    version = "1.0",
    files = list()
  )
  
  for (file_path in data_files) {
    if (file.exists(file_path)) {
      file_info <- file.info(file_path)
      
      # Calculate file checksum
      file_checksum <- tryCatch({
        if (tools::file_ext(file_path) == "csv") {
          data <- read.csv(file_path)
          digest(data, algo = "sha256")
        } else if (tools::file_ext(file_path) == "rds") {
          data <- readRDS(file_path)
          digest(data, algo = "sha256")
        } else {
          digest(file_path, algo = "sha256", file = TRUE)
        }
      }, error = function(e) {
        if (verbose) cat(sprintf("Warning: Could not calculate checksum for %s: %s\n", file_path, e$message))
        "ERROR"
      })
      
      manifest$files[[file_path]] <- list(
        path = file_path,
        size_bytes = file_info$size,
        modified_time = file_info$mtime,
        checksum = file_checksum,
        status = "verified"
      )
      
      if (verbose) cat(sprintf("✓ Added to manifest: %s\n", basename(file_path)))
    } else {
      if (verbose) cat(sprintf("✗ File not found: %s\n", file_path))
    }
  }
  
  # Save manifest
  tryCatch({
    write_json(manifest, manifest_path, pretty = TRUE, auto_unbox = TRUE)
    if (verbose) cat(sprintf("✓ Manifest saved: %s\n", manifest_path))
    return(list(success = TRUE, manifest = manifest, path = manifest_path))
  }, error = function(e) {
    if (verbose) cat(sprintf("✗ Failed to save manifest: %s\n", e$message))
    return(list(success = FALSE, error = e$message))
  })
}

#' Verify Data Integrity
#' 
#' Verifies data integrity against a manifest file
#' 
#' @param manifest_path Path to the manifest file
#' @param verbose Whether to print detailed results (default: TRUE)
#' @return List with verification results
verify_data_integrity <- function(manifest_path = "data_integrity_manifest.json", verbose = TRUE) {
  
  if (verbose) cat("\n=== VERIFYING DATA INTEGRITY ===\n")
  
  if (!file.exists(manifest_path)) {
    if (verbose) cat("✗ Manifest file not found\n")
    return(list(success = FALSE, error = "Manifest file not found"))
  }
  
  # Load manifest
  manifest <- tryCatch({
    read_json(manifest_path)
  }, error = function(e) {
    if (verbose) cat(sprintf("✗ Failed to load manifest: %s\n", e$message))
    return(NULL)
  })
  
  if (is.null(manifest)) {
    return(list(success = FALSE, error = "Could not load manifest"))
  }
  
  verification_results <- list(
    total_files = length(manifest$files),
    verified = 0,
    corrupted = 0,
    missing = 0,
    errors = character(0),
    details = list()
  )
  
  for (file_entry in manifest$files) {
    file_path <- file_entry$path
    expected_checksum <- file_entry$checksum
    
    if (!file.exists(file_path)) {
      verification_results$missing <- verification_results$missing + 1
      verification_results$errors <- c(verification_results$errors, 
                                      sprintf("Missing file: %s", file_path))
      verification_results$details[[file_path]] <- list(status = "missing")
      
      if (verbose) cat(sprintf("✗ Missing: %s\n", basename(file_path)))
      next
    }
    
    # Calculate current checksum
    current_checksum <- tryCatch({
      if (tools::file_ext(file_path) == "csv") {
        data <- read.csv(file_path)
        digest(data, algo = "sha256")
      } else if (tools::file_ext(file_path) == "rds") {
        data <- readRDS(file_path)
        digest(data, algo = "sha256")
      } else {
        digest(file_path, algo = "sha256", file = TRUE)
      }
    }, error = function(e) {
      if (verbose) cat(sprintf("✗ Error calculating checksum for %s: %s\n", file_path, e$message))
      "ERROR"
    })
    
    if (current_checksum == "ERROR") {
      verification_results$errors <- c(verification_results$errors, 
                                     sprintf("Checksum calculation failed: %s", file_path))
      verification_results$details[[file_path]] <- list(status = "error")
      next
    }
    
    if (current_checksum == expected_checksum) {
      verification_results$verified <- verification_results$verified + 1
      verification_results$details[[file_path]] <- list(status = "verified", checksum = current_checksum)
      if (verbose) cat(sprintf("✓ Verified: %s\n", basename(file_path)))
    } else {
      verification_results$corrupted <- verification_results$corrupted + 1
      verification_results$errors <- c(verification_results$errors, 
                                      sprintf("Checksum mismatch: %s", file_path))
      verification_results$details[[file_path]] <- list(
        status = "corrupted", 
        expected = expected_checksum, 
        actual = current_checksum
      )
      if (verbose) cat(sprintf("✗ Corrupted: %s\n", basename(file_path)))
    }
  }
  
  verification_results$success <- (verification_results$corrupted == 0 && verification_results$missing == 0)
  
  if (verbose) {
    cat(sprintf("\n=== INTEGRITY VERIFICATION SUMMARY ===\n"))
    cat(sprintf("Total files: %d\n", verification_results$total_files))
    cat(sprintf("Verified: %d\n", verification_results$verified))
    cat(sprintf("Corrupted: %d\n", verification_results$corrupted))
    cat(sprintf("Missing: %d\n", verification_results$missing))
    cat(sprintf("Overall status: %s\n", ifelse(verification_results$success, "PASS", "FAIL")))
  }
  
  return(verification_results)
}

#' Manage Backup Files
#' 
#' Manages backup files with rotation and cleanup policies
#' 
#' @param file_pattern Pattern to match backup files (default: "*_backup_*.csv")
#' @param max_backups Maximum number of backups to keep per file (default: 5)
#' @param max_age_days Maximum age of backups in days (default: 30)
#' @param verbose Whether to print management actions (default: TRUE)
#' @return List with backup management results
manage_backups <- function(file_pattern = "*_backup_*", max_backups = 5, max_age_days = 30, verbose = TRUE) {
  
  if (verbose) cat("\n=== MANAGING BACKUP FILES ===\n")
  
  # Find all backup files
  backup_files <- list.files(pattern = glob2rx(file_pattern), full.names = TRUE, recursive = TRUE)
  
  if (length(backup_files) == 0) {
    if (verbose) cat("No backup files found\n")
    return(list(success = TRUE, removed = 0, kept = 0))
  }
  
  # Group backups by base filename
  backup_groups <- list()
  
  for (backup_file in backup_files) {
    # Extract base filename (remove backup timestamp)
    base_name <- gsub("_backup_[0-9]{8}_[0-9]{6}", "", tools::file_path_sans_ext(backup_file))
    base_name <- paste0(base_name, ".", tools::file_ext(backup_file))
    
    if (is.null(backup_groups[[base_name]])) {
      backup_groups[[base_name]] <- character(0)
    }
    backup_groups[[base_name]] <- c(backup_groups[[base_name]], backup_file)
  }
  
  removed_count <- 0
  kept_count <- 0
  
  # Process each group
  for (base_name in names(backup_groups)) {
    group_files <- backup_groups[[base_name]]
    
    # Sort by modification time (newest first)
    file_info <- file.info(group_files)
    group_files <- group_files[order(file_info$mtime, decreasing = TRUE)]
    
    if (verbose) cat(sprintf("Processing %d backups for %s\n", length(group_files), basename(base_name)))
    
    for (i in seq_along(group_files)) {
      backup_file <- group_files[i]
      file_age_days <- as.numeric(difftime(Sys.time(), file.info(backup_file)$mtime, units = "days"))
      
      # Remove if too old or exceeds max count
      should_remove <- (i > max_backups) || (file_age_days > max_age_days)
      
      if (should_remove) {
        tryCatch({
          file.remove(backup_file)
          removed_count <- removed_count + 1
          if (verbose) {
            reason <- ifelse(i > max_backups, "excess count", "too old")
            cat(sprintf("  ✗ Removed: %s (%s)\n", basename(backup_file), reason))
          }
        }, error = function(e) {
          if (verbose) cat(sprintf("  ! Could not remove %s: %s\n", basename(backup_file), e$message))
        })
      } else {
        kept_count <- kept_count + 1
        if (verbose) cat(sprintf("  ✓ Kept: %s (%.1f days old)\n", basename(backup_file), file_age_days))
      }
    }
  }
  
  if (verbose) {
    cat(sprintf("\n=== BACKUP MANAGEMENT SUMMARY ===\n"))
    cat(sprintf("Files removed: %d\n", removed_count))
    cat(sprintf("Files kept: %d\n", kept_count))
  }
  
  return(list(success = TRUE, removed = removed_count, kept = kept_count))
}

#' Automated Recovery Workflow
#' 
#' Attempts to recover corrupted data using available backups
#' 
#' @param corrupted_files Vector of corrupted file paths
#' @param verbose Whether to print recovery progress (default: TRUE)
#' @return List with recovery results
automated_recovery <- function(corrupted_files, verbose = TRUE) {
  
  if (verbose) cat("\n=== AUTOMATED DATA RECOVERY ===\n")
  
  recovery_results <- list(
    attempted = length(corrupted_files),
    recovered = 0,
    failed = 0,
    details = list()
  )
  
  for (corrupted_file in corrupted_files) {
    if (verbose) cat(sprintf("Attempting recovery for: %s\n", basename(corrupted_file)))
    
    # Look for backup files
    file_base <- tools::file_path_sans_ext(corrupted_file)
    file_ext <- tools::file_ext(corrupted_file)
    backup_pattern <- sprintf("%s_backup_*\\.%s$", basename(file_base), file_ext)
    backup_dir <- dirname(corrupted_file)
    
    backup_files <- list.files(backup_dir, pattern = backup_pattern, full.names = TRUE)
    
    if (length(backup_files) == 0) {
      recovery_results$failed <- recovery_results$failed + 1
      recovery_results$details[[corrupted_file]] <- list(status = "no_backups")
      if (verbose) cat(sprintf("  ✗ No backups found\n"))
      next
    }
    
    # Sort backups by modification time (newest first)
    backup_files <- backup_files[order(file.mtime(backup_files), decreasing = TRUE)]
    
    recovery_success <- FALSE
    
    for (backup_file in backup_files) {
      if (verbose) cat(sprintf("  Trying backup: %s\n", basename(backup_file)))
      
      # Test backup integrity
      backup_test <- secure_data_load(backup_file, 
                                     paste("Recovery test for", basename(corrupted_file)),
                                     verify_integrity = TRUE,
                                     attempt_recovery = FALSE,
                                     verbose = FALSE)
      
      if (backup_test$success) {
        # Backup is good, restore it
        tryCatch({
          file.copy(backup_file, corrupted_file, overwrite = TRUE)
          recovery_results$recovered <- recovery_results$recovered + 1
          recovery_results$details[[corrupted_file]] <- list(
            status = "recovered",
            backup_used = backup_file,
            backup_date = file.info(backup_file)$mtime
          )
          recovery_success <- TRUE
          if (verbose) cat(sprintf("  ✓ Successfully recovered from %s\n", basename(backup_file)))
          break
        }, error = function(e) {
          if (verbose) cat(sprintf("  ! Recovery failed: %s\n", e$message))
        })
      } else {
        if (verbose) cat(sprintf("  ✗ Backup also corrupted\n"))
      }
    }
    
    if (!recovery_success) {
      recovery_results$failed <- recovery_results$failed + 1
      recovery_results$details[[corrupted_file]] <- list(status = "recovery_failed")
      if (verbose) cat(sprintf("  ✗ All recovery attempts failed\n"))
    }
  }
  
  if (verbose) {
    cat(sprintf("\n=== RECOVERY SUMMARY ===\n"))
    cat(sprintf("Files attempted: %d\n", recovery_results$attempted))
    cat(sprintf("Successfully recovered: %d\n", recovery_results$recovered))
    cat(sprintf("Failed to recover: %d\n", recovery_results$failed))
  }
  
  return(recovery_results)
}

#' Generate Integrity Report
#' 
#' Generates a comprehensive integrity and backup status report
#' 
#' @param output_file Path for the report file (default: "data_integrity_report.txt")
#' @param verbose Whether to print report to console (default: TRUE)
#' @return Path to the generated report file
generate_integrity_report <- function(output_file = "data_integrity_report.txt", verbose = TRUE) {
  
  report_lines <- character(0)
  
  # Header
  report_lines <- c(report_lines, 
                   "===============================================",
                   "DATA INTEGRITY AND BACKUP STATUS REPORT",
                   "===============================================",
                   sprintf("Generated: %s", Sys.time()),
                   sprintf("System: %s", Sys.info()["sysname"]),
                   sprintf("Working Directory: %s", getwd()),
                   "")
  
  # Check for manifest
  manifest_exists <- file.exists("data_integrity_manifest.json")
  report_lines <- c(report_lines,
                   "--- MANIFEST STATUS ---",
                   sprintf("Manifest file exists: %s", ifelse(manifest_exists, "YES", "NO")))
  
  if (manifest_exists) {
    verification <- verify_data_integrity(verbose = FALSE)
    report_lines <- c(report_lines,
                     sprintf("Total tracked files: %d", verification$total_files),
                     sprintf("Verified files: %d", verification$verified),
                     sprintf("Corrupted files: %d", verification$corrupted),
                     sprintf("Missing files: %d", verification$missing),
                     sprintf("Overall integrity: %s", ifelse(verification$success, "PASS", "FAIL")))
    
    if (length(verification$errors) > 0) {
      report_lines <- c(report_lines, "", "--- INTEGRITY ISSUES ---")
      for (error in verification$errors) {
        report_lines <- c(report_lines, sprintf("  %s", error))
      }
    }
  }
  
  # Backup status
  backup_files <- list.files(pattern = glob2rx("*_backup_*"), full.names = TRUE, recursive = TRUE)
  report_lines <- c(report_lines,
                   "",
                   "--- BACKUP STATUS ---",
                   sprintf("Total backup files: %d", length(backup_files)))
  
  if (length(backup_files) > 0) {
    backup_info <- file.info(backup_files)
    oldest_backup <- min(backup_info$mtime)
    newest_backup <- max(backup_info$mtime)
    total_backup_size <- sum(backup_info$size) / 1024^2  # MB
    
    report_lines <- c(report_lines,
                     sprintf("Oldest backup: %s", oldest_backup),
                     sprintf("Newest backup: %s", newest_backup),
                     sprintf("Total backup size: %.2f MB", total_backup_size))
  }
  
  # Storage recommendations
  report_lines <- c(report_lines,
                   "",
                   "--- RECOMMENDATIONS ---")
  
  if (!manifest_exists) {
    report_lines <- c(report_lines, "  • Create integrity manifest for data tracking")
  }
  
  if (manifest_exists && verification$corrupted > 0) {
    report_lines <- c(report_lines, "  • Run automated recovery for corrupted files")
  }
  
  if (length(backup_files) > 20) {
    report_lines <- c(report_lines, "  • Consider backup cleanup to free disk space")
  }
  
  if (length(backup_files) == 0) {
    report_lines <- c(report_lines, "  • Enable backup creation for critical data")
  }
  
  report_lines <- c(report_lines,
                   "",
                   "===============================================",
                   "END OF REPORT",
                   "===============================================")
  
  # Write report to file
  writeLines(report_lines, output_file)
  
  if (verbose) {
    cat("\n")
    for (line in report_lines) {
      cat(line, "\n")
    }
  }
  
  return(output_file)
}

cat("\nData integrity and backup management module loaded successfully!\n")
cat("Advanced features available:\n")
cat("  - create_integrity_manifest(): Track data files with checksums\n")
cat("  - verify_data_integrity(): Verify against manifest\n")
cat("  - manage_backups(): Automated backup rotation and cleanup\n")
cat("  - automated_recovery(): Recover corrupted data from backups\n")
cat("  - generate_integrity_report(): Comprehensive status reporting\n")
cat("\nIntegrity features:\n")
cat("  ✓ SHA-256 checksum tracking\n")
cat("  ✓ Automated corruption detection\n")
cat("  ✓ Backup rotation policies\n")
cat("  ✓ Automated recovery workflows\n")
cat("  ✓ Comprehensive status reporting\n")