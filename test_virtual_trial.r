# Test script to identify the error in virtual clinical trial

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
})

# Set seed
set.seed(123)

cat("Testing virtual clinical trial loading...\n")

# Try to source each component separately
cat("1. Loading file utilities...\n")
tryCatch({
  source("ml_file_utils.r")
  cat("✓ File utilities loaded\n")
}, error = function(e) {
  cat("ERROR in file utilities:", e$message, "\n")
})

cat("2. Loading synthetic population generator...\n")
tryCatch({
  source("synthetic_population_generator.r")
  cat("✓ Population generator loaded\n")
}, error = function(e) {
  cat("ERROR in population generator:", e$message, "\n")
})

cat("3. Loading comprehensive model...\n")
tryCatch({
  source("ibrutinib_comprehensive_model_with_interactions.r")
  cat("✓ Comprehensive model loaded\n")
}, error = function(e) {
  cat("ERROR in comprehensive model:", e$message, "\n")
})

cat("4. Testing virtual clinical trial functions...\n")

# Test individual functions from virtual clinical trial
cat("Testing CTCAE classification function...\n")
tryCatch({
  # Define the CTCAE function inline to test
  classify_bleeding_severity_ctcae <- function(bleeding_events, risk_factors) {
    n_events <- length(bleeding_events)
    severity_grades <- rep(0, n_events)
    
    for (i in 1:n_events) {
      if (bleeding_events[i] > 0) {
        age_factor <- ifelse(risk_factors$age[i] >= 75, 1.5, 1.0)
        anticoag_factor <- ifelse(risk_factors$anticoagulants[i], 2.0, 1.0)
        comorbidity_factor <- ifelse(risk_factors$high_comorbidity_score[i], 1.3, 1.0)
        platelet_factor <- ifelse(risk_factors$platelet_count[i] < 100, 1.8, 1.0)
        
        risk_multiplier <- age_factor * anticoag_factor * comorbidity_factor * platelet_factor
        
        if (risk_multiplier > 2.0) {
          adjusted_probs <- c(0.25, 0.30, 0.30, 0.13, 0.02)
        } else if (risk_multiplier > 1.5) {
          adjusted_probs <- c(0.30, 0.32, 0.25, 0.11, 0.02)
        } else if (risk_multiplier > 1.2) {
          adjusted_probs <- c(0.35, 0.33, 0.22, 0.08, 0.02)
        } else {
          adjusted_probs <- c(0.40, 0.35, 0.20, 0.045, 0.005)
        }
        
        severity_grades[i] <- sample(1:5, 1, prob = adjusted_probs)
      }
    }
    
    return(severity_grades)
  }
  
  # Test with sample data
  test_bleeding_events <- c(1, 0, 2, 1, 0)
  test_risk_factors <- data.frame(
    age = c(70, 65, 80, 75, 60),
    anticoagulants = c(TRUE, FALSE, TRUE, FALSE, FALSE),
    high_comorbidity_score = c(FALSE, TRUE, TRUE, FALSE, FALSE),
    platelet_count = c(150, 200, 80, 120, 180)
  )
  
  result <- classify_bleeding_severity_ctcae(test_bleeding_events, test_risk_factors)
  cat("✓ CTCAE classification test passed, result:", paste(result, collapse = ", "), "\n")
  
}, error = function(e) {
  cat("ERROR in CTCAE function:", e$message, "\n")
})

cat("5. Testing dose modification function...\n")
tryCatch({
  # Test dose modification function
  test_population <- data.frame(
    patient_id = paste0("PT", 1:5),
    treatment_arm = c("Ibrutinib_420mg", "Control", "Ibrutinib_420mg", "Control", "Ibrutinib_420mg"),
    stringsAsFactors = FALSE
  )
  
  test_bleeding_outcomes <- data.frame(
    major_bleeding_occurred = c(TRUE, FALSE, FALSE, FALSE, TRUE),
    fatal_bleeding = c(FALSE, FALSE, FALSE, FALSE, FALSE),
    intracranial_hemorrhage = c(FALSE, FALSE, FALSE, FALSE, FALSE),
    clinically_relevant_non_major_events = c(0, 0, 1, 0, 0),
    bleeding_events = c(1, 0, 1, 0, 2)
  )
  
  # Simple dose modification logic
  dose_modifications <- data.frame(
    patient_id = test_population$patient_id,
    initial_dose = ifelse(test_population$treatment_arm == "Ibrutinib_420mg", 420, 0),
    current_dose = ifelse(test_population$treatment_arm == "Ibrutinib_420mg", 420, 0),
    dose_reductions = 0,
    dose_interruptions = 0,
    total_interruption_days = 0,
    permanent_discontinuation = FALSE,
    discontinuation_reason = "None",
    stringsAsFactors = FALSE
  )
  
  cat("✓ Dose modification test passed, created", nrow(dose_modifications), "records\n")
  
}, error = function(e) {
  cat("ERROR in dose modification function:", e$message, "\n")
})

cat("Testing completed.\n")
