# =============================================================================
# STORAGE SYSTEM TESTING SUITE
# =============================================================================
# This script comprehensively tests the secure data storage system including
# error scenarios, edge cases, and recovery procedures.
#
# Test Categories:
# - Basic functionality tests
# - Error handling tests
# - Data integrity tests
# - Recovery workflow tests
# - Performance tests
#
# Author: Storage System Test Suite
# Date: 2024
# =============================================================================

# Load all storage system modules
source("secure_data_storage.r")
source("data_integrity_manager.r")
source("data_recovery_validation.r")

# Load required libraries
if (!require(testthat, quietly = TRUE)) {
  install.packages("testthat")
  library(testthat)
}

cat("\n=============================================================================\n")
cat("COMPREHENSIVE STORAGE SYSTEM TEST SUITE\n")
cat("=============================================================================\n")

# =============================================================================
# TEST DATA GENERATION
# =============================================================================

#' Generate Test Data
#' 
#' Creates various types of test data for comprehensive testing
generate_test_data <- function() {
  
  # Valid inhibition data
  valid_inhibition_data <- data.frame(
    agonist_name = rep(c("ADP", "Collagen", "Thrombin"), each = 6),
    dose_mg = rep(c(0, 140, 280, 420, 560, 840), 3),
    percent_inhibition = c(
      c(0, 15, 35, 55, 75, 85),  # ADP
      c(0, 10, 25, 45, 65, 80),  # Collagen
      c(0, 20, 40, 60, 80, 90)   # Thrombin
    ),
    peak_agg = runif(18, 50, 100),
    control_agg = runif(18, 80, 120)
  )
  
  # Invalid data (missing columns)
  invalid_data_missing_cols <- data.frame(
    agonist_name = c("ADP", "Collagen"),
    dose_mg = c(140, 280)
    # Missing required columns
  )
  
  # Invalid data (wrong ranges)
  invalid_data_ranges <- data.frame(
    agonist_name = c("ADP", "Collagen"),
    dose_mg = c(-50, 2000),  # Invalid dose ranges
    percent_inhibition = c(-10, 150),  # Invalid inhibition ranges
    peak_agg = c(50, 80),
    control_agg = c(90, 110)
  )
  
  # Data with missing values
  data_with_na <- valid_inhibition_data
  data_with_na$percent_inhibition[c(1, 5, 10)] <- NA
  data_with_na$peak_agg[c(2, 8)] <- NA
  
  # Large dataset for performance testing
  large_data <- do.call(rbind, replicate(100, valid_inhibition_data, simplify = FALSE))
  large_data$replicate <- rep(1:100, each = nrow(valid_inhibition_data))
  
  return(list(
    valid = valid_inhibition_data,
    invalid_missing_cols = invalid_data_missing_cols,
    invalid_ranges = invalid_data_ranges,
    with_na = data_with_na,
    large = large_data
  ))
}

# Generate test datasets
test_data <- generate_test_data()

# =============================================================================
# TEST SUITE 1: BASIC FUNCTIONALITY TESTS
# =============================================================================

cat("\n=== TEST SUITE 1: BASIC FUNCTIONALITY ===\n")

test_results <- list()

# Test 1.1: Valid data storage (CSV)
cat("\nTest 1.1: Valid data storage (CSV)\n")
test_1_1 <- secure_data_save(
  data = test_data$valid,
  file_path = "test_valid_data.csv",
  data_name = "Test Valid Inhibition Data",
  format = "csv",
  required_columns = c("agonist_name", "dose_mg", "percent_inhibition"),
  create_backup = TRUE,
  verify_integrity = TRUE,
  verbose = TRUE
)
test_results$test_1_1 <- test_1_1$success
cat(sprintf("Result: %s\n", ifelse(test_1_1$success, "PASS", "FAIL")))

# Test 1.2: Valid data storage (RDS)
cat("\nTest 1.2: Valid data storage (RDS)\n")
test_1_2 <- secure_data_save(
  data = test_data$valid,
  file_path = "test_valid_data.rds",
  data_name = "Test Valid Inhibition Data (RDS)",
  format = "rds",
  required_columns = c("agonist_name", "dose_mg", "percent_inhibition"),
  create_backup = TRUE,
  verify_integrity = TRUE,
  verbose = TRUE
)
test_results$test_1_2 <- test_1_2$success
cat(sprintf("Result: %s\n", ifelse(test_1_2$success, "PASS", "FAIL")))

# Test 1.3: Data loading
cat("\nTest 1.3: Data loading\n")
test_1_3 <- secure_data_load(
  file_path = "test_valid_data.csv",
  data_name = "Test Data Load",
  verify_integrity = TRUE,
  verbose = TRUE
)
test_results$test_1_3 <- test_1_3$success
cat(sprintf("Result: %s\n", ifelse(test_1_3$success, "PASS", "FAIL")))

# =============================================================================
# TEST SUITE 2: ERROR HANDLING TESTS
# =============================================================================

cat("\n=== TEST SUITE 2: ERROR HANDLING ===\n")

# Test 2.1: Invalid data (missing columns)
cat("\nTest 2.1: Invalid data (missing columns)\n")
test_2_1 <- secure_data_save(
  data = test_data$invalid_missing_cols,
  file_path = "test_invalid_missing.csv",
  data_name = "Test Invalid Data (Missing Columns)",
  format = "csv",
  required_columns = c("agonist_name", "dose_mg", "percent_inhibition"),
  create_backup = FALSE,
  verify_integrity = TRUE,
  verbose = TRUE
)
test_results$test_2_1 <- !test_2_1$success  # Should fail
cat(sprintf("Result: %s (Expected to fail)\n", ifelse(!test_2_1$success, "PASS", "FAIL")))

# Test 2.2: Invalid file path
cat("\nTest 2.2: Invalid file path\n")
test_2_2 <- secure_data_save(
  data = test_data$valid,
  file_path = "/invalid/path/that/does/not/exist/test.csv",
  data_name = "Test Invalid Path",
  format = "csv",
  required_columns = c("agonist_name", "dose_mg", "percent_inhibition"),
  create_backup = FALSE,
  verify_integrity = TRUE,
  verbose = TRUE
)
test_results$test_2_2 <- !test_2_2$success  # Should fail
cat(sprintf("Result: %s (Expected to fail)\n", ifelse(!test_2_2$success, "PASS", "FAIL")))

# Test 2.3: Loading non-existent file
cat("\nTest 2.3: Loading non-existent file\n")
test_2_3 <- secure_data_load(
  file_path = "non_existent_file.csv",
  data_name = "Test Non-existent File",
  verify_integrity = FALSE,
  verbose = TRUE
)
test_results$test_2_3 <- !test_2_3$success  # Should fail
cat(sprintf("Result: %s (Expected to fail)\n", ifelse(!test_2_3$success, "PASS", "FAIL")))

# =============================================================================
# TEST SUITE 3: DATA INTEGRITY TESTS
# =============================================================================

cat("\n=== TEST SUITE 3: DATA INTEGRITY ===\n")

# Test 3.1: Create integrity manifest
cat("\nTest 3.1: Create integrity manifest\n")
test_files <- c("test_valid_data.csv", "test_valid_data.rds")
existing_files <- test_files[file.exists(test_files)]

if (length(existing_files) > 0) {
  test_3_1 <- create_integrity_manifest(
    data_files = existing_files,
    manifest_path = "test_manifest.json",
    verbose = TRUE
  )
  test_results$test_3_1 <- test_3_1$success
  cat(sprintf("Result: %s\n", ifelse(test_3_1$success, "PASS", "FAIL")))
} else {
  test_results$test_3_1 <- FALSE
  cat("Result: SKIP (No test files available)\n")
}

# Test 3.2: Verify data integrity
cat("\nTest 3.2: Verify data integrity\n")
if (file.exists("test_manifest.json")) {
  test_3_2 <- verify_data_integrity(
    manifest_path = "test_manifest.json",
    verbose = TRUE
  )
  test_results$test_3_2 <- test_3_2$success
  cat(sprintf("Result: %s\n", ifelse(test_3_2$success, "PASS", "FAIL")))
} else {
  test_results$test_3_2 <- FALSE
  cat("Result: SKIP (No manifest available)\n")
}

# Test 3.3: Data consistency check
cat("\nTest 3.3: Data consistency check\n")
if (file.exists("test_valid_data.csv") && file.exists("test_valid_data.rds")) {
  test_3_3 <- data_consistency_check(
    csv_file = "test_valid_data.csv",
    rds_file = "test_valid_data.rds",
    verbose = TRUE
  )
  test_results$test_3_3 <- test_3_3$content_match
  cat(sprintf("Result: %s\n", ifelse(test_3_3$content_match, "PASS", "FAIL")))
} else {
  test_results$test_3_3 <- FALSE
  cat("Result: SKIP (Test files not available)\n")
}

# =============================================================================
# TEST SUITE 4: DATA VALIDATION TESTS
# =============================================================================

cat("\n=== TEST SUITE 4: DATA VALIDATION ===\n")

# Test 4.1: Valid data validation
cat("\nTest 4.1: Valid data validation\n")
test_4_1 <- comprehensive_data_validation(
  data = test_data$valid,
  data_type = "inhibition",
  verbose = TRUE
)
test_results$test_4_1 <- (test_4_1$overall_status == "PASS")
cat(sprintf("Result: %s\n", ifelse(test_4_1$overall_status == "PASS", "PASS", "FAIL")))

# Test 4.2: Invalid data validation (ranges)
cat("\nTest 4.2: Invalid data validation (ranges)\n")
test_4_2 <- comprehensive_data_validation(
  data = test_data$invalid_ranges,
  data_type = "inhibition",
  verbose = TRUE
)
test_results$test_4_2 <- (test_4_2$overall_status == "FAIL")  # Should fail
cat(sprintf("Result: %s (Expected to fail)\n", ifelse(test_4_2$overall_status == "FAIL", "PASS", "FAIL")))

# Test 4.3: Data with missing values
cat("\nTest 4.3: Data with missing values\n")
test_4_3 <- comprehensive_data_validation(
  data = test_data$with_na,
  data_type = "inhibition",
  verbose = TRUE
)
test_results$test_4_3 <- (test_4_3$data_quality_score > 70)  # Should still pass with warnings
cat(sprintf("Result: %s (Quality score: %.1f%%)\n", 
           ifelse(test_4_3$data_quality_score > 70, "PASS", "FAIL"), 
           test_4_3$data_quality_score))

# =============================================================================
# TEST SUITE 5: RECOVERY TESTS
# =============================================================================

cat("\n=== TEST SUITE 5: RECOVERY TESTS ===\n")

# Test 5.1: Backup management
cat("\nTest 5.1: Backup management\n")
test_5_1 <- manage_backups(
  file_pattern = "*test*_backup_*",
  max_backups = 3,
  max_age_days = 1,
  verbose = TRUE
)
test_results$test_5_1 <- test_5_1$success
cat(sprintf("Result: %s\n", ifelse(test_5_1$success, "PASS", "FAIL")))

# Test 5.2: Simulate file corruption and recovery
cat("\nTest 5.2: File corruption and recovery\n")
if (file.exists("test_valid_data.csv")) {
  # Create a backup first
  backup_file <- sprintf("test_valid_data_backup_%s.csv", format(Sys.time(), "%Y%m%d_%H%M%S"))
  file.copy("test_valid_data.csv", backup_file)
  
  # Corrupt the original file
  writeLines("corrupted data", "test_valid_data.csv")
  
  # Attempt recovery
  test_5_2 <- emergency_data_recovery(
    file_path = "test_valid_data.csv",
    recovery_strategy = "backup",
    verbose = TRUE
  )
  test_results$test_5_2 <- test_5_2$success
  cat(sprintf("Result: %s\n", ifelse(test_5_2$success, "PASS", "FAIL")))
} else {
  test_results$test_5_2 <- FALSE
  cat("Result: SKIP (No test file available)\n")
}

# =============================================================================
# TEST SUITE 6: PERFORMANCE TESTS
# =============================================================================

cat("\n=== TEST SUITE 6: PERFORMANCE TESTS ===\n")

# Test 6.1: Large dataset storage
cat("\nTest 6.1: Large dataset storage performance\n")
start_time <- Sys.time()
test_6_1 <- secure_data_save(
  data = test_data$large,
  file_path = "test_large_data.csv",
  data_name = "Test Large Dataset",
  format = "csv",
  required_columns = c("agonist_name", "dose_mg", "percent_inhibition"),
  create_backup = FALSE,  # Skip backup for performance
  verify_integrity = TRUE,
  verbose = FALSE
)
end_time <- Sys.time()
processing_time <- as.numeric(difftime(end_time, start_time, units = "secs"))

test_results$test_6_1 <- test_6_1$success && (processing_time < 30)  # Should complete in <30 seconds
cat(sprintf("Result: %s (Time: %.2f seconds)\n", 
           ifelse(test_results$test_6_1, "PASS", "FAIL"), processing_time))

# =============================================================================
# TEST RESULTS SUMMARY
# =============================================================================

cat("\n=============================================================================\n")
cat("TEST RESULTS SUMMARY\n")
cat("=============================================================================\n")

total_tests <- length(test_results)
passed_tests <- sum(unlist(test_results), na.rm = TRUE)
failed_tests <- total_tests - passed_tests
pass_rate <- (passed_tests / total_tests) * 100

cat(sprintf("Total tests: %d\n", total_tests))
cat(sprintf("Passed: %d\n", passed_tests))
cat(sprintf("Failed: %d\n", failed_tests))
cat(sprintf("Pass rate: %.1f%%\n", pass_rate))

cat("\nDetailed Results:\n")
for (test_name in names(test_results)) {
  result <- test_results[[test_name]]
  status <- ifelse(is.na(result), "SKIP", ifelse(result, "PASS", "FAIL"))
  cat(sprintf("  %s: %s\n", test_name, status))
}

# Overall assessment
if (pass_rate >= 90) {
  overall_status <- "EXCELLENT"
} else if (pass_rate >= 80) {
  overall_status <- "GOOD"
} else if (pass_rate >= 70) {
  overall_status <- "ACCEPTABLE"
} else {
  overall_status <- "NEEDS IMPROVEMENT"
}

cat(sprintf("\nOverall Assessment: %s\n", overall_status))

# =============================================================================
# CLEANUP TEST FILES
# =============================================================================

cat("\n=== CLEANING UP TEST FILES ===\n")

test_files_to_remove <- c(
  "test_valid_data.csv",
  "test_valid_data.rds",
  "test_large_data.csv",
  "test_manifest.json",
  list.files(pattern = "test.*_backup_.*\\.(csv|rds)$", full.names = TRUE)
)

for (file in test_files_to_remove) {
  if (file.exists(file)) {
    tryCatch({
      file.remove(file)
      cat(sprintf("✓ Removed: %s\n", basename(file)))
    }, error = function(e) {
      cat(sprintf("✗ Could not remove: %s\n", basename(file)))
    })
  }
}

cat("\n=============================================================================\n")
cat("STORAGE SYSTEM TESTING COMPLETE\n")
cat("=============================================================================\n")

# Return test results for programmatic use
invisible(list(
  total_tests = total_tests,
  passed_tests = passed_tests,
  failed_tests = failed_tests,
  pass_rate = pass_rate,
  overall_status = overall_status,
  detailed_results = test_results
))