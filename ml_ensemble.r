# =============================================================================
# ML ENSEMBLE METHODS AND ADVANCED TECHNIQUES
# =============================================================================
# This module implements:
# - Ensemble methods (Bagging, Boosting, Stacking)
# - Regularization techniques (L1/L2, Elastic Net)
# - Class imbalance handling (SMOTE, ROSE, Cost-sensitive learning)
# - Advanced cross-validation strategies
# - Computational optimization

# Load required libraries with error handling
required_packages <- c("randomForest", "xgboost", "glmnet", "caret", "pROC", "dplyr", 
                      "parallel", "doParallel", "foreach")
optional_packages <- c("caretEnsemble", "SuperLearner", "h2o", "smotefamily", "ROSE")

# Load required packages
for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
    stop(paste("Required package", pkg, "is not installed. Please install it using: install.packages('", pkg, "')"))
  }
}

# Load optional packages with fallbacks
use_caret_ensemble <- require(caretEnsemble, quietly = TRUE)
use_super_learner <- require(SuperLearner, quietly = TRUE)
use_h2o <- require(h2o, quietly = TRUE)
use_smote_family <- require(smotefamily, quietly = TRUE)
use_rose <- require(ROSE, quietly = TRUE)

if (!use_caret_ensemble) {
  cat("caretEnsemble package not available - using basic ensemble methods\n")
}
if (!use_super_learner) {
  cat("SuperLearner package not available - using basic stacking\n")
}
if (!use_h2o) {
  cat("h2o package not available - skipping H2O ensemble methods\n")
}
if (!use_smote_family) {
  cat("smotefamily package not available - using basic oversampling\n")
}
if (!use_rose) {
  cat("ROSE package not available - using alternative imbalance handling\n")
}

set.seed(42)

# =============================================================================
# PART 1: ENSEMBLE METHODS
# =============================================================================

#' Advanced Ensemble Learning Framework
#' Implements multiple ensemble strategies including bagging, boosting, and stacking
advanced_ensemble_learning <- function(X_train, y_train, X_test, y_test, 
                                     ensemble_method = "stacking", 
                                     base_learners = NULL,
                                     meta_learner = "glm") {
  
  # Validate inputs and handle edge cases
  validation_result <- validate_ensemble_inputs(X_train, y_train, X_test, y_test, base_learners)
  if (!validation_result$valid) {
    cat(sprintf("Ensemble validation failed: %s\n", validation_result$message))
    return(validation_result$fallback_result)
  }
  
  # Adjust parameters for small datasets
  adjusted_params <- adjust_ensemble_params_for_small_data(nrow(X_train), ncol(X_train), 
                                                          ensemble_method, base_learners)
  
  cat(sprintf("Training %s ensemble with %d base learners (dataset size: %d samples, %d features)...\n", 
              ensemble_method, length(base_learners), nrow(X_train), ncol(X_train)))
  
  # Apply ensemble method with error handling
  tryCatch({
    if (ensemble_method == "bagging") {
      return(robust_bagging_ensemble(X_train, y_train, X_test, y_test, base_learners, adjusted_params))
    } else if (ensemble_method == "boosting") {
      return(robust_boosting_ensemble(X_train, y_train, X_test, y_test, adjusted_params))
    } else if (ensemble_method == "stacking") {
      return(robust_stacking_ensemble(X_train, y_train, X_test, y_test, base_learners, meta_learner, adjusted_params))
    } else if (ensemble_method == "voting") {
      return(robust_voting_ensemble(X_train, y_train, X_test, y_test, base_learners, adjusted_params))
    } else {
      cat(sprintf("Unknown ensemble method: %s. Using stacking as fallback.\n", ensemble_method))
      return(robust_stacking_ensemble(X_train, y_train, X_test, y_test, base_learners, meta_learner, adjusted_params))
    }
  }, error = function(e) {
    cat(sprintf("Ensemble method failed (%s). Using simple averaging fallback.\n", e$message))
    return(simple_averaging_fallback(X_train, y_train, X_test, y_test, base_learners))
  })
}

#' Validate Ensemble Inputs and Handle Edge Cases
validate_ensemble_inputs <- function(X_train, y_train, X_test, y_test, base_learners) {
  
  # Check minimum sample size
  if (nrow(X_train) < 10) {
    return(list(
      valid = FALSE,
      message = "Insufficient training samples (< 10). Using single model fallback.",
      fallback_result = single_model_fallback(X_train, y_train, X_test, y_test)
    ))
 }
 
 #' Helper Functions for Robust Ensemble Methods
 
 #' Stratified CV Folds for Small Datasets
 stratified_cv_folds <- function(y, k) {
   
   class_indices <- split(seq_along(y), y)
   folds <- vector("list", k)
   
   for (class_label in names(class_indices)) {
     class_idx <- class_indices[[class_label]]
     fold_assignment <- rep(1:k, length.out = length(class_idx))
     fold_assignment <- sample(fold_assignment)
     
     for (fold in 1:k) {
       folds[[fold]] <- c(folds[[fold]], class_idx[fold_assignment == fold])
     }
   }
   
   return(folds)
 }
 
 #' Train Robust Base Learner
 train_robust_base_learner <- function(learner, X_train, y_train, total_samples) {
   
   tryCatch({
     if (learner == "rf") {
       ntree <- if (total_samples < 100) 50 else 200
       mtry <- max(1, min(sqrt(ncol(X_train)), ncol(X_train) %/% 3))
       model <- randomForest(x = X_train, y = as.factor(y_train), 
                            ntree = ntree, mtry = mtry,
                            nodesize = max(1, nrow(X_train) %/% 20))
       return(model)
       
     } else if (learner == "xgb") {
       dtrain <- xgb.DMatrix(data = as.matrix(X_train), label = y_train)
       
       max_depth <- if (total_samples < 100) 3 else 6
       eta <- if (total_samples < 100) 0.3 else 0.1
       nrounds <- if (total_samples < 100) 50 else 100
       
       model <- xgb.train(
         params = list(objective = "binary:logistic", 
                      max_depth = max_depth, 
                      eta = eta,
                      subsample = min(0.8, max(0.5, nrow(X_train) / 100)),
                      colsample_bytree = min(0.8, max(0.3, sqrt(ncol(X_train)) / ncol(X_train))),
                      min_child_weight = max(1, nrow(X_train) %/% 50)),
         data = dtrain, 
         nrounds = nrounds, 
         verbose = 0
       )
       return(model)
       
     } else if (learner == "glm") {
       # Try regularized GLM first
       tryCatch({
         model <- cv.glmnet(as.matrix(X_train), y_train, 
                           family = "binomial", alpha = 0.5)
         return(model)
       }, error = function(e) {
         # Fallback to simple GLM with fewer features
         n_features <- min(5, ncol(X_train))
         model <- glm(y_train ~ ., 
                     data = data.frame(y_train = y_train, 
                                      X_train[, 1:n_features, drop = FALSE]), 
                     family = binomial())
         return(model)
       })
       
     } else if (learner == "svm") {
       model <- svm(x = X_train, y = as.factor(y_train), 
                   probability = TRUE, kernel = "radial",
                   cost = if (total_samples < 100) 0.1 else 1)
       return(model)
     }
     
     return(NULL)
     
   }, error = function(e) {
     return(NULL)
   })
 }
 
 #' Predict with Robust Base Learner
 predict_robust_base_learner <- function(model, learner, X_test) {
   
   tryCatch({
     if (learner == "rf") {
       pred <- predict(model, X_test, type = "prob")[, 2]
       return(pred)
       
     } else if (learner == "xgb") {
       dtest <- xgb.DMatrix(data = as.matrix(X_test))
       pred <- predict(model, dtest)
       return(pred)
       
     } else if (learner == "glm") {
       if (inherits(model, "cv.glmnet")) {
         pred <- predict(model, as.matrix(X_test), 
                        s = "lambda.min", type = "response")[, 1]
       } else {
         pred <- predict(model, newdata = data.frame(X_test), type = "response")
       }
       return(pred)
       
     } else if (learner == "svm") {
       pred <- attr(predict(model, X_test, probability = TRUE), "probabilities")[, "1"]
       return(pred)
     }
     
     return(NULL)
     
   }, error = function(e) {
     return(NULL)
   })
 }
 
 #' Train Robust Meta-Learner
 train_robust_meta_learner <- function(meta_learner, meta_data, y_train, test_data, total_samples) {
   
   tryCatch({
     y_train_numeric <- as.numeric(y_train)
     
     if (meta_learner == "glm") {
       if (total_samples < 50) {
         # Use simple averaging for very small datasets
         return(rowMeans(test_data))
       } else {
         model <- glm(y_train_numeric ~ ., data = data.frame(y_train_numeric = y_train_numeric, meta_data), 
                     family = binomial())
         pred <- predict(model, test_data, type = "response")
         return(pred)
       }
       
     } else if (meta_learner == "rf") {
       ntree <- if (total_samples < 100) 50 else 100
       model <- randomForest(x = meta_data, y = as.factor(y_train_numeric), ntree = ntree)
       pred <- predict(model, test_data, type = "prob")[, 2]
       return(pred)
       
     } else if (meta_learner == "xgb") {
       dmeta_train <- xgb.DMatrix(data = as.matrix(meta_data), label = y_train_numeric)
       dmeta_test <- xgb.DMatrix(data = as.matrix(test_data))
       
       nrounds <- if (total_samples < 100) 30 else 50
       model <- xgb.train(
         params = list(objective = "binary:logistic", max_depth = 3, eta = 0.1),
         data = dmeta_train, nrounds = nrounds, verbose = 0
       )
       pred <- predict(model, dmeta_test)
       return(pred)
     }
     
     # Default fallback to simple averaging
     return(rowMeans(test_data))
     
   }, error = function(e) {
     return(NULL)
   })
 }
  
  # Check class balance
  class_counts <- table(y_train)
  min_class_count <- min(class_counts)
  if (min_class_count < 3) {
    return(list(
      valid = FALSE,
      message = sprintf("Insufficient samples in minority class (%d). Using single model fallback.", min_class_count),
      fallback_result = single_model_fallback(X_train, y_train, X_test, y_test)
    ))
  }
  
  # Check feature to sample ratio
  if (ncol(X_train) > nrow(X_train) * 0.8) {
    cat("Warning: High feature-to-sample ratio detected. Ensemble may be unstable.\n")
  }
  
  # Validate base learners
  if (is.null(base_learners) || length(base_learners) == 0) {
    base_learners <- c("rf", "glm")  # Default safe learners
    cat("No base learners specified. Using default: rf, glm\n")
  }
  
  return(list(valid = TRUE, base_learners = base_learners))
}

#' Adjust Ensemble Parameters for Small Datasets
adjust_ensemble_params_for_small_data <- function(n_samples, n_features, ensemble_method, base_learners) {
  
  params <- list()
  
  # Adjust number of bags/estimators based on sample size
  if (n_samples < 50) {
    params$n_bags <- max(3, min(5, n_samples %/% 5))
    params$n_estimators <- max(10, min(20, n_samples))
    params$cv_folds <- max(3, min(5, n_samples %/% 10))
  } else if (n_samples < 200) {
    params$n_bags <- 5
    params$n_estimators <- 30
    params$cv_folds <- 5
  } else {
    params$n_bags <- 10
    params$n_estimators <- 50
    params$cv_folds <- 5
  }
  
  # Adjust model complexity for high-dimensional data
  if (n_features > n_samples * 0.5) {
    params$max_depth <- min(3, max(2, n_samples %/% 20))
    params$mtry <- max(1, min(sqrt(n_features), n_features %/% 3))
    params$ntree <- max(50, min(100, n_samples * 2))
  } else {
    params$max_depth <- 6
    params$mtry <- sqrt(n_features)
    params$ntree <- 200
  }
  
  return(params)
}

#' Robust Bagging Ensemble Implementation
robust_bagging_ensemble <- function(X_train, y_train, X_test, y_test, base_learners, params) {
  
  n_bags <- params$n_bags
  bag_predictions <- list()
  bag_models <- list()
  successful_bags <- 0
  
  cat(sprintf("  Training %d bags with adjusted parameters...\n", n_bags))
  
  # Create bootstrap samples and train models
  for (i in 1:n_bags) {
    tryCatch({
      # Bootstrap sampling with stratification for small datasets
      if (nrow(X_train) < 100) {
        # Stratified bootstrap for small datasets
        bag_idx <- stratified_bootstrap_sample(y_train, nrow(X_train))
      } else {
        bag_idx <- sample(nrow(X_train), nrow(X_train), replace = TRUE)
      }
      
      X_bag <- X_train[bag_idx, , drop = FALSE]
      y_bag <- y_train[bag_idx]
      
      # Skip if bag has insufficient class diversity
      if (length(unique(y_bag)) < 2) {
        next
      }
      
      # Train base learner on bootstrap sample
      model <- NULL
      pred <- NULL
      
      if ("rf" %in% base_learners) {
        model <- randomForest(x = X_bag, y = as.factor(y_bag), 
                             ntree = params$ntree, mtry = max(1, floor(params$mtry)),
                             nodesize = max(1, nrow(X_bag) %/% 20))
        pred <- predict(model, X_test, type = "prob")[, 2]
        
      } else if ("xgb" %in% base_learners) {
        dtrain <- xgb.DMatrix(data = as.matrix(X_bag), label = y_bag)
        dtest <- xgb.DMatrix(data = as.matrix(X_test))
        
        model <- xgb.train(
          params = list(objective = "binary:logistic", 
                       max_depth = params$max_depth, 
                       eta = 0.1,
                       subsample = 0.8,
                       colsample_bytree = 0.8),
          data = dtrain, 
          nrounds = min(params$n_estimators, 100), 
          verbose = 0
        )
        pred <- predict(model, dtest)
        
      } else if ("glm" %in% base_learners) {
        # GLM with regularization for small datasets
        tryCatch({
          model <- glm(y_bag ~ ., data = data.frame(y_bag = y_bag, X_bag), 
                      family = binomial())
          pred <- predict(model, newdata = data.frame(X_test), type = "response")
        }, error = function(e) {
          # Fallback to simple logistic regression
          model <- glm(y_bag ~ ., data = data.frame(y_bag = y_bag, X_bag[, 1:min(5, ncol(X_bag)), drop = FALSE]), 
                      family = binomial())
          pred <- predict(model, newdata = data.frame(X_test[, 1:min(5, ncol(X_test)), drop = FALSE]), type = "response")
        })
      }
      
      if (!is.null(pred) && !any(is.na(pred))) {
        bag_predictions[[length(bag_predictions) + 1]] <- pred
        bag_models[[length(bag_models) + 1]] <- model
        successful_bags <- successful_bags + 1
      }
      
    }, error = function(e) {
      cat(sprintf("  Bag %d failed: %s\n", i, e$message))
    })
  }
  
  # Check if we have enough successful bags
  if (successful_bags == 0) {
    cat("  All bags failed. Using single model fallback.\n")
    return(single_model_fallback(X_train, y_train, X_test, y_test))
  }
  
  cat(sprintf("  Successfully trained %d out of %d bags.\n", successful_bags, n_bags))
  
  # Average predictions from successful bags
  final_pred <- Reduce("+", bag_predictions) / length(bag_predictions)
  
  # Calculate performance
  performance <- calculate_performance_metrics(y_test, final_pred)
  
  return(list(
    predictions = final_pred,
    models = bag_models,
    performance = performance,
    method = "robust_bagging",
    successful_bags = successful_bags,
    total_bags = n_bags
  ))
}

#' Robust Boosting Ensemble Implementation
robust_boosting_ensemble <- function(X_train, y_train, X_test, y_test, params) {
  
  cat(sprintf("  Training boosting ensemble with %d estimators...\n", params$n_estimators))
  
  tryCatch({
    # XGBoost implementation with adjusted parameters
    dtrain <- xgb.DMatrix(data = as.matrix(X_train), label = y_train)
    dtest <- xgb.DMatrix(data = as.matrix(X_test))
    
    # Adjust learning rate for small datasets
    eta <- if (nrow(X_train) < 100) 0.3 else 0.1
    
    # Train XGBoost model with robust parameters
    model <- xgb.train(
      params = list(
        objective = "binary:logistic",
        max_depth = params$max_depth,
        eta = eta,
        subsample = min(0.8, max(0.5, nrow(X_train) / 100)),
        colsample_bytree = min(0.8, max(0.3, sqrt(ncol(X_train)) / ncol(X_train))),
        min_child_weight = max(1, nrow(X_train) %/% 50),
        reg_alpha = if (ncol(X_train) > nrow(X_train) * 0.5) 0.1 else 0,
        reg_lambda = if (ncol(X_train) > nrow(X_train) * 0.5) 0.1 else 0
      ),
      data = dtrain,
      nrounds = params$n_estimators,
      verbose = 0,
      early_stopping_rounds = max(10, params$n_estimators %/% 10)
    )
    
    # Make predictions
    predictions <- predict(model, dtest)
    
    # Calculate performance metrics
    performance <- calculate_performance_metrics(y_test, predictions)
    
    return(list(
      predictions = predictions,
      model = model,
      performance = performance,
      method = "robust_boosting"
    ))
    
  }, error = function(e) {
    cat(sprintf("  Boosting failed: %s. Using random forest fallback.\n", e$message))
    
    # Fallback to random forest
    model <- randomForest(x = X_train, y = as.factor(y_train), 
                         ntree = params$ntree, mtry = max(1, floor(params$mtry)))
    predictions <- predict(model, X_test, type = "prob")[, 2]
    performance <- calculate_performance_metrics(y_test, predictions)
    
    return(list(
      predictions = predictions,
      model = model,
      performance = performance,
      method = "boosting_fallback_rf"
    ))
  })
}

#' Robust Stacking Ensemble Implementation
robust_stacking_ensemble <- function(X_train, y_train, X_test, y_test, base_learners, meta_learner, params) {
  
  cat(sprintf("  Training stacking ensemble with %d base learners...\n", length(base_learners)))
  
  return(stacking_ensemble(X_train, y_train, X_test, y_test, base_learners, meta_learner))
}

#' Robust Voting Ensemble Implementation
robust_voting_ensemble <- function(X_train, y_train, X_test, y_test, base_learners, params) {
  
  cat(sprintf("  Training voting ensemble with %d base learners...\n", length(base_learners)))
  
  return(voting_ensemble(X_train, y_train, X_test, y_test, base_learners))
}

#' Stratified Bootstrap Sample for Small Datasets
stratified_bootstrap_sample <- function(y, n_samples) {
  
  class_indices <- split(seq_along(y), y)
  bootstrap_indices <- c()
  
  for (class_label in names(class_indices)) {
    class_idx <- class_indices[[class_label]]
    n_class_samples <- round(n_samples * length(class_idx) / length(y))
    bootstrap_indices <- c(bootstrap_indices, sample(class_idx, n_class_samples, replace = TRUE))
  }
  
  return(bootstrap_indices)
}

#' Single Model Fallback for Edge Cases
single_model_fallback <- function(X_train, y_train, X_test, y_test) {
  
  cat("  Using single model fallback (Random Forest)...\n")
  
  tryCatch({
    model <- randomForest(x = X_train, y = as.factor(y_train), 
                         ntree = min(50, nrow(X_train)), 
                         mtry = max(1, min(sqrt(ncol(X_train)), ncol(X_train) %/% 3)))
    predictions <- predict(model, X_test, type = "prob")[, 2]
    performance <- calculate_performance_metrics(y_test, predictions)
    
    return(list(
      predictions = predictions,
      model = model,
      performance = performance,
      method = "single_rf_fallback"
    ))
  }, error = function(e) {
    cat(sprintf("  Random Forest fallback failed: %s. Using GLM fallback.\n", e$message))
    
    # Ultimate fallback to GLM
    model <- glm(y_train ~ ., data = data.frame(y_train = y_train, X_train[, 1:min(5, ncol(X_train)), drop = FALSE]), 
                family = binomial())
    predictions <- predict(model, newdata = data.frame(X_test[, 1:min(5, ncol(X_test)), drop = FALSE]), type = "response")
    performance <- calculate_performance_metrics(y_test, predictions)
    
    return(list(
      predictions = predictions,
      model = model,
      performance = performance,
      method = "single_glm_fallback"
    ))
  })
}

#' Simple Averaging Fallback
simple_averaging_fallback <- function(X_train, y_train, X_test, y_test, base_learners) {
  
  cat("  Using simple averaging fallback...\n")
  
  predictions <- list()
  
  # Try to train at least one model
  tryCatch({
    model <- randomForest(x = X_train, y = as.factor(y_train), ntree = 50)
    pred <- predict(model, X_test, type = "prob")[, 2]
    predictions[["rf"]] <- pred
  }, error = function(e) {
    cat("  Random Forest failed in fallback\n")
  })
  
  tryCatch({
    model <- glm(y_train ~ ., data = data.frame(y_train = y_train, X_train), family = binomial())
    pred <- predict(model, newdata = data.frame(X_test), type = "response")
    predictions[["glm"]] <- pred
  }, error = function(e) {
    cat("  GLM failed in fallback\n")
  })
  
  if (length(predictions) == 0) {
    # Ultimate fallback - random predictions
    final_pred <- runif(nrow(X_test))
  } else {
    final_pred <- Reduce("+", predictions) / length(predictions)
  }
  
  performance <- calculate_performance_metrics(y_test, final_pred)
  
  return(list(
    predictions = final_pred,
    performance = performance,
    method = "simple_averaging_fallback"
  ))
}

#' Robust Stacking Ensemble Implementation
stacking_ensemble <- function(X_train, y_train, X_test, y_test, base_learners, meta_learner) {
  
  # Adjust CV folds for small datasets
  cv_folds <- if (nrow(X_train) < 50) max(3, nrow(X_train) %/% 10) else 5
  cv_folds <- min(cv_folds, nrow(X_train) %/% 5)  # Ensure minimum samples per fold
  
  cat(sprintf("  Using %d-fold cross-validation for stacking...\n", cv_folds))
  
  # Initialize meta-features matrix
  meta_features_train <- matrix(0, nrow = nrow(X_train), ncol = length(base_learners))
  meta_features_test <- matrix(0, nrow = nrow(X_test), ncol = length(base_learners))
  
  base_models <- list()
  successful_learners <- c()
  
  # Train base learners with cross-validation and error handling
  for (i in seq_along(base_learners)) {
    learner <- base_learners[i]
    cat(sprintf("  Training base learner %d: %s\n", i, learner))
    
    tryCatch({
      fold_idx <- createFolds(y_train, k = cv_folds)
      fold_predictions <- numeric(nrow(X_train))
      test_predictions <- list()
      
      for (fold in 1:cv_folds) {
        train_idx <- unlist(fold_idx[-fold])
        val_idx <- fold_idx[[fold]]
        
        # Skip fold if too few samples
        if (length(train_idx) < 5 || length(val_idx) < 2) next
        
        X_fold_train <- X_train[train_idx, ]
        y_fold_train <- y_train[train_idx]
        X_fold_val <- X_train[val_idx, ]
        
        # Skip if insufficient class diversity
        if (length(unique(y_fold_train)) < 2) next
        
        # Train base model with robust parameters
        if (learner == "rf") {
          ntree <- if (nrow(X_train) < 100) 50 else 200
          mtry <- max(1, min(sqrt(ncol(X_fold_train)), ncol(X_fold_train) %/% 3))
          model <- randomForest(x = X_fold_train, y = as.factor(y_fold_train),
                               ntree = ntree, mtry = mtry,
                               nodesize = max(1, nrow(X_fold_train) %/% 20))
          fold_pred <- predict(model, X_fold_val, type = "prob")[, 2]
          test_pred <- predict(model, X_test, type = "prob")[, 2]
          
        } else if (learner == "xgb") {
          dtrain <- xgb.DMatrix(data = as.matrix(X_fold_train), label = y_fold_train)
          dval <- xgb.DMatrix(data = as.matrix(X_fold_val))
          dtest <- xgb.DMatrix(data = as.matrix(X_test))
          
          max_depth <- if (nrow(X_train) < 100) 3 else 6
          eta <- if (nrow(X_train) < 100) 0.3 else 0.1
          nrounds <- if (nrow(X_train) < 100) 50 else 100
          
          model <- xgb.train(
            params = list(objective = "binary:logistic", max_depth = max_depth, eta = eta,
                         subsample = 0.8, colsample_bytree = 0.8),
            data = dtrain, nrounds = nrounds, verbose = 0
          )
          fold_pred <- predict(model, dval)
          test_pred <- predict(model, dtest)
          
        } else if (learner == "glm") {
          # Logistic regression with regularization and error handling
          tryCatch({
            model <- cv.glmnet(as.matrix(X_fold_train), y_fold_train, 
                              family = "binomial", alpha = 0.5)
            fold_pred <- predict(model, as.matrix(X_fold_val), 
                               s = "lambda.min", type = "response")[, 1]
            test_pred <- predict(model, as.matrix(X_test), 
                               s = "lambda.min", type = "response")[, 1]
          }, error = function(e) {
            # Fallback to simple GLM with fewer features
            n_features <- min(5, ncol(X_fold_train))
            model <- glm(y_fold_train ~ ., 
                        data = data.frame(y_fold_train = y_fold_train, 
                                         X_fold_train[, 1:n_features, drop = FALSE]), 
                        family = binomial())
            fold_pred <- predict(model, 
                               newdata = data.frame(X_fold_val[, 1:n_features, drop = FALSE]), 
                               type = "response")
            test_pred <- predict(model, 
                               newdata = data.frame(X_test[, 1:n_features, drop = FALSE]), 
                               type = "response")
          })
        }
        
        if (!any(is.na(fold_pred)) && !any(is.na(test_pred))) {
          fold_predictions[val_idx] <- fold_pred
          test_predictions[[fold]] <- test_pred
        }
      }
      
      if (length(test_predictions) > 0) {
        meta_features_train[, i] <- fold_predictions
        meta_features_test[, i] <- Reduce("+", test_predictions) / length(test_predictions)
        successful_learners <- c(successful_learners, i)
        
        # Train final base model on full training set
        if (learner == "rf") {
          ntree <- if (nrow(X_train) < 100) 50 else 200
          mtry <- max(1, min(sqrt(ncol(X_train)), ncol(X_train) %/% 3))
          final_model <- randomForest(x = X_train, y = as.factor(y_train),
                                     ntree = ntree, mtry = mtry)
        } else if (learner == "xgb") {
          dtrain_full <- xgb.DMatrix(data = as.matrix(X_train), label = y_train)
          max_depth <- if (nrow(X_train) < 100) 3 else 6
          eta <- if (nrow(X_train) < 100) 0.3 else 0.1
          nrounds <- if (nrow(X_train) < 100) 50 else 100
          final_model <- xgb.train(
            params = list(objective = "binary:logistic", max_depth = max_depth, eta = eta),
            data = dtrain_full, nrounds = nrounds, verbose = 0
          )
        }
        
        base_models[[learner]] <- final_model
      }
      
    }, error = function(e) {
      cat(sprintf("    Base learner %s failed: %s\n", learner, e$message))
    })
  }
  
  # Check if we have enough successful base learners
  if (length(successful_learners) == 0) {
    cat("  All base learners failed. Using single model fallback.\n")
    return(single_model_fallback(X_train, y_train, X_test, y_test))
  }
  
  if (length(successful_learners) == 1) {
    cat("  Only one base learner succeeded. Using simple prediction.\n")
    return(list(
      predictions = meta_features_test[, successful_learners[1]],
      performance = calculate_performance_metrics(y_test, meta_features_test[, successful_learners[1]]),
      method = "single_base_learner"
    ))
  }
  
  cat(sprintf("  Successfully trained %d out of %d base learners.\n", length(successful_learners), length(base_learners)))
  
  # Use only successful learners
  meta_features_train <- meta_features_train[, successful_learners, drop = FALSE]
  meta_features_test <- meta_features_test[, successful_learners, drop = FALSE]
  
  # Train meta-learner with error handling
  cat("  Training meta-learner...\n")
  
  tryCatch({
    y_train_numeric <- as.numeric(y_train)
    
    if (meta_learner == "glm") {
      meta_model <- glm(y_train_numeric ~ ., data = data.frame(meta_features_train), family = "binomial")
      final_pred <- predict(meta_model, data.frame(meta_features_test), type = "response")
      
    } else if (meta_learner == "rf") {
      meta_model <- randomForest(x = meta_features_train, y = as.factor(y_train_numeric), ntree = 100)
      final_pred <- predict(meta_model, meta_features_test, type = "prob")[, 2]
      
    } else if (meta_learner == "xgb") {
      dmeta_train <- xgb.DMatrix(data = meta_features_train, label = y_train_numeric)
      dmeta_test <- xgb.DMatrix(data = meta_features_test)
      
      meta_model <- xgb.train(
        params = list(objective = "binary:logistic", max_depth = 3, eta = 0.1),
        data = dmeta_train, nrounds = 50, verbose = 0
      )
      final_pred <- predict(meta_model, dmeta_test)
    }
    
    performance <- calculate_performance_metrics(y_test, final_pred)
    
    return(list(
      predictions = final_pred,
      base_models = base_models,
      meta_model = meta_model,
      meta_features_train = meta_features_train,
      meta_features_test = meta_features_test,
      performance = performance,
      method = "robust_stacking",
      successful_learners = length(successful_learners)
    ))
    
  }, error = function(e) {
    cat(sprintf("  Meta-learner failed: %s. Using simple averaging.\n", e$message))
    final_pred <- rowMeans(meta_features_test)
    performance <- calculate_performance_metrics(y_test, final_pred)
    
    return(list(
      predictions = final_pred,
      performance = performance,
      method = "stacking_fallback_averaging"
    ))
  })
}

#' Robust Voting Ensemble
voting_ensemble <- function(X_train, y_train, X_test, y_test, base_learners) {
  
  predictions <- list()
  models <- list()
  successful_learners <- c()
  
  cat(sprintf("  Training %d base learners for voting...\n", length(base_learners)))
  
  for (learner in base_learners) {
    cat(sprintf("    Training %s...\n", learner))
    
    tryCatch({
      if (learner == "rf") {
        ntree <- if (nrow(X_train) < 100) 50 else 200
        mtry <- max(1, min(sqrt(ncol(X_train)), ncol(X_train) %/% 3))
        model <- randomForest(x = X_train, y = as.factor(y_train), 
                             ntree = ntree, mtry = mtry,
                             nodesize = max(1, nrow(X_train) %/% 20))
        pred <- predict(model, X_test, type = "prob")[, 2]
        
      } else if (learner == "xgb") {
        dtrain <- xgb.DMatrix(data = as.matrix(X_train), label = y_train)
        dtest <- xgb.DMatrix(data = as.matrix(X_test))
        
        max_depth <- if (nrow(X_train) < 100) 3 else 6
        eta <- if (nrow(X_train) < 100) 0.3 else 0.1
        nrounds <- if (nrow(X_train) < 100) 50 else 100
        
        model <- xgb.train(
          params = list(objective = "binary:logistic", max_depth = max_depth, eta = eta,
                       subsample = 0.8, colsample_bytree = 0.8,
                       min_child_weight = max(1, nrow(X_train) %/% 50)),
          data = dtrain, nrounds = nrounds, verbose = 0
        )
        pred <- predict(model, dtest)
        
      } else if (learner == "glm") {
        # GLM with error handling for small datasets
        tryCatch({
          model <- cv.glmnet(as.matrix(X_train), y_train, 
                            family = "binomial", alpha = 0.5)
          pred <- predict(model, as.matrix(X_test), 
                         s = "lambda.min", type = "response")[, 1]
        }, error = function(e) {
          # Fallback to simple GLM
          n_features <- min(5, ncol(X_train))
          model <- glm(y_train ~ ., 
                      data = data.frame(y_train = y_train, 
                                       X_train[, 1:n_features, drop = FALSE]), 
                      family = binomial())
          pred <- predict(model, 
                         newdata = data.frame(X_test[, 1:n_features, drop = FALSE]), 
                         type = "response")
        })
      }
      
      if (!is.null(pred) && !any(is.na(pred))) {
        predictions[[learner]] <- pred
        models[[learner]] <- model
        successful_learners <- c(successful_learners, learner)
      }
      
    }, error = function(e) {
      cat(sprintf("      %s failed: %s\n", learner, e$message))
    })
  }
  
  # Check if we have enough successful learners
  if (length(successful_learners) == 0) {
    cat("  All base learners failed. Using single model fallback.\n")
    return(single_model_fallback(X_train, y_train, X_test, y_test))
  }
  
  if (length(successful_learners) == 1) {
    cat("  Only one base learner succeeded. Using single prediction.\n")
    return(list(
      predictions = predictions[[successful_learners[1]]],
      performance = calculate_performance_metrics(y_test, predictions[[successful_learners[1]]]),
      method = "single_voting_learner"
    ))
  }
  
  cat(sprintf("  Successfully trained %d out of %d base learners.\n", length(successful_learners), length(base_learners)))
  
  # Average predictions from successful learners (soft voting)
  final_pred <- Reduce("+", predictions[successful_learners]) / length(successful_learners)
  
  performance <- calculate_performance_metrics(y_test, final_pred)
  
  return(list(
    predictions = final_pred,
    individual_predictions = predictions,
    models = models,
    performance = performance,
    method = "robust_voting",
    successful_learners = length(successful_learners),
    total_learners = length(base_learners)
  ))
}

# =============================================================================
# PART 2: REGULARIZATION TECHNIQUES
# =============================================================================

#' Advanced Regularization Methods
#' Implements L1, L2, and Elastic Net regularization
regularized_learning <- function(X_train, y_train, X_test, y_test, 
                               regularization_type = "elastic_net",
                               alpha_range = seq(0, 1, 0.1)) {
  
  cat(sprintf("Training regularized model with %s regularization...\n", regularization_type))
  
  if (regularization_type == "lasso") {
    # L1 regularization (Lasso)
    cv_model <- cv.glmnet(as.matrix(X_train), y_train, family = "binomial", alpha = 1)
    
  } else if (regularization_type == "ridge") {
    # L2 regularization (Ridge)
    cv_model <- cv.glmnet(as.matrix(X_train), y_train, family = "binomial", alpha = 0)
    
  } else if (regularization_type == "elastic_net") {
    # Elastic Net (combination of L1 and L2)
    best_alpha <- tune_elastic_net_alpha(X_train, y_train, alpha_range)
    cv_model <- cv.glmnet(as.matrix(X_train), y_train, family = "binomial", alpha = best_alpha)
  }
  
  # Make predictions
  predictions <- predict(cv_model, as.matrix(X_test), s = "lambda.min", type = "response")[, 1]
  
  # Calculate performance
  performance <- calculate_performance_metrics(y_test, predictions)
  
  # Feature importance (non-zero coefficients)
  coefficients <- coef(cv_model, s = "lambda.min")
  feature_importance <- abs(coefficients[-1, 1])  # Exclude intercept
  names(feature_importance) <- colnames(X_train)
  
  return(list(
    model = cv_model,
    predictions = predictions,
    performance = performance,
    feature_importance = feature_importance,
    regularization_type = regularization_type
  ))
}

#' Tune Elastic Net Alpha Parameter
tune_elastic_net_alpha <- function(X_train, y_train, alpha_range) {
  
  cv_scores <- numeric(length(alpha_range))
  
  for (i in seq_along(alpha_range)) {
    cv_model <- cv.glmnet(as.matrix(X_train), y_train, family = "binomial", alpha = alpha_range[i])
    cv_scores[i] <- min(cv_model$cvm)
  }
  
  best_alpha <- alpha_range[which.min(cv_scores)]
  cat(sprintf("  Best alpha for Elastic Net: %.2f\n", best_alpha))
  
  return(best_alpha)
}

# =============================================================================
# PART 3: CLASS IMBALANCE HANDLING
# =============================================================================

#' Comprehensive Class Imbalance Handling
#' Implements SMOTE, ROSE, and cost-sensitive learning
handle_class_imbalance <- function(X_train, y_train, method = "smote", 
                                 target_ratio = 0.5) {
  
  cat(sprintf("Handling class imbalance using %s method...\n", method))
  
  # Check class distribution
  class_counts <- table(y_train)
  minority_class <- names(class_counts)[which.min(class_counts)]
  majority_class <- names(class_counts)[which.max(class_counts)]
  
  cat(sprintf("  Original distribution: %s\n", paste(names(class_counts), class_counts, collapse = ", ")))
  
  # Check if optional packages are available and fallback if needed
  if (method == "smote" && !use_smote_family) {
    cat("  SMOTE package not available, falling back to oversampling\n")
    method <- "oversampling"
  }
  
  if (method == "rose" && !use_rose) {
    cat("  ROSE package not available, falling back to oversampling\n")
    method <- "oversampling"
  }
  
  if (method == "smote") {
    return(apply_smote(X_train, y_train, target_ratio))
  } else if (method == "rose") {
    return(apply_rose(X_train, y_train, target_ratio))
  } else if (method == "undersampling") {
    return(apply_undersampling(X_train, y_train, target_ratio))
  } else if (method == "oversampling") {
    return(apply_oversampling(X_train, y_train, target_ratio))
  } else if (method == "cost_sensitive") {
    return(list(X_train = X_train, y_train = y_train, class_weights = calculate_class_weights(y_train)))
  }
}

#' SMOTE Implementation
apply_smote <- function(X_train, y_train, target_ratio) {
  
  # Check if SMOTE package is available
  if (!use_smote_family) {
    cat("  SMOTE package not available, using simple oversampling\n")
    return(apply_oversampling(X_train, y_train, target_ratio))
  }
  
  # Try SMOTE with error handling
  tryCatch({
    # Ensure X_train is a data frame and y_train is numeric
    X_train <- as.data.frame(X_train)
    y_train <- as.numeric(y_train)
    
    # Check if we have enough samples for SMOTE
    class_counts <- table(y_train)
    min_class_count <- min(class_counts)
    
    if (min_class_count >= 5 && ncol(X_train) > 0) {
      # Apply SMOTE using smotefamily package
      smote_result <- SMOTE(X_train, y_train, K = min(5, min_class_count - 1), dup_size = 2)
      
      # Extract balanced data
      X_balanced <- smote_result$data[, -ncol(smote_result$data), drop = FALSE]
      y_balanced <- smote_result$data[, ncol(smote_result$data)]
      
      cat(sprintf("  SMOTE applied successfully. Balanced distribution: %s\n", 
                  paste(names(table(y_balanced)), table(y_balanced), collapse = ", ")))
      
      return(list(X_train = X_balanced, y_train = y_balanced))
    } else {
      cat("  Insufficient samples for SMOTE, using simple oversampling\n")
      return(apply_oversampling(X_train, y_train, target_ratio))
    }
  }, error = function(e) {
    cat(sprintf("  SMOTE failed (%s), using simple oversampling\n", e$message))
    return(apply_oversampling(X_train, y_train, target_ratio))
  })
}

#' ROSE Implementation
apply_rose <- function(X_train, y_train, target_ratio) {
  
  # Check if ROSE package is available
  if (!use_rose) {
    cat("  ROSE not available, using simple oversampling\n")
    return(apply_oversampling(X_train, y_train, target_ratio))
  }
  
  tryCatch({
    train_data <- cbind(X_train, target = as.factor(y_train))
    
    # Apply ROSE
    rose_data <- ROSE(target ~ ., data = train_data, seed = 42)$data
    
    X_balanced <- rose_data[, -ncol(rose_data)]
    y_balanced <- as.numeric(as.character(rose_data$target))
    
    cat(sprintf("  Balanced distribution: %s\n", paste(names(table(y_balanced)), table(y_balanced), collapse = ", ")))
    
    return(list(X_train = X_balanced, y_train = y_balanced))
  }, error = function(e) {
    cat(sprintf("  ROSE failed (%s), using simple oversampling\n", e$message))
    return(apply_oversampling(X_train, y_train, target_ratio))
  })
}

#' Simple Oversampling
apply_oversampling <- function(X_train, y_train, target_ratio) {
  
  class_counts <- table(y_train)
  minority_class <- names(class_counts)[which.min(class_counts)]
  majority_class <- names(class_counts)[which.max(class_counts)]
  
  minority_indices <- which(y_train == minority_class)
  majority_indices <- which(y_train == majority_class)
  
  # Calculate how many minority samples to generate
  target_minority_count <- round(length(majority_indices) * target_ratio / (1 - target_ratio))
  samples_to_generate <- target_minority_count - length(minority_indices)
  
  if (samples_to_generate > 0) {
    # Oversample minority class
    oversample_indices <- sample(minority_indices, samples_to_generate, replace = TRUE)
    
    X_balanced <- rbind(X_train, X_train[oversample_indices, ])
    y_balanced <- c(y_train, y_train[oversample_indices])
  } else {
    X_balanced <- X_train
    y_balanced <- y_train
  }
  
  cat(sprintf("  Balanced distribution: %s\n", paste(names(table(y_balanced)), table(y_balanced), collapse = ", ")))
  
  return(list(X_train = X_balanced, y_train = y_balanced))
}

#' Simple Undersampling
apply_undersampling <- function(X_train, y_train, target_ratio) {
  
  class_counts <- table(y_train)
  minority_class <- names(class_counts)[which.min(class_counts)]
  majority_class <- names(class_counts)[which.max(class_counts)]
  
  minority_indices <- which(y_train == minority_class)
  majority_indices <- which(y_train == majority_class)
  
  # Calculate how many majority samples to keep
  target_majority_count <- round(length(minority_indices) * (1 - target_ratio) / target_ratio)
  
  if (target_majority_count < length(majority_indices)) {
    # Undersample majority class
    keep_majority_indices <- sample(majority_indices, target_majority_count)
    
    keep_indices <- c(minority_indices, keep_majority_indices)
    X_balanced <- X_train[keep_indices, ]
    y_balanced <- y_train[keep_indices]
  } else {
    X_balanced <- X_train
    y_balanced <- y_train
  }
  
  cat(sprintf("  Balanced distribution: %s\n", paste(names(table(y_balanced)), table(y_balanced), collapse = ", ")))
  
  return(list(X_train = X_balanced, y_train = y_balanced))
}

#' Calculate Class Weights for Cost-Sensitive Learning
calculate_class_weights <- function(y_train) {
  
  class_counts <- table(y_train)
  total_samples <- length(y_train)
  n_classes <- length(class_counts)
  
  # Inverse frequency weighting
  class_weights <- total_samples / (n_classes * class_counts)
  
  return(class_weights)
}

# =============================================================================
# PART 4: ADVANCED CROSS-VALIDATION
# =============================================================================

#' Advanced Cross-Validation Strategies
advanced_cross_validation <- function(X, y, model_func, cv_method = "stratified_kfold", 
                                    k = 5, repeats = 3) {
  
  cat(sprintf("Performing %s cross-validation with k=%d, repeats=%d...\n", cv_method, k, repeats))
  
  if (cv_method == "stratified_kfold") {
    return(stratified_kfold_cv(X, y, model_func, k, repeats))
  } else if (cv_method == "time_series") {
    return(time_series_cv(X, y, model_func, k))
  } else if (cv_method == "group_kfold") {
    return(group_kfold_cv(X, y, model_func, k))
  } else if (cv_method == "nested_cv") {
    return(nested_cv(X, y, model_func, k))
  }
}

#' Stratified K-Fold Cross-Validation
stratified_kfold_cv <- function(X, y, model_func, k, repeats) {
  
  cv_scores <- numeric(k * repeats)
  cv_predictions <- list()
  
  for (repeat_i in 1:repeats) {
    folds <- createFolds(y, k = k, list = TRUE)
    
    for (fold_i in 1:k) {
      train_idx <- unlist(folds[-fold_i])
      test_idx <- folds[[fold_i]]
      
      X_train_fold <- X[train_idx, ]
      y_train_fold <- y[train_idx]
      X_test_fold <- X[test_idx, ]
      y_test_fold <- y[test_idx]
      
      # Train model
      model <- model_func(X_train_fold, y_train_fold)
      
      # Make predictions
      if ("randomForest" %in% class(model)) {
        pred <- predict(model, X_test_fold, type = "prob")[, 2]
      } else if ("xgb.Booster" %in% class(model)) {
        dtest <- xgb.DMatrix(data = as.matrix(X_test_fold))
        pred <- predict(model, dtest)
      }
      
      # Calculate score
      if (length(unique(y_test_fold)) > 1) {
        roc_obj <- roc(y_test_fold, pred, quiet = TRUE)
        score <- as.numeric(roc_obj$auc)
      } else {
        score <- mean((pred > 0.5) == y_test_fold)
      }
      
      cv_scores[(repeat_i - 1) * k + fold_i] <- score
      cv_predictions[[(repeat_i - 1) * k + fold_i]] <- list(
        predictions = pred,
        actual = y_test_fold,
        indices = test_idx
      )
    }
  }
  
  return(list(
    scores = cv_scores,
    mean_score = mean(cv_scores, na.rm = TRUE),
    std_score = sd(cv_scores, na.rm = TRUE),
    predictions = cv_predictions
  ))
}

# =============================================================================
# PART 5: PERFORMANCE METRICS
# =============================================================================

# Load standardized performance utilities
if (!exists("calculate_standardized_performance")) {
  source("ml_performance_utils.r")
}

#' Calculate Comprehensive Performance Metrics (Wrapper for standardized function)
calculate_performance_metrics <- function(y_true, y_pred, threshold = 0.5) {
  
  # Use standardized performance calculation
  performance <- calculate_standardized_performance(
    y_true = y_true,
    y_pred = y_pred,
    threshold = threshold,
    task_type = "classification"
  )
  
  # Add confusion matrix for backward compatibility
  if (!is.null(performance$auc) && !is.na(performance$auc)) {
    y_pred_binary <- as.numeric(y_pred > threshold)
    tp <- sum(y_true == 1 & y_pred_binary == 1)
    tn <- sum(y_true == 0 & y_pred_binary == 0)
    fp <- sum(y_true == 0 & y_pred_binary == 1)
    fn <- sum(y_true == 1 & y_pred_binary == 0)
    
    performance$confusion_matrix <- matrix(c(tn, fp, fn, tp), nrow = 2, 
                                         dimnames = list(c("Pred_0", "Pred_1"), c("True_0", "True_1")))
  }
  
  return(performance)
}

cat("ML ensemble and advanced techniques module loaded successfully!\n")
cat("Available functions:\n")
cat("  - advanced_ensemble_learning(): Comprehensive ensemble methods\n")
cat("  - regularized_learning(): L1/L2/Elastic Net regularization\n")
cat("  - handle_class_imbalance(): SMOTE, ROSE, and cost-sensitive learning\n")
cat("  - advanced_cross_validation(): Multiple CV strategies\n")
cat("  - calculate_performance_metrics(): Comprehensive evaluation metrics\n")