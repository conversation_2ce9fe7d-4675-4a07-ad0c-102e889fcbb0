# =============================================================================
# ENHANCED VIRTUAL CLINICAL TRIAL EXECUTION SCRIPT
# =============================================================================
# This script runs the enhanced virtual clinical trial with comprehensive
# safety assessment for the Ibrutinib platelet QSP model

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
  library(survival)
  library(survminer)
  library(gridExtra)
  library(splines)
  library(mgcv)
})

# Set working directory and seed for reproducibility
set.seed(12345)

cat("=== ENHANCED VIRTUAL CLINICAL TRIAL FOR IBRUTINIB SAFETY ===\n")
cat("Loading enhanced virtual clinical trial functions...\n")

# Source the enhanced virtual clinical trial script
tryCatch({
  source("virtual_clinical_trial.r")
  cat("✓ Enhanced virtual clinical trial functions loaded successfully\n\n")
}, error = function(e) {
  cat("ERROR loading virtual clinical trial script:", e$message, "\n")
  stop("Cannot proceed without virtual clinical trial functions")
})

# Check if synthetic population exists
results_dir <- get_ml_results_dir()
population_file <- build_path(results_dir, "synthetic_patient_population.csv")

if (file.exists(population_file)) {
  cat("✓ Existing synthetic population found\n")
} else {
  cat("! No existing synthetic population found - will generate new one\n")
}

# Run the enhanced virtual clinical trial
cat("Starting enhanced virtual clinical trial simulation...\n")
cat("Features:\n")
cat("- CTCAE v5.0 bleeding classification\n")
cat("- Multiple dosing regimens (420mg CLL/SLL, 560mg MCL)\n")
cat("- Dose modification modeling\n")
cat("- ICH E3 compliant safety tables\n")
cat("- Kaplan-Meier survival analyses\n")
cat("- Forest plots for subgroup analyses\n\n")

# Execute the trial
trial_results <- tryCatch({
  run_enhanced_virtual_clinical_trial(
    n_patients = 2000,  # Manageable sample size for testing
    save_results = TRUE,
    use_existing_population = TRUE
  )
}, error = function(e) {
  cat("ERROR during trial execution:", e$message, "\n")
  return(NULL)
})

if (!is.null(trial_results)) {
  cat("\n=== TRIAL EXECUTION COMPLETED SUCCESSFULLY ===\n")
  
  # Generate comprehensive visualizations
  cat("Generating comprehensive safety visualizations...\n")
  
  visualization_results <- tryCatch({
    plot_enhanced_trial_results(trial_results)
  }, error = function(e) {
    cat("ERROR during visualization generation:", e$message, "\n")
    return(NULL)
  })
  
  if (!is.null(visualization_results)) {
    cat("✓ All safety analyses and visualizations completed\n")
    
    # Summary of generated outputs
    cat("\n=== GENERATED OUTPUTS ===\n")
    cat("Data files:\n")
    cat("- enhanced_virtual_clinical_trial_results.csv\n")
    cat("- safety_overview_table.csv\n")
    cat("- ctcae_distribution_table.csv\n")
    cat("- time_to_event_summary_table.csv\n\n")
    
    cat("Visualization files:\n")
    cat("- enhanced_bleeding_risk_distribution.png\n")
    cat("- enhanced_temporal_bleeding_pattern.png\n")
    cat("- enhanced_anticoagulant_interactions.png\n")
    cat("- enhanced_age_bleeding_relationship.png\n")
    cat("- kaplan_meier_any_bleeding.png\n")
    cat("- kaplan_meier_major_bleeding.png\n")
    cat("- safety_forest_plot.png\n\n")
    
    # Key safety metrics summary
    cat("=== KEY SAFETY METRICS ===\n")
    
    # Calculate key metrics
    ibrutinib_patients <- trial_results[trial_results$treatment_arm == "Ibrutinib_420mg", ]
    control_patients <- trial_results[trial_results$treatment_arm == "Control", ]
    
    if (nrow(ibrutinib_patients) > 0) {
      cat("Ibrutinib patients (n =", nrow(ibrutinib_patients), "):\n")
      cat("- Any bleeding rate:", 
          round(mean(ibrutinib_patients$bleeding_event_occurred, na.rm = TRUE) * 100, 1), "%\n")
      cat("- Major bleeding rate:", 
          round(mean(ibrutinib_patients$major_bleeding_occurred, na.rm = TRUE) * 100, 1), "%\n")
      cat("- Fatal bleeding rate:", 
          round(mean(ibrutinib_patients$fatal_bleeding, na.rm = TRUE) * 100, 1), "%\n")
      cat("- Dose reduction rate:", 
          round(mean(ibrutinib_patients$dose_reductions > 0, na.rm = TRUE) * 100, 1), "%\n")
      cat("- Discontinuation rate:", 
          round(mean(ibrutinib_patients$permanent_discontinuation, na.rm = TRUE) * 100, 1), "%\n")
    }
    
    if (nrow(control_patients) > 0) {
      cat("\nControl patients (n =", nrow(control_patients), "):\n")
      cat("- Any bleeding rate:", 
          round(mean(control_patients$bleeding_event_occurred, na.rm = TRUE) * 100, 1), "%\n")
      cat("- Major bleeding rate:", 
          round(mean(control_patients$major_bleeding_occurred, na.rm = TRUE) * 100, 1), "%\n")
    }
    
    cat("\n=== ENHANCED VIRTUAL CLINICAL TRIAL COMPLETED SUCCESSFULLY ===\n")
    
  } else {
    cat("! Visualization generation failed, but trial data is available\n")
  }
  
} else {
  cat("! Trial execution failed\n")
}

cat("\nScript execution completed.\n")
