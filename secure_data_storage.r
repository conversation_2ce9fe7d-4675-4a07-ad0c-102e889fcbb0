# =============================================================================
# SECURE DATA STORAGE MODULE
# =============================================================================
# This module provides secure and efficient data storage solutions with
# comprehensive error handling, data validation, and integrity checks.
#
# Features:
# - Data validation and sanitization
# - Atomic write operations with backup
# - Checksum verification for data integrity
# - Automatic recovery mechanisms
# - Comprehensive error handling and logging
#
# Author: Enhanced Storage System
# Date: 2024
# =============================================================================

# Load required libraries
if (!require(digest, quietly = TRUE)) {
  install.packages("digest")
  library(digest)
}

source("ml_file_utils.r")

#' Validate Data Before Storage
#' 
#' Performs comprehensive validation of data before storage
#' 
#' @param data Data to validate
#' @param data_name Name of the dataset for logging
#' @param required_columns Optional vector of required column names
#' @param min_rows Minimum number of rows required (default: 1)
#' @param verbose Whether to print validation messages (default: TRUE)
#' @return List with validation results
validate_data <- function(data, data_name, required_columns = NULL, min_rows = 1, verbose = TRUE) {
  
  validation_results <- list(
    valid = TRUE,
    errors = character(0),
    warnings = character(0),
    metadata = list()
  )
  
  # Check if data exists
  if (is.null(data)) {
    validation_results$valid <- FALSE
    validation_results$errors <- c(validation_results$errors, "Data is NULL")
    return(validation_results)
  }
  
  # Check data type
  if (!is.data.frame(data) && !is.matrix(data) && !is.list(data)) {
    validation_results$warnings <- c(validation_results$warnings, 
                                   sprintf("Data type is %s, not data.frame", class(data)[1]))
  }
  
  # Check dimensions for data frames
  if (is.data.frame(data)) {
    validation_results$metadata$rows <- nrow(data)
    validation_results$metadata$cols <- ncol(data)
    validation_results$metadata$size_mb <- round(object.size(data) / 1024^2, 2)
    
    # Check minimum rows
    if (nrow(data) < min_rows) {
      validation_results$valid <- FALSE
      validation_results$errors <- c(validation_results$errors, 
                                   sprintf("Insufficient rows: %d < %d", nrow(data), min_rows))
    }
    
    # Check required columns
    if (!is.null(required_columns)) {
      missing_cols <- setdiff(required_columns, colnames(data))
      if (length(missing_cols) > 0) {
        validation_results$valid <- FALSE
        validation_results$errors <- c(validation_results$errors, 
                                     sprintf("Missing columns: %s", paste(missing_cols, collapse = ", ")))
      }
    }
    
    # Check for missing values
    na_count <- sum(is.na(data))
    if (na_count > 0) {
      na_percentage <- round(na_count / (nrow(data) * ncol(data)) * 100, 2)
      validation_results$warnings <- c(validation_results$warnings, 
                                     sprintf("Contains %d NA values (%.2f%%)", na_count, na_percentage))
      validation_results$metadata$na_count <- na_count
      validation_results$metadata$na_percentage <- na_percentage
    }
  }
  
  # Generate data checksum for integrity
  validation_results$metadata$checksum <- digest(data, algo = "sha256")
  validation_results$metadata$timestamp <- Sys.time()
  
  if (verbose) {
    cat(sprintf("\n=== Data Validation: %s ===\n", data_name))
    cat(sprintf("Status: %s\n", ifelse(validation_results$valid, "VALID", "INVALID")))
    
    if (length(validation_results$errors) > 0) {
      cat("Errors:\n")
      for (error in validation_results$errors) {
        cat(sprintf("  - %s\n", error))
      }
    }
    
    if (length(validation_results$warnings) > 0) {
      cat("Warnings:\n")
      for (warning in validation_results$warnings) {
        cat(sprintf("  - %s\n", warning))
      }
    }
    
    if (is.data.frame(data)) {
      cat(sprintf("Dimensions: %d rows × %d columns\n", 
                 validation_results$metadata$rows, validation_results$metadata$cols))
      cat(sprintf("Size: %.2f MB\n", validation_results$metadata$size_mb))
    }
    
    cat(sprintf("Checksum: %s\n", substr(validation_results$metadata$checksum, 1, 16)))
  }
  
  return(validation_results)
}

#' Create Backup of Existing File
#' 
#' Creates a timestamped backup of existing file before overwriting
#' 
#' @param file_path Original file path
#' @param verbose Whether to print backup messages (default: TRUE)
#' @return Backup file path or NULL if failed
create_backup <- function(file_path, verbose = TRUE) {
  
  if (!file.exists(file_path)) {
    return(NULL)  # No backup needed if file doesn't exist
  }
  
  # Generate backup filename with timestamp
  file_info <- file.info(file_path)
  timestamp <- format(Sys.time(), "%Y%m%d_%H%M%S")
  file_ext <- tools::file_ext(file_path)
  file_base <- tools::file_path_sans_ext(file_path)
  
  backup_path <- sprintf("%s_backup_%s.%s", file_base, timestamp, file_ext)
  
  tryCatch({
    file.copy(file_path, backup_path, overwrite = FALSE)
    if (verbose) cat(sprintf("Created backup: %s\n", backup_path))
    return(backup_path)
  }, error = function(e) {
    if (verbose) cat(sprintf("Failed to create backup: %s\n", e$message))
    return(NULL)
  })
}

#' Secure Data Save with Comprehensive Protection
#' 
#' Saves data with validation, backup, atomic operations, and integrity checks
#' 
#' @param data Data to save
#' @param file_path Target file path
#' @param data_name Descriptive name for the dataset
#' @param format Save format: "rds", "csv", or "auto" (default: "auto")
#' @param required_columns Optional vector of required column names
#' @param min_rows Minimum number of rows required (default: 1)
#' @param create_backup Whether to backup existing files (default: TRUE)
#' @param verify_integrity Whether to verify data after saving (default: TRUE)
#' @param verbose Whether to print detailed messages (default: TRUE)
#' @return List with operation results
secure_data_save <- function(data, file_path, data_name, 
                            format = "auto", required_columns = NULL, min_rows = 1,
                            create_backup = TRUE, verify_integrity = TRUE, verbose = TRUE) {
  
  operation_start <- Sys.time()
  
  result <- list(
    success = FALSE,
    file_path = file_path,
    data_name = data_name,
    backup_path = NULL,
    validation = NULL,
    checksum_original = NULL,
    checksum_saved = NULL,
    operation_time = NULL,
    errors = character(0)
  )
  
  if (verbose) {
    cat(sprintf("\n=== SECURE DATA SAVE: %s ===\n", data_name))
    cat(sprintf("Target: %s\n", file_path))
  }
  
  # Step 1: Validate input data
  validation <- validate_data(data, data_name, required_columns, min_rows, verbose)
  result$validation <- validation
  
  if (!validation$valid) {
    result$errors <- c(result$errors, "Data validation failed")
    if (verbose) cat("OPERATION ABORTED: Data validation failed\n")
    return(result)
  }
  
  result$checksum_original <- validation$metadata$checksum
  
  # Step 2: Determine save format
  if (format == "auto") {
    file_ext <- tolower(tools::file_ext(file_path))
    format <- switch(file_ext,
                    "csv" = "csv",
                    "rds" = "rds",
                    "rds")  # Default to RDS
  }
  
  # Step 3: Create backup if requested
  if (create_backup) {
    result$backup_path <- create_backup(file_path, verbose)
  }
  
  # Step 4: Prepare temporary file for atomic operation
  temp_file <- paste0(file_path, ".tmp")
  
  # Step 5: Save data to temporary file
  save_success <- FALSE
  
  tryCatch({
    if (format == "csv") {
      if (!is.data.frame(data)) {
        stop("CSV format requires data.frame input")
      }
      save_success <- safe_csv_save(data, temp_file, verbose = verbose)
    } else {
      save_success <- safe_file_save(data, temp_file, saveRDS, verbose = verbose)
    }
  }, error = function(e) {
    result$errors <- c(result$errors, sprintf("Save operation failed: %s", e$message))
    if (verbose) cat(sprintf("Save error: %s\n", e$message))
  })
  
  if (!save_success) {
    # Clean up temporary file
    if (file.exists(temp_file)) {
      file.remove(temp_file)
    }
    return(result)
  }
  
  # Step 6: Verify integrity if requested
  if (verify_integrity) {
    if (verbose) cat("Verifying data integrity...\n")
    
    tryCatch({
      if (format == "csv") {
        saved_data <- safe_csv_load(temp_file, verbose = FALSE)
      } else {
        saved_data <- safe_file_load(temp_file, readRDS, verbose = FALSE)
      }
      
      if (!is.null(saved_data)) {
        result$checksum_saved <- digest(saved_data, algo = "sha256")
        
        if (result$checksum_original == result$checksum_saved) {
          if (verbose) cat("✓ Data integrity verified\n")
        } else {
          result$errors <- c(result$errors, "Data integrity check failed - checksums don't match")
          if (verbose) cat("✗ Data integrity check failed\n")
          file.remove(temp_file)
          return(result)
        }
      } else {
        result$errors <- c(result$errors, "Could not load saved data for verification")
        if (verbose) cat("✗ Could not verify saved data\n")
        file.remove(temp_file)
        return(result)
      }
    }, error = function(e) {
      result$errors <- c(result$errors, sprintf("Integrity verification failed: %s", e$message))
      if (verbose) cat(sprintf("Verification error: %s\n", e$message))
      file.remove(temp_file)
      return(result)
    })
  }
  
  # Step 7: Atomic move from temporary to final location
  tryCatch({
    if (file.exists(file_path)) {
      file.remove(file_path)
    }
    file.rename(temp_file, file_path)
    
    result$success <- TRUE
    result$operation_time <- as.numeric(difftime(Sys.time(), operation_start, units = "secs"))
    
    if (verbose) {
      cat(sprintf("✓ Successfully saved %s\n", data_name))
      cat(sprintf("✓ Operation completed in %.2f seconds\n", result$operation_time))
    }
    
  }, error = function(e) {
    result$errors <- c(result$errors, sprintf("Atomic move failed: %s", e$message))
    if (verbose) cat(sprintf("Move error: %s\n", e$message))
    
    # Clean up temporary file
    if (file.exists(temp_file)) {
      file.remove(temp_file)
    }
  })
  
  return(result)
}

#' Secure Data Load with Integrity Verification
#' 
#' Loads data with integrity checks and error recovery
#' 
#' @param file_path File path to load
#' @param data_name Descriptive name for logging
#' @param format Load format: "rds", "csv", or "auto" (default: "auto")
#' @param verify_integrity Whether to perform integrity checks (default: TRUE)
#' @param attempt_recovery Whether to attempt backup recovery on failure (default: TRUE)
#' @param verbose Whether to print detailed messages (default: TRUE)
#' @return List with loaded data and operation results
secure_data_load <- function(file_path, data_name, format = "auto", 
                            verify_integrity = TRUE, attempt_recovery = TRUE, verbose = TRUE) {
  
  operation_start <- Sys.time()
  
  result <- list(
    success = FALSE,
    data = NULL,
    file_path = file_path,
    data_name = data_name,
    checksum = NULL,
    operation_time = NULL,
    recovery_attempted = FALSE,
    errors = character(0)
  )
  
  if (verbose) {
    cat(sprintf("\n=== SECURE DATA LOAD: %s ===\n", data_name))
    cat(sprintf("Source: %s\n", file_path))
  }
  
  # Check if file exists
  if (!file.exists(file_path)) {
    result$errors <- c(result$errors, "File does not exist")
    
    if (attempt_recovery && verbose) {
      cat("Attempting to find backup files...\n")
      
      # Look for backup files
      file_base <- tools::file_path_sans_ext(file_path)
      file_ext <- tools::file_ext(file_path)
      backup_pattern <- sprintf("%s_backup_*\\.%s$", basename(file_base), file_ext)
      backup_dir <- dirname(file_path)
      
      backup_files <- list.files(backup_dir, pattern = backup_pattern, full.names = TRUE)
      
      if (length(backup_files) > 0) {
        # Use most recent backup
        backup_files <- backup_files[order(file.mtime(backup_files), decreasing = TRUE)]
        latest_backup <- backup_files[1]
        
        if (verbose) cat(sprintf("Found backup: %s\n", latest_backup))
        
        # Recursively load from backup
        backup_result <- secure_data_load(latest_backup, paste(data_name, "(backup)"), 
                                         format, verify_integrity, FALSE, verbose)
        
        if (backup_result$success) {
          result$data <- backup_result$data
          result$success <- TRUE
          result$recovery_attempted <- TRUE
          result$checksum <- backup_result$checksum
          
          if (verbose) cat("✓ Successfully recovered from backup\n")
        }
      }
    }
    
    if (!result$success) {
      if (verbose) cat("✗ No valid file or backup found\n")
      return(result)
    }
  }
  
  # Load data if not already loaded from backup
  if (!result$success) {
    # Determine load format
    if (format == "auto") {
      file_ext <- tolower(tools::file_ext(file_path))
      format <- switch(file_ext,
                      "csv" = "csv",
                      "rds" = "rds",
                      "rds")  # Default to RDS
    }
    
    tryCatch({
      if (format == "csv") {
        result$data <- safe_csv_load(file_path, verbose = verbose)
      } else {
        result$data <- safe_file_load(file_path, readRDS, verbose = verbose)
      }
      
      if (!is.null(result$data)) {
        result$success <- TRUE
        
        # Generate checksum for integrity
        if (verify_integrity) {
          result$checksum <- digest(result$data, algo = "sha256")
          if (verbose) {
            cat(sprintf("✓ Data loaded successfully\n"))
            cat(sprintf("Checksum: %s\n", substr(result$checksum, 1, 16)))
          }
        }
      } else {
        result$errors <- c(result$errors, "Loaded data is NULL")
      }
      
    }, error = function(e) {
      result$errors <- c(result$errors, sprintf("Load operation failed: %s", e$message))
      if (verbose) cat(sprintf("Load error: %s\n", e$message))
    })
  }
  
  result$operation_time <- as.numeric(difftime(Sys.time(), operation_start, units = "secs"))
  
  if (verbose && result$success) {
    cat(sprintf("✓ Operation completed in %.2f seconds\n", result$operation_time))
  }
  
  return(result)
}

cat("\nSecure data storage module loaded successfully!\n")
cat("Enhanced features available:\n")
cat("  - validate_data(): Comprehensive data validation\n")
cat("  - create_backup(): Automatic backup creation\n")
cat("  - secure_data_save(): Secure saving with integrity checks\n")
cat("  - secure_data_load(): Secure loading with recovery options\n")
cat("\nSecurity features:\n")
cat("  ✓ Data validation and sanitization\n")
cat("  ✓ Atomic write operations\n")
cat("  ✓ Automatic backup creation\n")
cat("  ✓ SHA-256 checksum verification\n")
cat("  ✓ Error recovery mechanisms\n")
cat("  ✓ Comprehensive logging\n")