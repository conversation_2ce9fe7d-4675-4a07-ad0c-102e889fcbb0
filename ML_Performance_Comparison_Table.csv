Algorithm,Target,AUC,Accuracy,Sensitivity,Specificity,Implementation,Status,Notes
Regularized GLM,Major Bleeding Event,1.000,1.000,1.000,1.000,glmnet (Elastic Net α=0.5),Success,Best performing model
Stacking Ensemble,Major Bleeding Event,1.000,1.000,1.000,1.000,RF+XGBoost+GLM with GLM meta-learner,Success,Perfect classification
Voting Ensemble,Major Bleeding Event,1.000,1.000,1.000,1.000,Majority voting,Success,Perfect classification
Random Forest (Baseline),Major Bleeding Event,1.000,1.000,1.000,1.000,randomForest (100 trees),Success,Baseline model
XGBoost (Optimized),Major Bleeding Event,0.000,0.000,0.000,0.000,xgboost with hyperparameter tuning,Failed,Failed to converge
Random Forest (Optimized),Major Bleeding Event,0.000,0.000,0.000,0.000,randomForest with optimization,Failed,Failed to converge
Random Forest (Baseline),Bleeding Risk,1.000,NA,NA,NA,randomForest (100 trees),Success,Best performing model
Regularized GLM,Bleeding Risk,NA,NA,NA,NA,glmnet (Elastic Net α=0.5),Partial,Elastic Net regularization
XGBoost (Tuned),Bleeding Risk,NA,NA,NA,NA,xgboost with tuning,Partial,Hyperparameter optimized
Stacking Ensemble,Bleeding Risk,NA,NA,NA,NA,RF+XGBoost+GLM meta-learner,Partial,Meta-learner approach
Random Forest (Baseline),Major Bleeding,1.000,NA,NA,NA,randomForest (100 trees),Success,Alternative target
Regularized GLM,Major Bleeding,NA,NA,NA,NA,glmnet (Elastic Net α=0.5),Partial,Elastic Net regularization
XGBoost (Tuned),Major Bleeding,NA,NA,NA,NA,xgboost with tuning,Partial,Gradient boosting
Stacking Ensemble,Major Bleeding,NA,NA,NA,NA,RF+XGBoost+GLM meta-learner,Partial,Combined predictions
Neural Networks,All Targets,NA,NA,NA,NA,Multi-layer perceptron,Available,Limited evaluation
Bagging,All Targets,NA,NA,NA,NA,Bootstrap aggregating,Integrated,Within Random Forest
Boosting,All Targets,Variable,Variable,Variable,Variable,XGBoost implementation,Variable,Variable results
Lasso (L1),All Targets,NA,NA,NA,NA,glmnet (α=1),Available,Feature selection
Ridge (L2),All Targets,NA,NA,NA,NA,glmnet (α=0),Available,Coefficient shrinkage
Elastic Net,All Targets,1.000,1.000,1.000,1.000,glmnet (α=0.5),Success,Best regularization method
SMOTE,All Targets,NA,NA,NA,NA,smotefamily package,Available,Class imbalance handling
ROSE,All Targets,NA,NA,NA,NA,ROSE package,Available,Random oversampling
Cost-Sensitive Learning,All Targets,NA,NA,NA,NA,Custom implementation,Available,Imbalanced data handling