# =============================================================================
# COMPREHENSIVE SAFETY ANALYSIS - FULL 5,000 PATIENT POPULATION
# =============================================================================
# Re-execute comprehensive safety analysis on all 5,000 synthetic patients

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
  library(survival)
  library(survminer)
  library(gridExtra)
})

set.seed(12345)

cat("=== COMPREHENSIVE SAFETY ANALYSIS - FULL POPULATION ===\n")
cat("Re-executing safety analysis on all 5,000 synthetic patients\n\n")

# Load file utilities and full population
source("ml_file_utils.r")
results_dir <- get_ml_results_dir()
population_file <- build_path(results_dir, "synthetic_patient_population.csv")

population <- read.csv(population_file, stringsAsFactors = FALSE)
n_patients <- nrow(population)

cat("Loaded FULL population:", n_patients, "patients\n")
cat("Treatment arm distribution:\n")
print(table(population$treatment_arm))
cat("\n")

# Generate realistic bleeding events based on clinical evidence
cat("Generating realistic bleeding events for full population...\n")

# Initialize comprehensive safety data for all patients
safety_data <- data.frame(
  patient_id = population$patient_id,
  treatment_arm = population$treatment_arm,
  age = population$age,
  anticoagulants = population$anticoagulants,
  high_comorbidity_score = population$high_comorbidity_score,
  platelet_count = population$platelet_count,
  treatment_duration_months = population$treatment_duration_months,
  
  # Bleeding outcomes
  any_bleeding = FALSE,
  major_bleeding = FALSE,
  minor_bleeding = FALSE,
  crnm_bleeding = FALSE,
  fatal_bleeding = FALSE,
  ich_bleeding = FALSE,
  gi_bleeding = FALSE,
  
  # CTCAE grades
  ctcae_max_grade = 0,
  
  # Time-to-event
  time_to_first_bleeding_days = NA,
  time_to_major_bleeding_days = NA,
  
  # Dose modifications
  dose_reduction = FALSE,
  dose_interruption = FALSE,
  permanent_discontinuation = FALSE,
  
  stringsAsFactors = FALSE
)

cat("Processing", n_patients, "patients for bleeding risk simulation...\n")

# Enhanced bleeding risk calculation with literature-based parameters
for (i in 1:n_patients) {
  
  # Literature-based baseline bleeding rates
  # Ibrutinib clinical trials: 19% any bleeding, 6% major bleeding
  # Control: 2.5% any bleeding, 1% major bleeding
  if (population$treatment_arm[i] == "Ibrutinib_420mg") {
    base_any_bleeding_risk <- 0.19
    base_major_bleeding_risk <- 0.06
  } else {
    base_any_bleeding_risk <- 0.025
    base_major_bleeding_risk <- 0.01
  }
  
  # Risk modifiers based on clinical evidence
  # Age effect: HR 7.49 for age ≥70 from SEER-Medicare analysis
  age_multiplier <- case_when(
    population$age[i] >= 75 ~ 3.0,    # Very elderly
    population$age[i] >= 70 ~ 2.5,    # Elderly (based on HR 7.49)
    population$age[i] >= 65 ~ 1.5,    # Older adults
    TRUE ~ 1.0                        # Younger adults
  )
  
  # Anticoagulant effect: OR 2.54 from nested case-control study
  anticoag_multiplier <- ifelse(population$anticoagulants[i], 2.54, 1.0)
  
  # Comorbidity effect: increased bleeding risk
  comorbidity_multiplier <- ifelse(population$high_comorbidity_score[i], 1.4, 1.0)
  
  # Platelet count effect: OR 0.9 per 10×10⁹/L decrease
  platelet_multiplier <- (population$platelet_count[i] / 150)^(-0.1)
  platelet_multiplier <- pmax(platelet_multiplier, 0.5)  # Floor at 0.5
  
  # Combined individual risk
  individual_any_bleeding_risk <- base_any_bleeding_risk * age_multiplier * 
                                 anticoag_multiplier * comorbidity_multiplier * 
                                 platelet_multiplier
  
  individual_major_bleeding_risk <- base_major_bleeding_risk * age_multiplier * 
                                   anticoag_multiplier * comorbidity_multiplier * 
                                   platelet_multiplier
  
  # Cap risks at realistic maximums
  individual_any_bleeding_risk <- min(individual_any_bleeding_risk, 0.85)
  individual_major_bleeding_risk <- min(individual_major_bleeding_risk, 0.60)
  
  # Simulate bleeding occurrence
  if (runif(1) < individual_any_bleeding_risk) {
    safety_data$any_bleeding[i] <- TRUE
    
    # Time to bleeding (exponential distribution)
    max_time <- population$treatment_duration_months[i] * 30.44
    # Higher hazard rate for higher risk patients
    hazard_rate <- individual_any_bleeding_risk * 2 / max_time
    time_to_bleeding <- rexp(1, rate = hazard_rate)
    safety_data$time_to_first_bleeding_days[i] <- min(time_to_bleeding, max_time)
    
    # Determine if major bleeding occurs
    major_bleeding_conditional_prob <- individual_major_bleeding_risk / individual_any_bleeding_risk
    major_bleeding_conditional_prob <- min(major_bleeding_conditional_prob, 0.8)
    
    if (runif(1) < major_bleeding_conditional_prob) {
      # Major bleeding
      safety_data$major_bleeding[i] <- TRUE
      safety_data$time_to_major_bleeding_days[i] <- safety_data$time_to_first_bleeding_days[i]
      
      # CTCAE grade for major bleeding (3-5)
      # Risk-adjusted grade probabilities
      if (age_multiplier >= 2.5 && anticoag_multiplier > 2.0) {
        # High-risk patients: more severe grades
        grade_probs <- c(0.5, 0.35, 0.15)  # Grade 3, 4, 5
      } else {
        # Standard risk patients
        grade_probs <- c(0.65, 0.25, 0.10)  # Grade 3, 4, 5
      }
      
      safety_data$ctcae_max_grade[i] <- sample(3:5, 1, prob = grade_probs)
      
      # Fatal bleeding (Grade 5)
      if (safety_data$ctcae_max_grade[i] == 5) {
        safety_data$fatal_bleeding[i] <- TRUE
      }
      
      # Location of major bleeding based on clinical data
      # ICH: 8%, GI: 65%, Other: 27%
      location_probs <- c(0.08, 0.65, 0.27)
      location <- sample(c("ICH", "GI", "Other"), 1, prob = location_probs)
      
      if (location == "ICH") {
        safety_data$ich_bleeding[i] <- TRUE
      } else if (location == "GI") {
        safety_data$gi_bleeding[i] <- TRUE
      }
      
    } else {
      # Non-major bleeding
      if (runif(1) < 0.6) {
        # CRNM bleeding (Grade 2)
        safety_data$crnm_bleeding[i] <- TRUE
        safety_data$ctcae_max_grade[i] <- 2
      } else {
        # Minor bleeding (Grade 1)
        safety_data$minor_bleeding[i] <- TRUE
        safety_data$ctcae_max_grade[i] <- 1
      }
    }
  } else {
    # No bleeding
    safety_data$time_to_first_bleeding_days[i] <- population$treatment_duration_months[i] * 30.44
    safety_data$time_to_major_bleeding_days[i] <- population$treatment_duration_months[i] * 30.44
  }
  
  # Dose modifications for ibrutinib patients
  if (population$treatment_arm[i] == "Ibrutinib_420mg") {
    
    if (safety_data$major_bleeding[i]) {
      if (safety_data$fatal_bleeding[i] || safety_data$ich_bleeding[i]) {
        safety_data$permanent_discontinuation[i] <- TRUE
      } else {
        safety_data$dose_reduction[i] <- TRUE
      }
    }
    
    # CRNM bleeding may trigger dose interruption (30% probability)
    if (safety_data$crnm_bleeding[i] && runif(1) < 0.3) {
      safety_data$dose_interruption[i] <- TRUE
    }
  }
  
  # Progress indicator
  if (i %% 1000 == 0) {
    cat("Processed", i, "patients...\n")
  }
}

cat("✓ Bleeding events generated for all", n_patients, "patients\n\n")

# Save comprehensive safety data for full population
safety_file <- build_path(results_dir, "full_population_safety_analysis.csv")
safe_csv_save(safety_data, safety_file)

cat("✓ Full population safety analysis saved to:", safety_file, "\n\n")

# Generate comprehensive safety summary
cat("=== FULL POPULATION SAFETY RESULTS ===\n\n")

# Overall safety summary
ibr_patients <- safety_data[safety_data$treatment_arm == "Ibrutinib_420mg", ]
ctrl_patients <- safety_data[safety_data$treatment_arm == "Control", ]

cat("IBRUTINIB 420mg (n =", nrow(ibr_patients), "):\n")
cat("- Any bleeding:", sum(ibr_patients$any_bleeding), "(", round(mean(ibr_patients$any_bleeding)*100, 1), "%)\n")
cat("- Major bleeding:", sum(ibr_patients$major_bleeding), "(", round(mean(ibr_patients$major_bleeding)*100, 1), "%)\n")
cat("- CRNM bleeding:", sum(ibr_patients$crnm_bleeding), "(", round(mean(ibr_patients$crnm_bleeding)*100, 1), "%)\n")
cat("- Minor bleeding:", sum(ibr_patients$minor_bleeding), "(", round(mean(ibr_patients$minor_bleeding)*100, 1), "%)\n")
cat("- Fatal bleeding:", sum(ibr_patients$fatal_bleeding), "(", round(mean(ibr_patients$fatal_bleeding)*100, 1), "%)\n")
cat("- ICH:", sum(ibr_patients$ich_bleeding), "(", round(mean(ibr_patients$ich_bleeding)*100, 1), "%)\n")
cat("- GI bleeding:", sum(ibr_patients$gi_bleeding), "(", round(mean(ibr_patients$gi_bleeding)*100, 1), "%)\n")
cat("- Dose reduction:", sum(ibr_patients$dose_reduction), "(", round(mean(ibr_patients$dose_reduction)*100, 1), "%)\n")
cat("- Discontinuation:", sum(ibr_patients$permanent_discontinuation), "(", round(mean(ibr_patients$permanent_discontinuation)*100, 1), "%)\n")

cat("\nCONTROL (n =", nrow(ctrl_patients), "):\n")
cat("- Any bleeding:", sum(ctrl_patients$any_bleeding), "(", round(mean(ctrl_patients$any_bleeding)*100, 1), "%)\n")
cat("- Major bleeding:", sum(ctrl_patients$major_bleeding), "(", round(mean(ctrl_patients$major_bleeding)*100, 1), "%)\n")

# Risk ratio calculations
ibr_any_rate <- mean(ibr_patients$any_bleeding)
ctrl_any_rate <- mean(ctrl_patients$any_bleeding)
any_bleeding_rr <- ifelse(ctrl_any_rate > 0, ibr_any_rate / ctrl_any_rate, NA)

ibr_major_rate <- mean(ibr_patients$major_bleeding)
ctrl_major_rate <- mean(ctrl_patients$major_bleeding)
major_bleeding_rr <- ifelse(ctrl_major_rate > 0, ibr_major_rate / ctrl_major_rate, NA)

cat("\nRISK RATIOS:\n")
cat("- Any bleeding RR:", round(any_bleeding_rr, 2), "\n")
cat("- Major bleeding RR:", round(major_bleeding_rr, 2), "\n")

cat("\n=== FULL POPULATION SAFETY ANALYSIS COMPLETED ===\n")
cat("✓ All 5,000 patients analyzed with enhanced safety modeling\n")
cat("✓ CTCAE v5.0 grading applied\n")
cat("✓ Literature-based risk factors incorporated\n")
cat("✓ Dose modification patterns analyzed\n")
