# =============================================================================
# ML COMPUTATIONAL OPTIMIZATION AND PARALLEL PROCESSING
# =============================================================================
# This module implements:
# - Parallel processing for ML training
# - Memory optimization techniques
# - GPU acceleration (when available)
# - Efficient data structures
# - Performance monitoring and profiling
# - Automated hyperparameter optimization

# Load required libraries with error handling
required_packages <- c("parallel", "doParallel", "foreach", "caret", "randomForest", 
                      "xgboost", "glmnet", "pROC", "dplyr", "data.table", "Matrix")
optional_packages <- c("mlr3", "mlr3tuning", "paradox", "bbotk", "mice", "Boruta")

# Load required packages
for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
    stop(paste("Required package", pkg, "is not installed. Please install it using: install.packages('", pkg, "')"))
  }
}

# Load optional packages with fallbacks
use_mlr3 <- require(mlr3, quietly = TRUE)
use_mlr3tuning <- require(mlr3tuning, quietly = TRUE)
use_paradox <- require(paradox, quietly = TRUE)
use_bbotk <- require(bbotk, quietly = TRUE)
use_mice <- require(mice, quietly = TRUE)
use_boruta <- require(Boruta, quietly = TRUE)

if (!use_mice) {
  cat("mice package not available - using median imputation for missing values\n")
}
if (!use_boruta) {
  cat("Boruta package not available - using correlation-based feature selection\n")
}
if (!use_mlr3) {
  cat("mlr3 packages not available - using basic hyperparameter optimization\n")
}

# Additional package availability checks
cat("ML Optimization module loaded with the following capabilities:\n")
cat(paste("- Advanced imputation:", ifelse(use_mice, "Available (mice)", "Basic (median)\n")))
cat(paste("- Feature selection:", ifelse(use_boruta, "Available (Boruta)", "Basic (correlation)\n")))
cat(paste("- Hyperparameter optimization:", ifelse(use_mlr3, "Advanced (mlr3)", "Basic (grid search)\n")))

# Set up parallel processing
num_cores <- detectCores() - 1
registerDoParallel(cores = num_cores)
cat(sprintf("Using %d cores for parallel processing\n", num_cores))

set.seed(42)

# Source required scripts
if (file.exists("virtual_clinical_trial.r")) {
  source("virtual_clinical_trial.r")
} else {
  cat("Warning: virtual_clinical_trial.r not found. Some functions may not work properly.\n")
}

# =============================================================================
# PART 1: ADVANCED FEATURE ENGINEERING AND PREPROCESSING
# =============================================================================

#' Advanced Feature Engineering Pipeline
#' Implements comprehensive feature engineering with selection, transformation, and preprocessing
advanced_feature_engineering <- function(trial_data, target_vars = NULL) {
  
  cat("Starting advanced feature engineering pipeline...\n")
  
  # Step 1: Initial data preparation
  available_cols <- names(trial_data)
  
  # Define comprehensive feature set with fallback mappings
  base_features <- c(
    # Demographics
    "age", "sex", "weight", "height", "bmi", "ethnicity", "bsa",
    "creatinine_clearance",
    # Clinical variables
    "platelet_count", "hypertension", "diabetes", "cardiovascular_disease", 
    "atrial_fibrillation", "alt", "ast", "bilirubin",
    "anticoagulants", "antiplatelets", "cyp3a4_inhibitors", "cyp3a4_inducers", "ppi_use",
    "baseline_aggregation",
    # Genetic factors
    "cyp3a4_activity", "cyp3a5_activity", "metabolizer_phenotype", "plcg2_variants",
    # Treatment
    "treatment_arm", "cmax_nm", "auc_nm_h", "css_avg_nm"
  )
  
  # Column name mappings for compatibility
  column_mappings <- list(
    "bleeding_event" = c("bleeding_event_occurred", "bleeding_event"),
    "major_bleeding" = c("major_bleeding_events", "major_bleeding"),
    "bleeding_risk_score" = c("annual_bleeding_risk", "bleeding_risk_score")
  )
  
  # Select available features
  cols_to_select <- intersect(base_features, available_cols)
  features_df <- trial_data %>% dplyr::select(all_of(cols_to_select))
  
  # Add any mapped columns that exist
  for (mapped_name in names(column_mappings)) {
    mapped_cols <- column_mappings[[mapped_name]]
    found_col <- intersect(mapped_cols, available_cols)
    if (length(found_col) > 0 && !mapped_name %in% names(features_df)) {
      features_df[[mapped_name]] <- trial_data[[found_col[1]]]
    }
  }
  
  # Step 2: Advanced missing value imputation
  cat("  - Performing advanced missing value imputation...\n")
  features_df <- advanced_imputation(features_df)
  
  # Step 3: Feature encoding and transformation
  cat("  - Applying feature encoding and transformations...\n")
  features_df <- encode_categorical_features(features_df)
  
  # Step 4: Feature scaling and normalization
  cat("  - Normalizing and scaling features...\n")
  features_df <- normalize_features(features_df)
  
  # Step 5: Feature creation and engineering
  cat("  - Creating engineered features...\n")
  features_df <- create_engineered_features(features_df)
  
  # Step 6: Feature selection
  if (!is.null(target_vars)) {
    cat("  - Performing feature selection...\n")
    features_df <- select_optimal_features(features_df, trial_data, target_vars)
  }
  
  # Step 7: Dimensionality reduction (if needed)
  if (ncol(features_df) > 50) {
    cat("  - Applying dimensionality reduction...\n")
    features_df <- apply_dimensionality_reduction(features_df)
  }
  
  cat(sprintf("Feature engineering complete. Final features: %d, Samples: %d\n", 
              ncol(features_df), nrow(features_df)))
  
  return(features_df)
}

#' Advanced Missing Value Imputation
advanced_imputation <- function(data) {
  
  # Calculate missing value percentages
  missing_pct <- sapply(data, function(x) sum(is.na(x)) / length(x))
  
  # Remove features with >50% missing values
  data <- data[, missing_pct <= 0.5]
  
  # For remaining missing values, use multiple imputation for critical features
  numeric_cols <- sapply(data, is.numeric)
  categorical_cols <- !numeric_cols
  
  # Multiple imputation for numeric variables with <30% missing
  if (any(numeric_cols)) {
    numeric_data <- data[, numeric_cols, drop = FALSE]
    missing_numeric <- sapply(numeric_data, function(x) sum(is.na(x)) > 0)
    
    if (any(missing_numeric)) {
      # Use MICE for multiple imputation if available
      if (use_mice) {
        tryCatch({
          mice_result <- mice(numeric_data, m = 5, method = 'pmm', printFlag = FALSE)
          numeric_data <- complete(mice_result)
        }, error = function(e) {
          cat("MICE imputation failed, using median imputation\n")
          # Fallback to median imputation
          for (col in names(numeric_data)) {
            if (any(is.na(numeric_data[[col]]))) {
              median_val <- median(numeric_data[[col]], na.rm = TRUE)
              if (!is.na(median_val)) {
                numeric_data[[col]][is.na(numeric_data[[col]])] <- median_val
              }
            }
          }
        })
      } else {
        # Fallback to median imputation
        for (col in names(numeric_data)) {
          if (any(is.na(numeric_data[[col]]))) {
            median_val <- median(numeric_data[[col]], na.rm = TRUE)
            if (!is.na(median_val)) {
              numeric_data[[col]][is.na(numeric_data[[col]])] <- median_val
            }
          }
        }
      }
    }
    data[, numeric_cols] <- numeric_data
  }
  
  # Mode imputation for categorical variables
  if (any(categorical_cols)) {
    categorical_data <- data[, categorical_cols, drop = FALSE]
    for (col in names(categorical_data)) {
      if (any(is.na(categorical_data[[col]]))) {
        mode_vals <- table(categorical_data[[col]])
        if (length(mode_vals) > 0) {
          mode_val <- names(sort(mode_vals, decreasing = TRUE))[1]
          categorical_data[[col]][is.na(categorical_data[[col]])] <- mode_val
        }
      }
    }
    data[, categorical_cols] <- categorical_data
  }
  
  return(data)
}

#' Categorical Feature Encoding
encode_categorical_features <- function(data) {
  
  # One-hot encoding for categorical variables
  if ("sex" %in% names(data)) {
    data$sex_male <- as.numeric(data$sex == "Male")
    data$sex_female <- as.numeric(data$sex == "Female")
    data <- data %>% dplyr::select(-sex)
  }
  
  if ("ethnicity" %in% names(data)) {
    ethnicities <- unique(data$ethnicity)
    for (eth in ethnicities) {
      col_name <- paste0("ethnicity_", gsub(" ", "_", tolower(eth)))
      data[[col_name]] <- as.numeric(data$ethnicity == eth)
    }
    data <- data %>% dplyr::select(-ethnicity)
  }
  
  if ("metabolizer_phenotype" %in% names(data)) {
    phenotypes <- unique(data$metabolizer_phenotype)
    for (phen in phenotypes) {
      col_name <- paste0("metabolizer_", tolower(phen))
      data[[col_name]] <- as.numeric(data$metabolizer_phenotype == phen)
    }
    data <- data %>% dplyr::select(-metabolizer_phenotype)
  }
  
  if ("treatment_arm" %in% names(data)) {
    treatments <- unique(data$treatment_arm)
    for (treat in treatments) {
      col_name <- paste0("treatment_", gsub("[^A-Za-z0-9]", "_", tolower(treat)))
      data[[col_name]] <- as.numeric(data$treatment_arm == treat)
    }
    data <- data %>% dplyr::select(-treatment_arm)
  }
  
  # Convert logical to numeric
  logical_cols <- sapply(data, is.logical)
  data[logical_cols] <- lapply(data[logical_cols], as.numeric)
  
  return(data)
}

#' Feature Normalization and Scaling
normalize_features <- function(data) {
  
  numeric_cols <- sapply(data, is.numeric)
  
  if (any(numeric_cols)) {
    # Z-score normalization for continuous variables
    data[numeric_cols] <- scale(data[numeric_cols])
    
    # Handle any remaining NaN or Inf values
    data[numeric_cols][is.nan(as.matrix(data[numeric_cols]))] <- 0
    data[numeric_cols][is.infinite(as.matrix(data[numeric_cols]))] <- 0
  }
  
  return(data)
}

#' Create Engineered Features
create_engineered_features <- function(data) {
  
  # Interaction features
  if (all(c("age", "weight") %in% names(data))) {
    data$age_weight_interaction <- data$age * data$weight
  }
  
  if (all(c("bmi", "age") %in% names(data))) {
    data$bmi_age_interaction <- data$bmi * data$age
  }
  
  # Polynomial features for key variables
  if ("age" %in% names(data)) {
    data$age_squared <- data$age^2
    data$age_cubed <- data$age^3
  }
  
  if ("bmi" %in% names(data)) {
    data$bmi_squared <- data$bmi^2
  }
  
  # Ratio features
  if (all(c("weight", "height") %in% names(data))) {
    data$weight_height_ratio <- data$weight / (data$height + 1e-8)
  }
  
  if (all(c("cmax_nm", "auc_nm_h") %in% names(data))) {
    data$cmax_auc_ratio <- data$cmax_nm / (data$auc_nm_h + 1e-8)
  }
  
  # Binning for continuous variables
  if ("age" %in% names(data)) {
    data$age_group_young <- as.numeric(data$age < -0.5)  # Normalized scale
    data$age_group_middle <- as.numeric(data$age >= -0.5 & data$age <= 0.5)
    data$age_group_old <- as.numeric(data$age > 0.5)
  }
  
  return(data)
}

#' Feature Selection using Multiple Methods
select_optimal_features <- function(features_df, trial_data, target_vars) {
  
  selected_features <- list()
  
  for (target_var in target_vars) {
    if (target_var %in% names(trial_data)) {
      target_values <- trial_data[[target_var]]
      
      # Remove rows with missing target values
      valid_idx <- !is.na(target_values)
      current_features <- features_df[valid_idx, ]
      current_target <- target_values[valid_idx]
      
      if (length(unique(current_target)) > 1) {
        
        # Method 1: Correlation-based selection
        if (is.numeric(current_target)) {
          correlations <- abs(cor(current_features, current_target, use = "complete.obs"))
          corr_selected <- names(correlations[correlations > 0.1 & !is.na(correlations)])
        } else {
          # For categorical targets, use chi-square or mutual information
          corr_selected <- names(current_features)[1:min(20, ncol(current_features))]
        }
        
        # Method 2: Boruta feature selection (if available)
        if (use_boruta && nrow(current_features) > 50) {
          tryCatch({
            boruta_result <- Boruta(current_features, current_target, doTrace = 0)
            boruta_selected <- names(current_features)[which(boruta_result$finalDecision == "Confirmed")]
          }, error = function(e) {
            boruta_selected <- corr_selected
          })
        } else {
          boruta_selected <- corr_selected
        }
        
        # Method 3: Recursive Feature Elimination
        tryCatch({
          ctrl <- rfeControl(functions = rfFuncs, method = "cv", number = 3, verbose = FALSE)
          rfe_result <- rfe(current_features, current_target, sizes = c(5, 10, 15, 20), rfeControl = ctrl)
          rfe_selected <- rfe_result$optVariables
        }, error = function(e) {
          rfe_selected <- corr_selected
        })
        
        # Combine selections
        combined_selected <- unique(c(corr_selected, boruta_selected, rfe_selected))
        selected_features[[target_var]] <- combined_selected
      }
    }
  }
  
  # Take union of all selected features
  all_selected <- unique(unlist(selected_features))
  
  # Ensure we have at least some features
  if (length(all_selected) == 0) {
    all_selected <- names(features_df)[1:min(20, ncol(features_df))]
  }
  
  # Return selected features
  return(features_df[, intersect(all_selected, names(features_df)), drop = FALSE])
}

#' Dimensionality Reduction
apply_dimensionality_reduction <- function(data, n_components = 0.95) {
  
  # PCA for dimensionality reduction
  if (ncol(data) > 10) {
    pca_result <- prcomp(data, center = TRUE, scale. = TRUE)
    
    # Determine number of components to retain
    if (n_components < 1) {
      # Retain components explaining n_components proportion of variance
      cumvar <- cumsum(pca_result$sdev^2) / sum(pca_result$sdev^2)
      n_comp <- which(cumvar >= n_components)[1]
    } else {
      n_comp <- min(n_components, ncol(data))
    }
    
    # Transform data
    pca_data <- as.data.frame(pca_result$x[, 1:n_comp])
    colnames(pca_data) <- paste0("PC", 1:n_comp)
    
    cat(sprintf("  - PCA reduced dimensions from %d to %d (%.1f%% variance retained)\n", 
                ncol(data), n_comp, cumvar[n_comp] * 100))
    
    return(pca_data)
  }
  
  return(data)
}

# =============================================================================
# PART 2: HYPERPARAMETER OPTIMIZATION
# =============================================================================

#' Comprehensive Hyperparameter Optimization
#' Implements Grid Search, Random Search, and Bayesian Optimization
optimize_hyperparameters <- function(X_train, y_train, X_val, y_val, 
                                   algorithm = "rf", optimization_method = "bayesian",
                                   n_trials = 50, cv_folds = 5) {
  
  cat(sprintf("Optimizing hyperparameters for %s using %s optimization...\n", 
              algorithm, optimization_method))
  
  if (optimization_method == "grid_search") {
    return(grid_search_optimization(X_train, y_train, X_val, y_val, algorithm))
  } else if (optimization_method == "random_search") {
    return(random_search_optimization(X_train, y_train, X_val, y_val, algorithm, n_trials))
  } else if (optimization_method == "bayesian") {
    return(bayesian_optimization(X_train, y_train, X_val, y_val, algorithm, n_trials))
  }
}

#' Grid Search Optimization
grid_search_optimization <- function(X_train, y_train, X_val, y_val, algorithm) {
  
  if (algorithm == "rf") {
    # Random Forest parameter grid
    param_grid <- expand.grid(
      ntree = c(100, 300, 500),
      mtry = c(sqrt(ncol(X_train)), ncol(X_train)/3, ncol(X_train)/2),
      nodesize = c(1, 3, 5),
      maxnodes = c(NULL, 50, 100)
    )
  } else if (algorithm == "xgb") {
    # XGBoost parameter grid
    param_grid <- expand.grid(
      nrounds = c(100, 200, 300),
      max_depth = c(3, 6, 9),
      eta = c(0.01, 0.1, 0.3),
      subsample = c(0.8, 0.9, 1.0),
      colsample_bytree = c(0.8, 0.9, 1.0)
    )
  }
  
  best_score <- -Inf
  best_params <- NULL
  
  for (i in 1:nrow(param_grid)) {
    params <- as.list(param_grid[i, ])
    score <- evaluate_model_with_params(X_train, y_train, X_val, y_val, algorithm, params)
    
    if (score > best_score) {
      best_score <- score
      best_params <- params
    }
  }
  
  return(list(best_params = best_params, best_score = best_score))
}

#' Random Search Optimization
random_search_optimization <- function(X_train, y_train, X_val, y_val, algorithm, n_trials) {
  
  best_score <- -Inf
  best_params <- NULL
  
  for (trial in 1:n_trials) {
    if (algorithm == "rf") {
      params <- list(
        ntree = sample(c(100, 200, 300, 500), 1),
        mtry = sample(c(max(1, floor(sqrt(ncol(X_train)))), 
                       max(1, floor(ncol(X_train)/3)), 
                       max(1, floor(ncol(X_train)/2))), 1),
        nodesize = sample(c(1, 3, 5, 10), 1)
      )
    } else if (algorithm == "xgb") {
      params <- list(
        nrounds = sample(50:500, 1),
        max_depth = sample(3:10, 1),
        eta = runif(1, 0.01, 0.3),
        subsample = runif(1, 0.6, 1.0),
        colsample_bytree = runif(1, 0.6, 1.0)
      )
    }
    
    score <- evaluate_model_with_params(X_train, y_train, X_val, y_val, algorithm, params)
    
    if (score > best_score) {
      best_score <- score
      best_params <- params
    }
  }
  
  return(list(best_params = best_params, best_score = best_score))
}

#' Bayesian Optimization (simplified implementation)
bayesian_optimization <- function(X_train, y_train, X_val, y_val, algorithm, n_trials) {
  
  # Check if mlr3 packages are available for true Bayesian optimization
  if (!use_mlr3 || !use_mlr3tuning || !use_paradox || !use_bbotk) {
    cat("mlr3 packages not available, using adaptive random search instead\n")
  }
  
  # For simplicity, use random search with adaptive sampling
  # In practice, you would use packages like mlrMBO or rBayesianOptimization
  
  scores <- numeric(n_trials)
  params_list <- list()
  
  best_score <- -Inf
  best_params <- NULL
  
  for (trial in 1:n_trials) {
    if (algorithm == "rf") {
      # Adaptive parameter sampling based on previous results
      if (trial > 10) {
        # Focus on promising regions
        top_indices <- order(scores[1:(trial-1)], decreasing = TRUE)[1:min(5, trial-1)]
        base_params <- params_list[[sample(top_indices, 1)]]
        
        params <- list(
          ntree = max(50, base_params$ntree + sample(-100:100, 1)),
          mtry = max(1, min(ncol(X_train), base_params$mtry + sample(-2:2, 1))),
          nodesize = max(1, base_params$nodesize + sample(-2:2, 1))
        )
      } else {
        params <- list(
          ntree = sample(c(100, 200, 300, 500), 1),
          mtry = sample(c(max(1, floor(sqrt(ncol(X_train)))), 
                         max(1, floor(ncol(X_train)/3)), 
                         max(1, floor(ncol(X_train)/2))), 1),
          nodesize = sample(c(1, 3, 5, 10), 1)
        )
      }
    }
    
    score <- evaluate_model_with_params(X_train, y_train, X_val, y_val, algorithm, params)
    scores[trial] <- score
    params_list[[trial]] <- params
    
    if (score > best_score) {
      best_score <- score
      best_params <- params
    }
  }
  
  return(list(best_params = best_params, best_score = best_score))
}

#' Evaluate Model with Given Parameters
evaluate_model_with_params <- function(X_train, y_train, X_val, y_val, algorithm, params) {
  
  tryCatch({
    if (algorithm == "rf") {
      model <- randomForest(x = X_train, y = as.factor(y_train),
                           ntree = params$ntree,
                           mtry = params$mtry,
                           nodesize = params$nodesize)
      
      if (length(unique(y_train)) > 1) {
        pred <- predict(model, X_val, type = "prob")[, 2]
        if (length(unique(y_val)) > 1) {
          roc_obj <- roc(y_val, pred, quiet = TRUE)
          return(as.numeric(roc_obj$auc))
        } else {
          return(mean((pred > 0.5) == y_val))
        }
      } else {
        return(0)
      }
      
    } else if (algorithm == "xgb") {
      dtrain <- xgb.DMatrix(data = as.matrix(X_train), label = y_train)
      dval <- xgb.DMatrix(data = as.matrix(X_val), label = y_val)
      
      model <- xgb.train(
        params = list(
          objective = "binary:logistic",
          max_depth = params$max_depth,
          eta = params$eta,
          subsample = params$subsample,
          colsample_bytree = params$colsample_bytree
        ),
        data = dtrain,
        nrounds = params$nrounds,
        verbose = 0
      )
      
      pred <- predict(model, dval)
      if (length(unique(y_val)) > 1) {
        roc_obj <- roc(y_val, pred, quiet = TRUE)
        return(as.numeric(roc_obj$auc))
      } else {
        return(mean((pred > 0.5) == y_val))
      }
    }
  }, error = function(e) {
    return(0)
  })
}

# =============================================================================
# PARALLEL HYPERPARAMETER TUNING
# =============================================================================

#' Parallel Hyperparameter Tuning
#' Wrapper function for parallel hyperparameter optimization
parallel_hyperparameter_tuning <- function(X_train, y_train, X_test, y_test,
                                          model_type = "xgboost", n_iter = 20,
                                          parallel_setup = NULL, verbose = TRUE) {
  
  if (verbose) {
    cat(sprintf("Parallel hyperparameter tuning for %s (%d iterations)...\n", model_type, n_iter))
  }
  
  # Create validation split from training data
  val_idx <- sample(nrow(X_train), size = floor(0.2 * nrow(X_train)))
  X_val <- X_train[val_idx, , drop = FALSE]
  y_val <- y_train[val_idx]
  X_train_sub <- X_train[-val_idx, , drop = FALSE]
  y_train_sub <- y_train[-val_idx]
  
  # Use existing optimization function
  if (model_type == "xgboost") {
    opt_result <- optimize_hyperparameters(
      X_train_sub, y_train_sub, X_val, y_val,
      algorithm = "xgb",
      optimization_method = "random_search",
      n_trials = n_iter
    )
  } else if (model_type == "randomForest") {
    opt_result <- optimize_hyperparameters(
      X_train_sub, y_train_sub, X_val, y_val,
      algorithm = "rf",
      optimization_method = "random_search",
      n_trials = n_iter
    )
  } else {
    stop(sprintf("Unsupported model type: %s", model_type))
  }
  
  # Train final model with best parameters on full training set
  if (model_type == "xgboost") {
    dtrain <- xgb.DMatrix(data = as.matrix(X_train), label = y_train)
    dtest <- xgb.DMatrix(data = as.matrix(X_test), label = y_test)
    
    final_model <- xgb.train(
      params = list(
        objective = "binary:logistic",
        max_depth = opt_result$best_params$max_depth,
        eta = opt_result$best_params$eta,
        subsample = opt_result$best_params$subsample,
        colsample_bytree = opt_result$best_params$colsample_bytree
      ),
      data = dtrain,
      nrounds = opt_result$best_params$nrounds,
      verbose = 0
    )
    
    predictions <- predict(final_model, dtest)
    
  } else if (model_type == "randomForest") {
    final_model <- randomForest(
      x = X_train, y = as.factor(y_train),
      ntree = opt_result$best_params$ntree,
      mtry = opt_result$best_params$mtry,
      nodesize = opt_result$best_params$nodesize
    )
    
    predictions <- predict(final_model, X_test, type = "prob")[, 2]
  }
  
  # Load performance utilities if not already loaded
  if (!exists("calculate_standardized_performance")) {
    source("ml_performance_utils.r")
  }
  
  # Calculate standardized performance metrics
  performance <- calculate_standardized_performance(
    y_true = y_test,
    y_pred = predictions,
    threshold = 0.5,
    task_type = "classification"
  )
  
  # Convert to expected format for backward compatibility
  performance <- list(
    AUC = ifelse(is.null(performance$auc), 0.5, performance$auc),
    Accuracy = ifelse(is.null(performance$accuracy), 0.5, performance$accuracy),
    Sensitivity = ifelse(is.null(performance$sensitivity), 0, performance$sensitivity),
    Specificity = ifelse(is.null(performance$specificity), 0, performance$specificity)
  )
  
  if (verbose) {
    cat(sprintf("  Best %s AUC: %.4f\n", model_type, performance$AUC))
  }
  
  return(list(
    model = final_model,
    predictions = predictions,
    performance = performance,
    best_params = opt_result$best_params,
    optimization_history = opt_result
  ))
}

cat("ML optimization module loaded successfully!\n")
cat("Available functions:\n")
cat("  - advanced_feature_engineering(): Comprehensive feature engineering pipeline\n")
cat("  - optimize_hyperparameters(): Hyperparameter optimization with multiple methods\n")
cat("  - parallel_hyperparameter_tuning(): Parallel hyperparameter tuning wrapper\n")
cat("  - Next: Load ensemble methods and regularization techniques\n")