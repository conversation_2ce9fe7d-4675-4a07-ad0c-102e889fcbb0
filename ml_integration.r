# =============================================================================
# ML INTEGRATION SCRIPT
# =============================================================================
# This script integrates all ML optimization modules and demonstrates
# their usage with the Ibrutinib QSP model data

# Load file utilities first
if (file.exists("ml_file_utils.r")) {
  source("ml_file_utils.r")
} else {
  cat("Warning: ml_file_utils.r not found. Using basic file operations.\n")
}

# Load required libraries with error handling
required_packages <- c("randomForest", "xgboost", "caret", "pROC", "dplyr", "ggplot2")
optional_packages <- c("corrplot", "gridExtra")

# Load required packages
for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
    stop(paste("Required package", pkg, "is not installed. Please install it using: install.packages('", pkg, "')"))
  }
}

# Load optional packages with fallbacks
use_corrplot <- require(corrplot, quietly = TRUE)
use_gridextra <- require(gridExtra, quietly = TRUE)

if (!use_corrplot) {
  cat("corrplot package not available - using ggplot2 for correlation plots\n")
}
if (!use_gridextra) {
  cat("gridExtra package not available - using basic plot arrangement\n")
}

cat("ML Integration module loaded successfully\n")

# Source all optimization modules with error handling
ml_modules <- c(
  "ml_optimization.r",
  "ml_ensemble.r", 
  "ml_response_prediction.r",
  "ml_demo.r",
  "generate_ml_results_plots.r"
)

for (module in ml_modules) {
  if (file.exists(module)) {
    cat(paste("Loading", module, "...\n"))
    source(module)
  } else {
    cat(paste("Warning:", module, "not found. Some functions may not be available.\n"))
  }
}

cat("ML Integration System Loaded\n")
cat("=============================\n")

# =============================================================================
# PARALLEL PROCESSING HELPER FUNCTIONS
# =============================================================================

#' Setup Parallel Processing
setup_parallel_processing <- function(n_cores = NULL, verbose = TRUE) {
  if (is.null(n_cores)) {
    n_cores <- parallel::detectCores() - 1
  }
  
  if (verbose) {
    cat(sprintf("Setting up parallel processing with %d cores\n", n_cores))
  }
  
  return(list(n_cores = n_cores, cluster = NULL))
}

#' Cleanup Parallel Processing
cleanup_parallel_processing <- function(parallel_setup) {
  if (!is.null(parallel_setup$cluster)) {
    parallel::stopCluster(parallel_setup$cluster)
  }
}

#' Advanced Data Preprocessing
advanced_data_preprocessing <- function(trial_data, target_variable, handle_missing = TRUE, 
                                      normalize_features = TRUE, create_interactions = FALSE, 
                                      verbose = TRUE) {
  if (verbose) {
    cat(sprintf("Preprocessing data for target: %s\n", target_variable))
  }
  
  # Use existing preprocessing function
  processed_list <- preprocess_trial_data(trial_data)
  
  # Extract the ml_data component which contains all variables
  processed_data <- processed_list$ml_data
  
  # Check if target variable exists after preprocessing
  if (!target_variable %in% names(processed_data)) {
    if (verbose) {
      cat(sprintf("Warning: Target variable '%s' not found. Available variables: %s\n", 
                  target_variable, paste(names(processed_data), collapse=", ")))
    }
    return(NULL)
  }
  
  # Ensure target variable is properly formatted
  if (target_variable %in% names(processed_data)) {
    # Convert to binary if needed
    if (is.numeric(processed_data[[target_variable]])) {
      processed_data[[target_variable]] <- as.numeric(processed_data[[target_variable]] > 0)
    }
    
    # Handle missing values in target
    if (any(is.na(processed_data[[target_variable]]))) {
      processed_data[[target_variable]][is.na(processed_data[[target_variable]])] <- 0
    }
  }
  
  if (verbose) {
    cat(sprintf("Preprocessing complete. Features: %d, Samples: %d\n", 
                ncol(processed_data) - 1, nrow(processed_data)))
    cat(sprintf("Target variable '%s' summary: %d positive cases out of %d total\n",
                target_variable, sum(processed_data[[target_variable]], na.rm = TRUE), nrow(processed_data)))
  }
  
  return(processed_data)
}

# =============================================================================
# MAIN ML PIPELINE FUNCTION
# =============================================================================

#' Complete ML Pipeline
#' Integrates all 10 optimization strategies into a comprehensive pipeline
run_ml_pipeline <- function(trial_data = NULL, 
                                   targets = c("bleeding_risk", "major_bleeding", "collagen_inhibition"),
                                   optimization_level = "full",
                                   parallel_cores = NULL,
                                   save_results = TRUE,
                                   verbose = TRUE) {
  
  cat("\n" %+% "=" %+% rep("=", 60) %+% "\n")
  cat("MACHINE LEARNING OPTIMIZATION PIPELINE\n")
  cat("=" %+% rep("=", 60) %+% "\n")
  
  # Load data if not provided
  if (is.null(trial_data)) {
    cat("Loading trial data...\n")
    if (file.exists("virtual_clinical_trial_results.csv")) {
      trial_data <- safe_csv_load("virtual_clinical_trial_results.csv")
    } else if (file.exists("enhanced_virtual_clinical_trial_results.csv")) {
      trial_data <- safe_csv_load("enhanced_virtual_clinical_trial_results.csv")
    } else {
      cat("Error: Trial data not found. Please run virtual clinical trial first.\n")
      return(NULL)
    }
  }
  
  # Apply column mappings for compatibility
  column_mappings <- list(
    "bleeding_event" = c("bleeding_event_occurred", "bleeding_event"),
    "major_bleeding" = c("major_bleeding_events", "major_bleeding"),
    "bleeding_risk_score" = c("annual_bleeding_risk", "bleeding_risk_score"),
    "bleeding_risk" = c("annual_bleeding_risk", "bleeding_risk_score", "bleeding_risk")
  )
  
  available_cols <- names(trial_data)
  for (mapped_name in names(column_mappings)) {
    mapped_cols <- column_mappings[[mapped_name]]
    found_col <- intersect(mapped_cols, available_cols)
    if (length(found_col) > 0 && !mapped_name %in% names(trial_data)) {
      trial_data[[mapped_name]] <- trial_data[[found_col[1]]]
    }
  }
  
  # Initialize results storage
  ml_results <- list()
  
  # Setup parallel processing
  parallel_setup <- setup_parallel_processing(n_cores = parallel_cores, verbose = verbose)
  
  # Process each target
  for (target in targets) {
    cat(sprintf("\n" %+% "=" %+% rep("=", 40) %+% "\n"))
    cat(sprintf("PROCESSING TARGET: %s\n", toupper(target)))
    cat("=" %+% rep("=", 40) %+% "\n")
    
    # Step 1: Advanced Data Preprocessing
    cat("\n1. ADVANCED DATA PREPROCESSING\n")
    cat("------------------------------\n")
    
    processed_data <- advanced_data_preprocessing(
      trial_data, 
      target_variable = target,
      handle_missing = TRUE,
      normalize_features = TRUE,
      create_interactions = (optimization_level == "full"),
      verbose = verbose
    )
    
    if (is.null(processed_data)) {
      cat(sprintf("Warning: Could not process data for target %s, skipping...\n", target))
      next
    }
    
    # Extract features and target from processed data
    y <- processed_data[[target]]
    X <- processed_data[, !names(processed_data) %in% target, drop = FALSE]
    
    # Check if we have enough data and sufficient positive cases
    positive_cases <- sum(y, na.rm = TRUE)
    if (nrow(X) < 50 || length(unique(y)) < 2 || positive_cases < 5) {
      cat(sprintf("Warning: Insufficient data for target %s (positive cases: %d), skipping...\n", target, positive_cases))
      next
    }
    
    # Step 2: Advanced Feature Engineering
    cat("\n2. ADVANCED FEATURE ENGINEERING\n")
    cat("-------------------------------\n")
    
    # Prepare data for feature engineering (combine X and y back into trial_data format)
    trial_data_subset <- X
    trial_data_subset[[target]] <- y
    
    # Use the advanced_feature_engineering function from ml_demo.r (loaded last)
    X_engineered <- advanced_feature_engineering(
      data = trial_data_subset,
      target_col = target,
      verbose = verbose
    )
    
    feature_importance <- NULL  # Feature importance not returned by this function
    
    # Step 3: Handle Class Imbalance
    cat("\n3. CLASS IMBALANCE HANDLING\n")
    cat("---------------------------\n")
    
    # Check class distribution
    class_dist <- table(y)
    minority_ratio <- min(class_dist) / sum(class_dist)
    
    if (minority_ratio < 0.3) {
      cat(sprintf("Class imbalance detected (minority ratio: %.2f), applying SMOTE...\n", minority_ratio))
      balanced_data <- handle_class_imbalance(X_engineered, y, method = "smote")
      X_balanced <- balanced_data$X_train
      y_balanced <- balanced_data$y_train
    } else {
      cat("Classes are reasonably balanced, no resampling needed.\n")
      X_balanced <- X_engineered
      y_balanced <- y
    }
    
    # Step 4: Train-Test Split
    cat("\n4. DATA SPLITTING\n")
    cat("-----------------\n")
    
    set.seed(42)
    train_idx <- createDataPartition(y_balanced, p = 0.8, list = FALSE)
    
    X_train <- X_balanced[train_idx, ]
    y_train <- y_balanced[train_idx]
    X_test <- X_balanced[-train_idx, ]
    y_test <- y_balanced[-train_idx]
    
    cat(sprintf("Training set: %d samples\n", nrow(X_train)))
    cat(sprintf("Test set: %d samples\n", nrow(X_test)))
    
    # Step 5: Model Training with Optimization
    cat("\n5. ADVANCED MODEL TRAINING\n")
    cat("--------------------------\n")
    
    target_results <- list()
    
    # 5a. Regularized Models
    cat("\n5a. Regularized Learning\n")
    regularized_result <- regularized_learning(
      X_train, y_train, X_test, y_test,
      regularization_type = "elastic_net"
    )
    target_results$regularized <- regularized_result
    
    # 5b. Ensemble Methods
    cat("\n5b. Ensemble Methods\n")
    
    # Stacking Ensemble
    stacking_result <- advanced_ensemble_learning(
      X_train, y_train, X_test, y_test,
      ensemble_method = "stacking",
      base_learners = c("rf", "xgb", "glm"),
      meta_learner = "glm"
    )
    target_results$stacking <- stacking_result
    
    # Voting Ensemble
    voting_result <- advanced_ensemble_learning(
      X_train, y_train, X_test, y_test,
      ensemble_method = "voting",
      base_learners = c("rf", "xgb")
    )
    target_results$voting <- voting_result
    
    # 5c. Hyperparameter Optimization
    if (optimization_level %in% c("medium", "full")) {
      cat("\n5c. Hyperparameter Optimization\n")
      
      # XGBoost optimization
      xgb_opt_result <- parallel_hyperparameter_tuning(
        X_train, y_train, X_test, y_test,
        model_type = "xgboost",
        n_iter = ifelse(optimization_level == "full", 50, 20),
        parallel_setup = parallel_setup,
        verbose = verbose
      )
      target_results$xgb_optimized <- xgb_opt_result
      
      # Random Forest optimization
      rf_opt_result <- parallel_hyperparameter_tuning(
        X_train, y_train, X_test, y_test,
        model_type = "randomForest",
        n_iter = ifelse(optimization_level == "full", 30, 15),
        parallel_setup = parallel_setup,
        verbose = verbose
      )
      target_results$rf_optimized <- rf_opt_result
    }
    
    # Step 6: Advanced Cross-Validation
    cat("\n6. ADVANCED CROSS-VALIDATION\n")
    cat("----------------------------\n")
    
    # Define model function for CV
    rf_model_func <- function(X_train, y_train) {
      randomForest(x = X_train, y = as.factor(y_train), ntree = 200)
    }
    
    cv_result <- advanced_cross_validation(
      X_balanced, y_balanced,
      model_func = rf_model_func,
      cv_method = "stratified_kfold",
      k = 5, repeats = 2
    )
    
    target_results$cross_validation <- cv_result
    
    # Step 7: Performance Evaluation
    cat("\n7. PERFORMANCE EVALUATION\n")
    cat("-------------------------\n")
    
    # Compare all models
    model_comparison <- data.frame(
      Model = character(),
      AUC = numeric(),
      Accuracy = numeric(),
      Sensitivity = numeric(),
      Specificity = numeric(),
      stringsAsFactors = FALSE
    )
    
    for (model_name in names(target_results)) {
      if ("performance" %in% names(target_results[[model_name]])) {
        perf <- target_results[[model_name]]$performance
        model_comparison <- rbind(model_comparison, data.frame(
          Model = model_name,
          AUC = ifelse(is.null(perf$auc) || is.na(perf$auc), 0, perf$auc),
          Accuracy = ifelse(is.null(perf$accuracy) || is.na(perf$accuracy), 0, perf$accuracy),
          Sensitivity = ifelse(is.null(perf$sensitivity) || is.na(perf$sensitivity), 0, perf$sensitivity),
          Specificity = ifelse(is.null(perf$specificity) || is.na(perf$specificity), 0, perf$specificity)
        ))
      }
    }
    
    # Find best model
    if (nrow(model_comparison) > 0) {
      best_model_idx <- which.max(model_comparison$AUC)
      best_model_name <- model_comparison$Model[best_model_idx]
      best_performance <- model_comparison[best_model_idx, ]
      
      cat(sprintf("\nBEST MODEL FOR %s: %s\n", toupper(target), best_model_name))
      cat(sprintf("AUC: %.4f, Accuracy: %.4f, Sensitivity: %.4f, Specificity: %.4f\n",
                  best_performance$AUC, best_performance$Accuracy,
                  best_performance$Sensitivity, best_performance$Specificity))
      
      # Store results
      ml_results[[target]] <- list(
        best_model = best_model_name,
        best_performance = best_performance,
        all_results = target_results,
        model_comparison = model_comparison,
        feature_importance = feature_importance,
        cv_results = cv_result
      )
    } else {
      cat(sprintf("Warning: No valid results for target %s\n", target))
    }
  }
  
  # Cleanup parallel processing
  cleanup_parallel_processing(parallel_setup)
  
  # Step 8: Generate Summary Report
  cat("\n" %+% "=" %+% rep("=", 60) %+% "\n")
  cat("ML PIPELINE SUMMARY\n")
  cat("=" %+% rep("=", 60) %+% "\n")
  
  summary_report <- generate_summary(ml_results)
  
  # Save results if requested
  if (save_results) {
    save_ml_results(ml_results, summary_report)
  }
  
  return(list(
    results = ml_results,
    summary = summary_report
  ))
}

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

#' Generate Summary Report
generate_summary <- function(ml_results) {
  
  summary_data <- data.frame(
    Target = character(),
    Best_Model = character(),
    AUC = numeric(),
    Accuracy = numeric(),
    Improvement_vs_Baseline = character(),
    stringsAsFactors = FALSE
  )
  
  for (target in names(ml_results)) {
    result <- ml_results[[target]]
    
    if (!is.null(result$best_performance)) {
      summary_data <- rbind(summary_data, data.frame(
        Target = target,
        Best_Model = result$best_model,
        AUC = result$best_performance$AUC,
        Accuracy = result$best_performance$Accuracy,
        Improvement_vs_Baseline = "Optimized",  # Could compare with baseline if available
        stringsAsFactors = FALSE
      ))
    }
  }
  
  # Print summary
  cat("\nPERFORMANCE SUMMARY:\n")
  print(summary_data)
  
  # Calculate overall statistics
  if (nrow(summary_data) > 0) {
    avg_auc <- mean(summary_data$AUC, na.rm = TRUE)
    avg_accuracy <- mean(summary_data$Accuracy, na.rm = TRUE)
    
    cat(sprintf("\nOVERALL STATISTICS:\n"))
    cat(sprintf("Average AUC: %.4f\n", avg_auc))
    cat(sprintf("Average Accuracy: %.4f\n", avg_accuracy))
    
    # Model frequency
    model_freq <- table(summary_data$Best_Model)
    cat("\nBEST MODEL FREQUENCY:\n")
    print(model_freq)
  }
  
  return(summary_data)
}

#' Save ML Results
save_ml_results <- function(ml_results, summary_report) {
  
  # Save main results using safe file utilities
  results_dir <- get_ml_results_dir()
  safe_file_save(ml_results, build_path(results_dir, "ml_results.rds"))
  
  # Save summary as CSV
  safe_csv_save(summary_report, build_path(results_dir, "ml_summary.csv"))
  
  # Create detailed report
  sink("ml_detailed_report.txt")
  
  cat("MACHINE LEARNING DETAILED REPORT\n")
  cat("================================\n")
  cat(sprintf("Generated: %s\n\n", Sys.time()))
  
  for (target in names(ml_results)) {
    cat(sprintf("\nTARGET: %s\n", toupper(target)))
    cat(rep("-", nchar(target) + 8), "\n")
    
    result <- ml_results[[target]]
    
    if (!is.null(result$model_comparison)) {
      cat("\nModel Comparison:\n")
      print(result$model_comparison)
    }
    
    if (!is.null(result$cv_results)) {
      cat(sprintf("\nCross-Validation Results:\n"))
      cat(sprintf("Mean Score: %.4f (±%.4f)\n", 
                  result$cv_results$mean_score, 
                  result$cv_results$std_score))
    }
    
    if (!is.null(result$feature_importance)) {
      cat("\nTop 10 Important Features:\n")
      top_features <- head(sort(result$feature_importance, decreasing = TRUE), 10)
      for (i in 1:length(top_features)) {
        cat(sprintf("%2d. %s: %.4f\n", i, names(top_features)[i], top_features[i]))
      }
    }
  }
  
  sink()
  
  cat("\nResults saved to:\n")
  cat("  - ml_results.rds (main results)\n")
  cat("  - ml_summary.csv (summary table)\n")
  cat("  - ml_detailed_report.txt (detailed report)\n")
}

# =============================================================================
# DEMONSTRATION FUNCTION
# =============================================================================

#' Demonstrate ML Capabilities
demo_ml <- function(optimization_level = "medium") {
  
  cat("MACHINE LEARNING DEMONSTRATION\n")
  cat("==============================\n")
  
  # Check if trial data exists
  if (!file.exists("virtual_clinical_trial_results.csv")) {
    cat("Generating virtual clinical trial data...\n")
    
    # Try to source and run the virtual trial
    if (file.exists("run_complete_system.r")) {
      source("run_complete_system.r")
    } else {
      cat("Error: Virtual clinical trial data not found.\n")
      cat("Please run the complete system first to generate trial data.\n")
      return(NULL)
    }
  }
  
  # Run ML pipeline
  cat("\nRunning ML pipeline...\n")
  
  results <- run_ml_pipeline(
    optimization_level = optimization_level,
    parallel_cores = 2,  # Use 2 cores for demo
    save_results = TRUE,
    verbose = TRUE
  )
  
  return(results)
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# String concatenation operator
`%+%` <- function(a, b) paste0(a, b)

cat("ML Integration System Ready!\n")
cat("\nAvailable functions:\n")
cat("  - run_ml_pipeline(): Complete ML pipeline\n")
cat("  - demo_ml(): Demonstration of ML capabilities\n")
cat("  - generate_summary(): Generate performance summary\n")
cat("  - save_ml_results(): Save results and reports\n")
cat("\nTo run demonstration: demo_ml()\n")
cat("To run full pipeline: run_ml_pipeline()\n")