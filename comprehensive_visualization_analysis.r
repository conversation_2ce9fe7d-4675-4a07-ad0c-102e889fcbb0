# Comprehensive Visualization and Analysis of Stochastic Dose-Dependent 
# Platelet Aggregation Inhibition Model

# Load required libraries
library(ggplot2)
library(dplyr)
library(tidyr)
library(gridExtra)
library(viridis)
library(RColorBrewer)
library(scales)

# Set theme for publication-quality plots
theme_publication <- theme_minimal() +
  theme(
    text = element_text(size = 12, family = "Arial"),
    axis.title = element_text(size = 14, face = "bold"),
    axis.text = element_text(size = 11),
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    strip.text = element_text(size = 11, face = "bold"),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(color = "black", fill = NA, size = 0.5)
  )

# Define consistent color palette for agonists
agonist_colors <- c(
  "collagen" = "#E31A1C",        # Red - Primary BTK target
  "ADP" = "#1F78B4",             # Blue - Secondary pathway
  "thrombin" = "#33A02C",        # Green - BTK-independent
  "arachidonic_acid" = "#FF7F00", # Orange - TxA2 pathway
  "ristocetin" = "#6A3D9A"       # Purple - VWF-mediated
)

# Load data files
cat("Loading data files...\n")
dose_response_detailed <- read.csv("stochastic_dose_response_detailed.csv")
dose_response_summary <- read.csv("stochastic_dose_response_summary.csv")
literature_validation <- read.csv("literature_validation_results.csv")

# =============================================================================
# 1. DOSE-RESPONSE CURVES WITH VARIABILITY
# =============================================================================

create_dose_response_plots <- function() {
  cat("Creating dose-response curves...\n")
  
  # Main dose-response plot with error bars
  p1 <- ggplot(dose_response_summary, aes(x = dose_mg, y = mean_inhibition, 
                                         color = agonist, fill = agonist)) +
    geom_ribbon(aes(ymin = mean_inhibition - sd_inhibition, 
                    ymax = mean_inhibition + sd_inhibition), 
                alpha = 0.2, color = NA) +
    geom_line(size = 1.2) +
    geom_point(size = 3, shape = 21, color = "white", stroke = 1.5) +
    scale_color_manual(values = agonist_colors, name = "Agonist") +
    scale_fill_manual(values = agonist_colors, name = "Agonist") +
    labs(
      title = "Dose-Response Curves: Ibrutinib-Induced Platelet Aggregation Inhibition",
      subtitle = "Mean ± SD from 20 patients per dose",
      x = "Ibrutinib Dose (mg/day)",
      y = "Aggregation Inhibition (%)"
    ) +
    theme_publication +
    facet_wrap(~agonist, scales = "free_y", ncol = 3) +
    theme(legend.position = "none")  # Remove legend since facets show agonists
  
  ggsave("dose_response_curves_by_agonist.png", p1, 
         width = 12, height = 8, dpi = 300, bg = "white")
  
  # Combined dose-response plot
  p2 <- ggplot(dose_response_summary, aes(x = dose_mg, y = mean_inhibition, 
                                         color = agonist)) +
    geom_errorbar(aes(ymin = mean_inhibition - sd_inhibition, 
                      ymax = mean_inhibition + sd_inhibition), 
                  width = 15, alpha = 0.7) +
    geom_line(size = 1.2) +
    geom_point(size = 3) +
    scale_color_manual(values = agonist_colors, name = "Agonist") +
    labs(
      title = "Comparative Dose-Response Curves",
      subtitle = "Ibrutinib inhibition across different platelet agonists",
      x = "Ibrutinib Dose (mg/day)",
      y = "Aggregation Inhibition (%)"
    ) +
    theme_publication +
    guides(color = guide_legend(override.aes = list(size = 4)))
  
  ggsave("dose_response_curves_combined.png", p2, 
         width = 10, height = 6, dpi = 300, bg = "white")
  
  return(list(faceted = p1, combined = p2))
}

# =============================================================================
# 2. STOCHASTIC VARIABILITY ANALYSIS PLOTS
# =============================================================================

create_variability_plots <- function() {
  cat("Creating stochastic variability plots...\n")
  
  # Filter data for clinical dose (420 mg)
  clinical_dose_data <- dose_response_detailed %>%
    filter(dose_mg == 420)
  
  # Box plots showing patient variability at clinical dose
  p1 <- ggplot(clinical_dose_data, aes(x = agonist, y = inhibition_percent, 
                                      fill = agonist)) +
    geom_boxplot(alpha = 0.7, outlier.shape = 21, outlier.size = 2) +
    geom_jitter(width = 0.2, alpha = 0.5, size = 1.5) +
    scale_fill_manual(values = agonist_colors, name = "Agonist") +
    labs(
      title = "Inter-Patient Variability in Aggregation Inhibition",
      subtitle = "Clinical dose (420 mg/day) across 20 patients",
      x = "Platelet Agonist",
      y = "Aggregation Inhibition (%)"
    ) +
    theme_publication +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "none"
    )
  
  ggsave("inter_patient_variability_boxplot.png", p1, 
         width = 10, height = 6, dpi = 300, bg = "white")
  
  # Violin plots showing distribution shapes
  p2 <- ggplot(clinical_dose_data, aes(x = agonist, y = inhibition_percent, 
                                      fill = agonist)) +
    geom_violin(alpha = 0.7, trim = FALSE) +
    geom_boxplot(width = 0.1, fill = "white", alpha = 0.8) +
    scale_fill_manual(values = agonist_colors, name = "Agonist") +
    labs(
      title = "Distribution of Aggregation Inhibition Responses",
      subtitle = "Violin plots showing response distributions at clinical dose",
      x = "Platelet Agonist",
      y = "Aggregation Inhibition (%)"
    ) +
    theme_publication +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "none"
    )
  
  ggsave("response_distribution_violin.png", p2, 
         width = 10, height = 6, dpi = 300, bg = "white")
  
  # Coefficient of variation plot
  cv_data <- dose_response_summary %>%
    filter(!is.na(cv_inhibition), cv_inhibition != Inf) %>%
    mutate(dose_mg = factor(dose_mg))
  
  p3 <- ggplot(cv_data, aes(x = dose_mg, y = cv_inhibition, 
                           color = agonist, group = agonist)) +
    geom_line(size = 1.2) +
    geom_point(size = 3) +
    scale_color_manual(values = agonist_colors, name = "Agonist") +
    labs(
      title = "Coefficient of Variation Across Doses",
      subtitle = "Relative variability in aggregation inhibition",
      x = "Ibrutinib Dose (mg/day)",
      y = "Coefficient of Variation (%)"
    ) +
    theme_publication +
    guides(color = guide_legend(override.aes = list(size = 4)))
  
  ggsave("coefficient_variation_by_dose.png", p3, 
         width = 10, height = 6, dpi = 300, bg = "white")
  
  return(list(boxplot = p1, violin = p2, cv = p3))
}

# =============================================================================
# 3. LITERATURE VALIDATION PLOTS
# =============================================================================

create_literature_validation_plots <- function() {
  cat("Creating literature validation plots...\n")
  
  # Prepare data for plotting
  validation_plot_data <- literature_validation %>%
    select(agonist, literature_inhibition, model_mean, model_sd) %>%
    pivot_longer(cols = c(literature_inhibition, model_mean), 
                 names_to = "source", values_to = "inhibition") %>%
    mutate(
      source = case_when(
        source == "literature_inhibition" ~ "Literature",
        source == "model_mean" ~ "Model"
      ),
      error = ifelse(source == "Model", model_sd, 0)
    )
  
  # Side-by-side comparison plot
  p1 <- ggplot(validation_plot_data, aes(x = agonist, y = inhibition, 
                                        fill = source)) +
    geom_col(position = "dodge", alpha = 0.8, width = 0.7) +
    geom_errorbar(aes(ymin = inhibition - error, ymax = inhibition + error),
                  position = position_dodge(0.7), width = 0.2) +
    scale_fill_manual(values = c("Literature" = "#2C3E50", "Model" = "#E74C3C"),
                      name = "Source") +
    labs(
      title = "Model Validation Against Literature",
      subtitle = "Aggregation inhibition at clinical dose (420 mg/day)",
      x = "Platelet Agonist",
      y = "Aggregation Inhibition (%)"
    ) +
    theme_publication +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  ggsave("literature_validation_comparison.png", p1, 
         width = 10, height = 6, dpi = 300, bg = "white")
  
  # Correlation plot
  p2 <- ggplot(literature_validation, aes(x = literature_inhibition, y = model_mean)) +
    geom_abline(intercept = 0, slope = 1, linetype = "dashed", 
                color = "gray50", size = 1) +
    geom_errorbar(aes(ymin = model_mean - model_sd, ymax = model_mean + model_sd),
                  width = 2, alpha = 0.7) +
    geom_point(aes(color = agonist), size = 4) +
    scale_color_manual(values = agonist_colors, name = "Agonist") +
    labs(
      title = "Model vs Literature Correlation",
      subtitle = "Line of unity (dashed) represents perfect agreement",
      x = "Literature Inhibition (%)",
      y = "Model Prediction (%)"
    ) +
    theme_publication +
    coord_equal() +
    guides(color = guide_legend(override.aes = list(size = 4)))
  
  ggsave("literature_model_correlation.png", p2, 
         width = 8, height = 8, dpi = 300, bg = "white")
  
  return(list(comparison = p1, correlation = p2))
}

# Run all visualization functions
dose_response_plots <- create_dose_response_plots()
variability_plots <- create_variability_plots()
validation_plots <- create_literature_validation_plots()

# =============================================================================
# 4. PATHWAY-SPECIFIC INHIBITION PLOTS
# =============================================================================

create_pathway_inhibition_plots <- function() {
  cat("Creating pathway-specific inhibition plots...\n")

  # Load model parameters for pathway analysis
  source("test_stochastic_model.r")

  # Generate BTK vs TEC inhibition data
  concentrations <- seq(0, 600, by = 50)
  pathway_data <- data.frame()

  for (conc in concentrations) {
    for (agonist in names(FIXED_AGONIST_CONCENTRATIONS)) {
      # BTK inhibition (primary)
      btk_remaining <- stochastic_aggregation_inhibition(conc, agonist, 1, 1)
      btk_inhibition <- (1 - btk_remaining) * 100

      # TEC inhibition (secondary, ~30% of BTK)
      tec_scaling <- 0.3
      tec_inhibition <- btk_inhibition * tec_scaling

      pathway_data <- rbind(pathway_data, data.frame(
        concentration_nM = conc,
        agonist = agonist,
        BTK_inhibition = btk_inhibition,
        TEC_inhibition = tec_inhibition
      ))
    }
  }

  # Reshape for plotting
  pathway_long <- pathway_data %>%
    pivot_longer(cols = c(BTK_inhibition, TEC_inhibition),
                 names_to = "kinase", values_to = "inhibition") %>%
    mutate(kinase = gsub("_inhibition", "", kinase))

  # BTK vs TEC comparison plot
  p1 <- ggplot(pathway_long, aes(x = concentration_nM, y = inhibition,
                                color = kinase, linetype = kinase)) +
    geom_line(size = 1.2) +
    scale_color_manual(values = c("BTK" = "#E31A1C", "TEC" = "#1F78B4"),
                       name = "Kinase") +
    scale_linetype_manual(values = c("BTK" = "solid", "TEC" = "dashed"),
                          name = "Kinase") +
    labs(
      title = "BTK vs TEC Kinase Inhibition Pathways",
      subtitle = "Differential inhibition across platelet agonists",
      x = "Ibrutinib Concentration (nM)",
      y = "Aggregation Inhibition (%)"
    ) +
    theme_publication +
    facet_wrap(~agonist, scales = "free_y", ncol = 3)

  ggsave("btk_tec_pathway_comparison.png", p1,
         width = 12, height = 8, dpi = 300, bg = "white")

  # Pathway selectivity plot
  pathway_selectivity <- pathway_data %>%
    mutate(selectivity_ratio = BTK_inhibition / (TEC_inhibition + 0.01)) %>%
    filter(concentration_nM == 300)  # Clinical concentration

  p2 <- ggplot(pathway_selectivity, aes(x = agonist, y = selectivity_ratio,
                                       fill = agonist)) +
    geom_col(alpha = 0.8) +
    scale_fill_manual(values = agonist_colors, name = "Agonist") +
    labs(
      title = "BTK/TEC Selectivity Ratio",
      subtitle = "Pathway selectivity at clinical concentration (300 nM)",
      x = "Platelet Agonist",
      y = "BTK/TEC Inhibition Ratio"
    ) +
    theme_publication +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "none"
    )

  ggsave("pathway_selectivity_ratio.png", p2,
         width = 8, height = 6, dpi = 300, bg = "white")

  return(list(comparison = p1, selectivity = p2))
}

# =============================================================================
# 5. CONCENTRATION-RESPONSE RELATIONSHIP PLOTS
# =============================================================================

create_concentration_response_plots <- function() {
  cat("Creating concentration-response plots...\n")

  # Convert dose to concentration for detailed analysis
  dose_conc_data <- dose_response_detailed %>%
    mutate(concentration_nM = dose_mg * (300/420))  # Dose-to-concentration conversion

  # Concentration-response plot with individual points
  p1 <- ggplot(dose_conc_data, aes(x = concentration_nM, y = inhibition_percent)) +
    geom_point(aes(color = agonist), alpha = 0.6, size = 1.5) +
    geom_smooth(aes(color = agonist), method = "loess", se = TRUE, alpha = 0.3) +
    scale_color_manual(values = agonist_colors, name = "Agonist") +
    labs(
      title = "Concentration-Response Relationships",
      subtitle = "Individual patient responses with smoothed trends",
      x = "Ibrutinib Concentration (nM)",
      y = "Aggregation Inhibition (%)"
    ) +
    theme_publication +
    facet_wrap(~agonist, scales = "free_y", ncol = 3) +
    theme(legend.position = "none")

  ggsave("concentration_response_individual.png", p1,
         width = 12, height = 8, dpi = 300, bg = "white")

  # EC50 visualization
  ec50_data <- data.frame(
    agonist = c("collagen", "ADP", "thrombin", "arachidonic_acid", "ristocetin"),
    ec50_nM = c(150, 2000, 2500, 400, 5000),
    max_inhibition = c(98, 6, 10, 55, 5)
  )

  p2 <- ggplot(ec50_data, aes(x = reorder(agonist, -ec50_nM), y = ec50_nM,
                             fill = agonist)) +
    geom_col(alpha = 0.8) +
    scale_fill_manual(values = agonist_colors, name = "Agonist") +
    scale_y_log10(labels = scales::comma) +
    labs(
      title = "EC50 Values Across Platelet Agonists",
      subtitle = "Half-maximal effective concentrations (log scale)",
      x = "Platelet Agonist",
      y = "EC50 (nM)"
    ) +
    theme_publication +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "none"
    )

  ggsave("ec50_comparison.png", p2,
         width = 8, height = 6, dpi = 300, bg = "white")

  return(list(individual = p1, ec50 = p2))
}

# Run additional visualization functions
pathway_plots <- create_pathway_inhibition_plots()
concentration_plots <- create_concentration_response_plots()

cat("\n=== VISUALIZATION GENERATION COMPLETE ===\n")
cat("Generated plot files:\n")
cat("- dose_response_curves_by_agonist.png\n")
cat("- dose_response_curves_combined.png\n")
cat("- inter_patient_variability_boxplot.png\n")
cat("- response_distribution_violin.png\n")
cat("- coefficient_variation_by_dose.png\n")
cat("- literature_validation_comparison.png\n")
cat("- literature_model_correlation.png\n")
cat("- btk_tec_pathway_comparison.png\n")
cat("- pathway_selectivity_ratio.png\n")
cat("- concentration_response_individual.png\n")
cat("- ec50_comparison.png\n")
