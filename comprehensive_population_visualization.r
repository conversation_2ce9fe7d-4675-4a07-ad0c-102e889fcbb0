# Comprehensive Demographic Visualization and Literature Validation Analysis
# For 5,000 CLL Patients with Stochastic Platelet Aggregation Profiles

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
  library(gridExtra)
  library(RColorBrewer)
  library(viridis)
  library(scales)
  library(corrplot)
  library(tidyr)
})

# Set publication-ready theme
theme_publication <- theme_minimal() +
  theme(
    text = element_text(size = 12, family = "sans"),
    axis.title = element_text(size = 14, face = "bold"),
    axis.text = element_text(size = 11),
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    strip.text = element_text(size = 11, face = "bold"),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(color = "black", fill = NA, linewidth = 0.5)
  )

# Define colorblind-friendly palettes
demographic_colors <- c("#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b")
agonist_colors <- c(
  "collagen" = "#e74c3c",        # Red - Primary BTK target
  "ADP" = "#3498db",             # Blue - Secondary pathway
  "thrombin" = "#2ecc71",        # Green - BTK-independent
  "arachidonic_acid" = "#f39c12", # Orange - TxA2 pathway
  "ristocetin" = "#9b59b6"       # Purple - VWF-mediated
)

# =============================================================================
# LOAD AND PREPARE DATA
# =============================================================================

cat("=== COMPREHENSIVE POPULATION VISUALIZATION ANALYSIS ===\n")
cat("Loading synthetic population data...\n")

# Load population data
population <- read.csv("ml_results/synthetic_patient_population.csv", stringsAsFactors = FALSE)

cat("Population loaded:", nrow(population), "patients,", ncol(population), "variables\n")

# Verify platelet aggregation variables
platelet_vars <- c("collagen_inhibition_pct", "adp_inhibition_pct", "thrombin_inhibition_pct",
                   "arachidonic_acid_inhibition_pct", "ristocetin_inhibition_pct")

missing_vars <- platelet_vars[!platelet_vars %in% names(population)]
if (length(missing_vars) == 0) {
  cat("✓ All platelet aggregation variables present\n")
} else {
  cat("✗ Missing variables:", paste(missing_vars, collapse = ", "), "\n")
}

# =============================================================================
# 1. DEMOGRAPHIC VISUALIZATION PLOTS
# =============================================================================

cat("\n1. Creating demographic visualization plots...\n")

# 1.1 Age Distribution with Clinical Benchmarks
median_age <- median(population$age)
target_median <- 71  # CLL clinical trial benchmark

p1_age <- ggplot(population, aes(x = age)) +
  geom_histogram(bins = 30, fill = "#3498db", alpha = 0.7, color = "white", linewidth = 0.5) +
  geom_vline(xintercept = median_age, color = "#e74c3c", linetype = "solid", linewidth = 1.2) +
  geom_vline(xintercept = target_median, color = "#2ecc71", linetype = "dashed", linewidth = 1.2) +
  annotate("text", x = median_age + 2, y = max(table(cut(population$age, 30))) * 0.8,
           label = paste("Population\nMedian:", median_age), color = "#e74c3c", fontface = "bold") +
  annotate("text", x = target_median - 2, y = max(table(cut(population$age, 30))) * 0.6,
           label = paste("CLL Trial\nTarget:", target_median), color = "#2ecc71", fontface = "bold") +
  labs(
    title = "Age Distribution: CLL Patient Population",
    subtitle = paste("N =", nrow(population), "| Median age:", median_age, "years (Target: 70-72)"),
    x = "Age (years)",
    y = "Number of Patients"
  ) +
  theme_publication

# 1.2 Sex Distribution with Literature Comparison
sex_counts <- table(population$sex)
male_pct <- round(sex_counts["Male"] / sum(sex_counts) * 100, 1)
target_male_pct <- 58

p1_sex <- ggplot(population, aes(x = sex, fill = sex)) +
  geom_bar(alpha = 0.8, color = "white", linewidth = 0.5) +
  scale_fill_manual(values = c("Female" = "#e74c3c", "Male" = "#3498db")) +
  geom_text(stat = "count", aes(label = paste0(..count.., "\n(", 
            round(..count../sum(..count..) * 100, 1), "%)")), 
            vjust = -0.5, fontface = "bold") +
  labs(
    title = "Sex Distribution",
    subtitle = paste("Male:", male_pct, "% (Target: ~", target_male_pct, "%)"),
    x = "Sex",
    y = "Number of Patients"
  ) +
  theme_publication +
  theme(legend.position = "none")

# 1.3 BMI Distribution with Health Categories
bmi_median <- round(median(population$bmi), 1)

p1_bmi <- ggplot(population, aes(x = bmi)) +
  geom_histogram(bins = 30, fill = "#2ecc71", alpha = 0.7, color = "white", linewidth = 0.5) +
  geom_vline(xintercept = 18.5, color = "#f39c12", linetype = "dashed", alpha = 0.7) +
  geom_vline(xintercept = 25, color = "#e67e22", linetype = "dashed", alpha = 0.7) +
  geom_vline(xintercept = 30, color = "#e74c3c", linetype = "dashed", alpha = 0.7) +
  geom_vline(xintercept = bmi_median, color = "#2c3e50", linetype = "solid", linewidth = 1.2) +
  annotate("text", x = 22, y = max(table(cut(population$bmi, 30))) * 0.8,
           label = "Normal\n(18.5-25)", size = 3, alpha = 0.7) +
  annotate("text", x = 27.5, y = max(table(cut(population$bmi, 30))) * 0.6,
           label = "Overweight\n(25-30)", size = 3, alpha = 0.7) +
  annotate("text", x = bmi_median + 1, y = max(table(cut(population$bmi, 30))) * 0.9,
           label = paste("Median:", bmi_median), color = "#2c3e50", fontface = "bold") +
  labs(
    title = "BMI Distribution",
    subtitle = paste("N =", nrow(population), "| Median BMI:", bmi_median, "kg/m²"),
    x = "BMI (kg/m²)",
    y = "Number of Patients"
  ) +
  theme_publication

# 1.4 Ethnicity Distribution
ethnicity_data <- population %>%
  count(ethnicity) %>%
  mutate(percentage = round(n / sum(n) * 100, 1)) %>%
  arrange(desc(n))

p1_ethnicity <- ggplot(ethnicity_data, aes(x = reorder(ethnicity, n), y = n, fill = ethnicity)) +
  geom_col(alpha = 0.8, color = "white", linewidth = 0.5) +
  scale_fill_brewer(type = "qual", palette = "Set3") +
  geom_text(aes(label = paste0(n, "\n(", percentage, "%)")), 
            hjust = -0.1, fontface = "bold") +
  labs(
    title = "Ethnicity Distribution",
    subtitle = paste("Caucasian:", ethnicity_data$percentage[ethnicity_data$ethnicity == "Caucasian"], 
                    "% (Target: ~85%)"),
    x = "Ethnicity",
    y = "Number of Patients"
  ) +
  theme_publication +
  theme(legend.position = "none") +
  coord_flip()

# Combine demographic plots
demo_combined <- grid.arrange(p1_age, p1_sex, p1_bmi, p1_ethnicity, ncol = 2, nrow = 2)
ggsave("population_demographics_summary.png", demo_combined, width = 14, height = 10, dpi = 300, bg = "white")

cat("✓ Demographic plots saved to 'population_demographics_summary.png'\n")

# =============================================================================
# 2. CLINICAL CHARACTERISTICS AND COMORBIDITY PLOTS
# =============================================================================

cat("\n2. Creating clinical characteristics plots...\n")

# 2.1 Comorbidity Prevalence
comorbidity_data <- population %>%
  summarise(
    `Cardiovascular Disease` = mean(cardiovascular_disease, na.rm = TRUE) * 100,
    `Diabetes` = mean(diabetes, na.rm = TRUE) * 100,
    `Hypertension` = mean(hypertension, na.rm = TRUE) * 100,
    `Atrial Fibrillation` = mean(atrial_fibrillation, na.rm = TRUE) * 100
  ) %>%
  pivot_longer(everything(), names_to = "Comorbidity", values_to = "Prevalence") %>%
  mutate(
    Target = case_when(
      Comorbidity == "Cardiovascular Disease" ~ 33,
      Comorbidity == "Diabetes" ~ 15,
      Comorbidity == "Hypertension" ~ 60,
      Comorbidity == "Atrial Fibrillation" ~ 25,
      TRUE ~ NA_real_
    )
  )

p2_comorbid <- ggplot(comorbidity_data, aes(x = reorder(Comorbidity, Prevalence))) +
  geom_col(aes(y = Prevalence), fill = "#3498db", alpha = 0.8, color = "white", linewidth = 0.5) +
  geom_point(aes(y = Target), color = "#e74c3c", size = 4, shape = 18) +
  geom_text(aes(y = Prevalence, label = paste0(round(Prevalence, 1), "%")),
            hjust = -0.1, fontface = "bold") +
  geom_text(aes(y = Target, label = paste0("Target: ", Target, "%")),
            hjust = 1.1, color = "#e74c3c", fontface = "bold", size = 3) +
  labs(
    title = "Comorbidity Prevalence in CLL Population",
    subtitle = "Population rates vs clinical targets (red diamonds)",
    x = "Comorbidity",
    y = "Prevalence (%)"
  ) +
  theme_publication +
  coord_flip()

# 2.2 Bleeding Risk Factors
bleeding_data <- population %>%
  summarise(
    `Anticoagulants` = mean(anticoagulants, na.rm = TRUE) * 100,
    `Antiplatelets` = mean(antiplatelets, na.rm = TRUE) * 100,
    `Low vWF Activity` = mean(vwf_low, na.rm = TRUE) * 100,
    `Low Factor VIII` = mean(factor_viii_low, na.rm = TRUE) * 100,
    `Prolonged EPI Closure` = mean(epi_closure_prolonged, na.rm = TRUE) * 100
  ) %>%
  pivot_longer(everything(), names_to = "Risk_Factor", values_to = "Prevalence")

p2_bleeding <- ggplot(bleeding_data, aes(x = reorder(Risk_Factor, Prevalence), y = Prevalence, fill = Risk_Factor)) +
  geom_col(alpha = 0.8, color = "white", linewidth = 0.5) +
  scale_fill_brewer(type = "qual", palette = "Set1") +
  geom_text(aes(label = paste0(round(Prevalence, 1), "%")),
            hjust = -0.1, fontface = "bold") +
  labs(
    title = "Bleeding Risk Factors Prevalence",
    subtitle = "Hemorrhagic risk factors in CLL population",
    x = "Risk Factor",
    y = "Prevalence (%)"
  ) +
  theme_publication +
  theme(legend.position = "none") +
  coord_flip()

# 2.3 CLL Disease Characteristics
cll_data <- population %>%
  summarise(
    `del(17p)/TP53 Mutations` = mean(del17p_tp53, na.rm = TRUE) * 100,
    `Unmutated IGHV` = mean(unmutated_ighv, na.rm = TRUE) * 100
  ) %>%
  pivot_longer(everything(), names_to = "Genetic_Factor", values_to = "Prevalence")

p2_cll <- ggplot(cll_data, aes(x = Genetic_Factor, y = Prevalence, fill = Genetic_Factor)) +
  geom_col(alpha = 0.8, color = "white", linewidth = 0.5) +
  scale_fill_manual(values = c("#e74c3c", "#f39c12")) +
  geom_text(aes(label = paste0(round(Prevalence, 1), "%")),
            vjust = -0.5, fontface = "bold") +
  labs(
    title = "CLL Genetic Risk Factors",
    subtitle = "High-risk genetic features",
    x = "Genetic Factor",
    y = "Prevalence (%)"
  ) +
  theme_publication +
  theme(legend.position = "none", axis.text.x = element_text(angle = 45, hjust = 1))

# 2.4 Treatment Allocation
treatment_counts <- table(population$treatment_arm)
treatment_data <- data.frame(
  Treatment = names(treatment_counts),
  Count = as.numeric(treatment_counts),
  Percentage = round(as.numeric(treatment_counts) / sum(treatment_counts) * 100, 1)
)

p2_treatment <- ggplot(treatment_data, aes(x = Treatment, y = Count, fill = Treatment)) +
  geom_col(alpha = 0.8, color = "white", linewidth = 0.5) +
  scale_fill_manual(values = c("Control" = "#95a5a6", "Ibrutinib_420mg" = "#e67e22")) +
  geom_text(aes(label = paste0(Count, "\n(", Percentage, "%)")),
            vjust = 0.5, fontface = "bold") +
  labs(
    title = "Treatment Allocation",
    subtitle = paste("Total N =", sum(treatment_counts)),
    x = "Treatment Arm",
    y = "Number of Patients"
  ) +
  theme_publication +
  theme(legend.position = "none")

# Combine clinical plots
clinical_combined <- grid.arrange(p2_comorbid, p2_bleeding, p2_cll, p2_treatment, ncol = 2, nrow = 2)
ggsave("population_clinical_characteristics.png", clinical_combined, width = 14, height = 10, dpi = 300, bg = "white")

cat("✓ Clinical characteristics plots saved to 'population_clinical_characteristics.png'\n")

# =============================================================================
# 3. PLATELET AGGREGATION DEMOGRAPHICS
# =============================================================================

cat("\n3. Creating platelet aggregation demographic plots...\n")

# Check if platelet variables exist
if (all(platelet_vars %in% names(population))) {

  # 3.1 Population Distribution of Platelet Responses
  platelet_long <- population %>%
    select(patient_id, all_of(platelet_vars)) %>%
    pivot_longer(cols = -patient_id, names_to = "agonist", values_to = "inhibition_pct") %>%
    mutate(agonist = gsub("_inhibition_pct", "", agonist))

  p3_distribution <- ggplot(platelet_long, aes(x = inhibition_pct, fill = agonist)) +
    geom_histogram(bins = 30, alpha = 0.7, color = "white", linewidth = 0.3) +
    scale_fill_manual(values = agonist_colors, name = "Agonist") +
    facet_wrap(~agonist, scales = "free", ncol = 3) +
    labs(
      title = "Platelet Aggregation Inhibition Distribution",
      subtitle = "Population responses at 300 nM ibrutinib (N = 5,000)",
      x = "Aggregation Inhibition (%)",
      y = "Number of Patients"
    ) +
    theme_publication +
    theme(legend.position = "none")

  # 3.2 Box Plots Comparing Inhibition Levels
  p3_boxplot <- ggplot(platelet_long, aes(x = reorder(agonist, -inhibition_pct, median),
                                         y = inhibition_pct, fill = agonist)) +
    geom_boxplot(alpha = 0.7, outlier.alpha = 0.3, color = "black", linewidth = 0.5) +
    scale_fill_manual(values = agonist_colors, name = "Agonist") +
    stat_summary(fun = median, geom = "text",
                aes(label = paste0("Median: ", round(..y.., 1), "%")),
                vjust = -0.5, fontface = "bold", size = 3) +
    labs(
      title = "Platelet Aggregation Inhibition by Agonist",
      subtitle = "Median, quartiles, and outliers across 5,000 patients",
      x = "Platelet Agonist",
      y = "Aggregation Inhibition (%)"
    ) +
    theme_publication +
    theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "none")

  # 3.3 Overall Inhibition Score Distribution
  p3_overall <- ggplot(population, aes(x = overall_inhibition_score)) +
    geom_histogram(bins = 30, fill = "#34495e", alpha = 0.7, color = "white", linewidth = 0.5) +
    geom_vline(xintercept = median(population$overall_inhibition_score),
               color = "#e74c3c", linetype = "solid", linewidth = 1.2) +
    geom_vline(xintercept = 30, color = "#f39c12", linetype = "dashed", linewidth = 1) +
    geom_vline(xintercept = 50, color = "#e67e22", linetype = "dashed", linewidth = 1) +
    annotate("text", x = median(population$overall_inhibition_score) + 3,
             y = max(table(cut(population$overall_inhibition_score, 30))) * 0.8,
             label = paste("Median:", round(median(population$overall_inhibition_score), 1), "%"),
             color = "#e74c3c", fontface = "bold") +
    annotate("text", x = 35, y = max(table(cut(population$overall_inhibition_score, 30))) * 0.6,
             label = "Moderate\nRisk", color = "#f39c12", fontface = "bold", size = 3) +
    annotate("text", x = 55, y = max(table(cut(population$overall_inhibition_score, 30))) * 0.4,
             label = "High\nRisk", color = "#e67e22", fontface = "bold", size = 3) +
    labs(
      title = "Overall Inhibition Score Distribution",
      subtitle = "Weighted composite score with clinical risk thresholds",
      x = "Overall Inhibition Score (%)",
      y = "Number of Patients"
    ) +
    theme_publication

  # 3.4 Pathway Selectivity Visualization
  selectivity_data <- population %>%
    mutate(
      btk_dependent = collagen_inhibition_pct,
      btk_independent = (adp_inhibition_pct + thrombin_inhibition_pct + ristocetin_inhibition_pct) / 3,
      selectivity_ratio = btk_dependent / (btk_independent + 0.1)
    )

  p3_selectivity <- ggplot(selectivity_data, aes(x = btk_dependent, y = btk_independent)) +
    geom_point(alpha = 0.3, color = "#3498db") +
    geom_smooth(method = "lm", color = "#e74c3c", linewidth = 1.2) +
    geom_abline(intercept = 0, slope = 1, linetype = "dashed", color = "gray50") +
    labs(
      title = "BTK-Dependent vs BTK-Independent Inhibition",
      subtitle = "Pathway selectivity demonstration",
      x = "BTK-Dependent Inhibition (Collagen, %)",
      y = "BTK-Independent Inhibition (ADP/Thrombin/Ristocetin, %)"
    ) +
    theme_publication

  # Combine platelet plots
  platelet_combined <- grid.arrange(p3_distribution, p3_boxplot, p3_overall, p3_selectivity,
                                   ncol = 2, nrow = 2)
  ggsave("platelet_aggregation_demographics.png", platelet_combined,
         width = 14, height = 10, dpi = 300, bg = "white")

  cat("✓ Platelet aggregation demographic plots saved\n")
} else {
  cat("✗ Platelet aggregation variables not found - skipping plots\n")
}

# =============================================================================
# 4. LITERATURE VALIDATION PLOTS
# =============================================================================

cat("\n4. Creating literature validation plots...\n")

# 4.1 Literature Comparison Data
literature_values <- data.frame(
  agonist = c("collagen", "ADP", "thrombin", "arachidonic_acid", "ristocetin"),
  literature_inhibition = c(98, 6, 10, 55, 5),
  source = c("Bye et al. 2017", "Kamel et al. 2018", "Kamel et al. 2018",
             "Bye et al. 2017", "Kamel et al. 2018"),
  stringsAsFactors = FALSE
)

# Calculate population means and statistics
if (all(platelet_vars %in% names(population))) {
  population_stats <- data.frame(
    agonist = c("collagen", "ADP", "thrombin", "arachidonic_acid", "ristocetin"),
    population_mean = c(
      mean(population$collagen_inhibition_pct, na.rm = TRUE),
      mean(population$adp_inhibition_pct, na.rm = TRUE),
      mean(population$thrombin_inhibition_pct, na.rm = TRUE),
      mean(population$arachidonic_acid_inhibition_pct, na.rm = TRUE),
      mean(population$ristocetin_inhibition_pct, na.rm = TRUE)
    ),
    population_sd = c(
      sd(population$collagen_inhibition_pct, na.rm = TRUE),
      sd(population$adp_inhibition_pct, na.rm = TRUE),
      sd(population$thrombin_inhibition_pct, na.rm = TRUE),
      sd(population$arachidonic_acid_inhibition_pct, na.rm = TRUE),
      sd(population$ristocetin_inhibition_pct, na.rm = TRUE)
    )
  )

  # Merge with literature data
  validation_data <- merge(literature_values, population_stats, by = "agonist")
  validation_data$absolute_error <- abs(validation_data$literature_inhibition - validation_data$population_mean)
  validation_data$relative_error <- validation_data$absolute_error / validation_data$literature_inhibition * 100

  # 4.2 Side-by-side Comparison Bar Chart
  comparison_long <- validation_data %>%
    select(agonist, literature_inhibition, population_mean, population_sd) %>%
    pivot_longer(cols = c(literature_inhibition, population_mean),
                 names_to = "source", values_to = "inhibition") %>%
    mutate(
      source = case_when(
        source == "literature_inhibition" ~ "Literature",
        source == "population_mean" ~ "Population"
      ),
      error = ifelse(source == "Population", population_sd, 0)
    )

  p4_comparison <- ggplot(comparison_long, aes(x = agonist, y = inhibition, fill = source)) +
    geom_col(position = "dodge", alpha = 0.8, color = "white", linewidth = 0.5) +
    geom_errorbar(aes(ymin = inhibition - error, ymax = inhibition + error),
                  position = position_dodge(0.9), width = 0.2, color = "black") +
    scale_fill_manual(values = c("Literature" = "#2c3e50", "Population" = "#3498db"),
                      name = "Source") +
    labs(
      title = "Platelet Aggregation Inhibition: Population vs Literature",
      subtitle = "Comparison at clinical ibrutinib dose (420 mg/day, ~300 nM)",
      x = "Platelet Agonist",
      y = "Aggregation Inhibition (%)"
    ) +
    theme_publication +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))

  # 4.3 Correlation Scatter Plot
  p4_correlation <- ggplot(validation_data, aes(x = literature_inhibition, y = population_mean)) +
    geom_abline(intercept = 0, slope = 1, linetype = "dashed", color = "gray50", linewidth = 1) +
    geom_errorbar(aes(ymin = population_mean - population_sd, ymax = population_mean + population_sd),
                  width = 2, alpha = 0.7, color = "#3498db") +
    geom_point(aes(color = agonist), size = 4) +
    scale_color_manual(values = agonist_colors, name = "Agonist") +
    annotate("text", x = 80, y = 10,
             label = paste("R² =", round(cor(validation_data$literature_inhibition,
                                            validation_data$population_mean)^2, 3)),
             fontface = "bold", size = 4) +
    labs(
      title = "Model vs Literature Correlation",
      subtitle = "Line of unity (dashed) represents perfect agreement",
      x = "Literature Inhibition (%)",
      y = "Population Mean Inhibition (%)"
    ) +
    theme_publication +
    coord_equal(xlim = c(0, 100), ylim = c(0, 100))

  # 4.4 Error Analysis Visualization
  error_data <- validation_data %>%
    select(agonist, absolute_error, relative_error) %>%
    pivot_longer(cols = c(absolute_error, relative_error),
                 names_to = "error_type", values_to = "error_value") %>%
    mutate(error_type = case_when(
      error_type == "absolute_error" ~ "Absolute Error (%)",
      error_type == "relative_error" ~ "Relative Error (%)"
    ))

  p4_error <- ggplot(error_data, aes(x = reorder(agonist, error_value), y = error_value, fill = agonist)) +
    geom_col(alpha = 0.8, color = "white", linewidth = 0.5) +
    scale_fill_manual(values = agonist_colors, name = "Agonist") +
    facet_wrap(~error_type, scales = "free_y", ncol = 1) +
    labs(
      title = "Prediction Error Analysis",
      subtitle = "Absolute and relative errors vs literature values",
      x = "Platelet Agonist",
      y = "Error"
    ) +
    theme_publication +
    theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "none")

  # Combine validation plots
  validation_combined <- grid.arrange(p4_comparison, p4_correlation, p4_error, ncol = 2, nrow = 2)
  ggsave("platelet_aggregation_literature_validation.png", validation_combined,
         width = 14, height = 10, dpi = 300, bg = "white")

  cat("✓ Literature validation plots saved\n")

  # Save validation data
  write.csv(validation_data, "detailed_literature_validation.csv", row.names = FALSE)
  cat("✓ Detailed validation data saved to 'detailed_literature_validation.csv'\n")
}

# =============================================================================
# 5. GENETIC FACTORS AND METABOLIZER PHENOTYPES
# =============================================================================

cat("\n5. Creating genetic factors visualization...\n")

# 5.1 Metabolizer Phenotype Distribution
metabolizer_counts <- table(population$metabolizer_phenotype)
metabolizer_data <- data.frame(
  Phenotype = names(metabolizer_counts),
  Count = as.numeric(metabolizer_counts),
  Percentage = round(as.numeric(metabolizer_counts) / sum(metabolizer_counts) * 100, 1)
)

p5_metabolizer <- ggplot(metabolizer_data, aes(x = reorder(Phenotype, Count), y = Count, fill = Phenotype)) +
  geom_col(alpha = 0.8, color = "white", linewidth = 0.5) +
  scale_fill_brewer(type = "qual", palette = "Dark2") +
  geom_text(aes(label = paste0(Count, "\n(", Percentage, "%)")),
            hjust = -0.1, fontface = "bold") +
  labs(
    title = "CYP3A4/3A5 Metabolizer Phenotype Distribution",
    subtitle = "Drug metabolism capacity affecting ibrutinib exposure",
    x = "Metabolizer Phenotype",
    y = "Number of Patients"
  ) +
  theme_publication +
  theme(legend.position = "none") +
  coord_flip()

# 5.2 Genetic Mutations Summary
genetic_summary <- population %>%
  summarise(
    `BTK C481S Mutation` = mean(btk_c481s_mutation, na.rm = TRUE) * 100,
    `PLCγ2 Variants` = mean(plcg2_variants, na.rm = TRUE) * 100,
    `PLCγ2 Resistance` = mean(plcg2_resistance_mutations, na.rm = TRUE) * 100
  ) %>%
  pivot_longer(everything(), names_to = "Mutation", values_to = "Prevalence")

p5_mutations <- ggplot(genetic_summary, aes(x = reorder(Mutation, Prevalence), y = Prevalence, fill = Mutation)) +
  geom_col(alpha = 0.8, color = "white", linewidth = 0.5) +
  scale_fill_brewer(type = "qual", palette = "Set3") +
  geom_text(aes(label = paste0(round(Prevalence, 1), "%")),
            hjust = -0.1, fontface = "bold") +
  labs(
    title = "Genetic Mutations Affecting Ibrutinib Response",
    subtitle = "Resistance mechanisms and pathway variants",
    x = "Mutation Type",
    y = "Prevalence (%)"
  ) +
  theme_publication +
  theme(legend.position = "none") +
  coord_flip()

# Combine genetic plots
genetic_combined <- grid.arrange(p5_metabolizer, p5_mutations, ncol = 1, nrow = 2)
ggsave("population_genetic_factors.png", genetic_combined, width = 12, height = 8, dpi = 300, bg = "white")

cat("✓ Genetic factors plots saved to 'population_genetic_factors.png'\n")

# =============================================================================
# 6. COMPREHENSIVE SUMMARY AND FINAL REPORT
# =============================================================================

cat("\n6. Generating comprehensive summary report...\n")

# Create summary statistics
summary_stats <- list(
  population_size = nrow(population),
  median_age = median(population$age),
  male_percentage = round(mean(population$sex == "Male") * 100, 1),
  caucasian_percentage = round(mean(population$ethnicity == "Caucasian") * 100, 1),
  cvd_prevalence = round(mean(population$cardiovascular_disease) * 100, 1),
  anticoagulant_use = round(mean(population$anticoagulants) * 100, 1)
)

if (all(platelet_vars %in% names(population))) {
  platelet_summary <- list(
    collagen_mean = round(mean(population$collagen_inhibition_pct), 1),
    collagen_sd = round(sd(population$collagen_inhibition_pct), 1),
    adp_mean = round(mean(population$adp_inhibition_pct), 1),
    arachidonic_mean = round(mean(population$arachidonic_acid_inhibition_pct), 1),
    overall_score_mean = round(mean(population$overall_inhibition_score), 1),
    pathway_selectivity = round(mean(population$collagen_inhibition_pct) /
                               (mean(population$adp_inhibition_pct) + 0.01), 1)
  )
  summary_stats <- c(summary_stats, platelet_summary)
}

# Save comprehensive summary
sink("comprehensive_population_summary.txt")
cat("=== COMPREHENSIVE POPULATION VISUALIZATION SUMMARY ===\n")
cat("Generated on:", as.character(Sys.time()), "\n\n")

cat("POPULATION OVERVIEW:\n")
cat("- Total patients:", summary_stats$population_size, "\n")
cat("- Median age:", summary_stats$median_age, "years (Target: 70-72)\n")
cat("- Male percentage:", summary_stats$male_percentage, "% (Target: ~58%)\n")
cat("- Caucasian percentage:", summary_stats$caucasian_percentage, "% (Target: ~85%)\n\n")

cat("CLINICAL CHARACTERISTICS:\n")
cat("- CVD prevalence:", summary_stats$cvd_prevalence, "% (Target: ~33%)\n")
cat("- Anticoagulant use:", summary_stats$anticoagulant_use, "%\n\n")

if (!is.null(summary_stats$collagen_mean)) {
  cat("PLATELET AGGREGATION RESPONSES (300 nM ibrutinib):\n")
  cat("- Collagen inhibition:", summary_stats$collagen_mean, "% ± ", summary_stats$collagen_sd, "% (Literature: 98%)\n")
  cat("- ADP inhibition:", summary_stats$adp_mean, "% (Literature: 6%)\n")
  cat("- Arachidonic acid inhibition:", summary_stats$arachidonic_mean, "% (Literature: 55%)\n")
  cat("- Overall inhibition score:", summary_stats$overall_score_mean, "%\n")
  cat("- Pathway selectivity ratio:", summary_stats$pathway_selectivity, ":1\n\n")
}

cat("GENERATED VISUALIZATION FILES:\n")
cat("- population_demographics_summary.png\n")
cat("- population_clinical_characteristics.png\n")
if (all(platelet_vars %in% names(population))) {
  cat("- platelet_aggregation_demographics.png\n")
  cat("- platelet_aggregation_literature_validation.png\n")
}
cat("- population_genetic_factors.png\n")
cat("- detailed_literature_validation.csv\n")
cat("- comprehensive_population_summary.txt\n\n")

cat("VALIDATION STATUS: COMPLETED SUCCESSFULLY\n")
cat("All demographic and platelet aggregation visualizations generated\n")
cat("Literature validation completed with comprehensive error analysis\n")
sink()

cat("✓ Comprehensive summary saved to 'comprehensive_population_summary.txt'\n")

cat("\n", paste(rep("=", 60), collapse=""), "\n")
cat("COMPREHENSIVE VISUALIZATION ANALYSIS COMPLETED\n")
cat(paste(rep("=", 60), collapse=""), "\n")

cat("\nKEY DELIVERABLES GENERATED:\n")
cat("✓ Demographics summary plots\n")
cat("✓ Clinical characteristics visualization\n")
cat("✓ Platelet aggregation demographics\n")
cat("✓ Literature validation analysis\n")
cat("✓ Genetic factors visualization\n")
cat("✓ Comprehensive summary report\n")

cat("\nThe synthetic population of", nrow(population), "CLL patients has been\n")
cat("comprehensively analyzed and validated with publication-ready visualizations.\n")

cat("\n=== ANALYSIS COMPLETE ===\n")
