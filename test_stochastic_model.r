# Test script for stochastic aggregation inhibition model
# This script validates the modified PK-PD modeling component

# Load required libraries
library(dplyr)

# Define constants (copied from main file)
FIXED_AGONIST_CONCENTRATIONS <- list(
  collagen = 2.0,        # μg/mL
  ADP = 10.0,           # μM  
  thrombin = 0.5,       # U/mL
  arachidonic_acid = 0.75, # mM
  ristocetin = 1.0      # mg/mL
)

LITERATURE_MAX_INHIBITION <- list(
  collagen = 98,        # %
  ADP = 6,             # %
  adp = 6,             # % - Alternative case
  thrombin = 10,       # %
  arachidonic_acid = 55, # %
  ristocetin = 5       # %
)

STOCHASTIC_PARAMETERS <- list(
  inter_patient_cv = 0.25,    # 25% coefficient of variation
  intra_patient_cv = 0.15,    # 15% coefficient of variation
  baseline_cv = 0.10,         # 10% variation in baseline response
  slope_cv = 0.20            # 20% variation in dose-response slope
)

# Define the stochastic aggregation inhibition function
stochastic_aggregation_inhibition <- function(ibrutinib_conc_nM, agonist_name, 
                                            patient_id = NULL, time_point = NULL) {
  
  # Get maximum inhibition for this agonist from literature
  max_inhibition_pct <- LITERATURE_MAX_INHIBITION[[tolower(agonist_name)]]
  if (is.null(max_inhibition_pct)) {
    warning(paste("Unknown agonist:", agonist_name, "- using default 10% max inhibition"))
    max_inhibition_pct <- 10
  }
  
  # EC50 values calibrated to achieve literature-reported inhibition at clinical dose
  ec50_values <- list(
    collagen = 150,           # nM
    adp = 2000,              # nM
    ADP = 2000,              # nM - Alternative case
    thrombin = 2500,         # nM
    arachidonic_acid = 400,   # nM
    ristocetin = 5000        # nM
  )
  
  ec50 <- ec50_values[[tolower(agonist_name)]]
  if (is.null(ec50)) ec50 <- 1000  # Default EC50
  
  # Hill coefficient for dose-response steepness
  hill_coef <- 1.2
  
  # Base inhibition calculation (deterministic component)
  if (ibrutinib_conc_nM == 0) {
    base_inhibition_pct <- 0
  } else {
    # Sigmoid dose-response: I = Imax * C^n / (EC50^n + C^n)
    base_inhibition_pct <- max_inhibition_pct * 
      (ibrutinib_conc_nM^hill_coef) / (ec50^hill_coef + ibrutinib_conc_nM^hill_coef)
  }
  
  # Add stochastic variability
  # Inter-patient variability (consistent for same patient across time)
  set.seed(ifelse(is.null(patient_id), 12345, patient_id * 1000))
  inter_patient_factor <- rnorm(1, mean = 1.0, 
                               sd = STOCHASTIC_PARAMETERS$inter_patient_cv)
  
  # Intra-patient variability (varies with time/measurement)
  set.seed(ifelse(is.null(time_point), 54321, 
                 as.numeric(patient_id) * 100 + as.numeric(time_point)))
  intra_patient_factor <- rnorm(1, mean = 1.0, 
                               sd = STOCHASTIC_PARAMETERS$intra_patient_cv)
  
  # Apply variability factors
  stochastic_inhibition_pct <- base_inhibition_pct * inter_patient_factor * intra_patient_factor
  
  # Ensure realistic bounds (0-100% inhibition)
  stochastic_inhibition_pct <- pmax(0, pmin(100, stochastic_inhibition_pct))
  
  # Return fraction of activity remaining (1 - inhibition_fraction)
  return(1 - stochastic_inhibition_pct/100)
}

# Test the function
cat("=== TESTING STOCHASTIC AGGREGATION INHIBITION ===\n")

# Test different concentrations for collagen
test_concentrations <- c(0, 100, 200, 300, 400, 500)
cat("Testing collagen inhibition at different concentrations:\n")

for (conc in test_concentrations) {
  inhibition_results <- numeric(5)
  for (i in 1:5) {
    remaining <- stochastic_aggregation_inhibition(conc, "collagen", patient_id = i, time_point = 1)
    inhibition_results[i] <- (1 - remaining) * 100
  }
  cat(sprintf("%.0f nM: %.1f%% ± %.1f%% inhibition\n", 
              conc, mean(inhibition_results), sd(inhibition_results)))
}

# Test all agonists at clinical concentration (300 nM)
cat("\nTesting all agonists at clinical concentration (300 nM):\n")
clinical_conc <- 300

for (agonist in names(FIXED_AGONIST_CONCENTRATIONS)) {
  inhibition_results <- numeric(10)
  for (i in 1:10) {
    remaining <- stochastic_aggregation_inhibition(clinical_conc, agonist, patient_id = i, time_point = 1)
    inhibition_results[i] <- (1 - remaining) * 100
  }
  
  lit_value <- LITERATURE_MAX_INHIBITION[[agonist]]
  cat(sprintf("%s: %.1f%% ± %.1f%% (Literature: %d%%)\n", 
              agonist, mean(inhibition_results), sd(inhibition_results), lit_value))
}

cat("\n=== TEST COMPLETED SUCCESSFULLY ===\n")
