import pandas as pd

# Read the enhanced virtual clinical trial results
df = pd.read_csv('enhanced_virtual_clinical_trial_results.csv')

# Analyze bleeding events by treatment group
print("=== BLEEDING EVENTS ANALYSIS ===\n")

# Group by treatment arm
treatment_groups = df.groupby('treatment_arm')

for group_name, group_data in treatment_groups:
    total_patients = len(group_data)
    
    # Count patients with bleeding events
    bleeding_patients = len(group_data[group_data['bleeding_event_occurred'] == True])
    bleeding_percentage = (bleeding_patients / total_patients) * 100
    
    # Count patients with major bleeding events
    major_bleeding_patients = len(group_data[group_data['major_bleeding_events'] > 0])
    major_bleeding_percentage = (major_bleeding_patients / total_patients) * 100
    
    # Count patients with minor bleeding events
    minor_bleeding_patients = len(group_data[group_data['minor_bleeding_events'] > 0])
    minor_bleeding_percentage = (minor_bleeding_patients / total_patients) * 100
    
    print(f"Treatment Group: {group_name}")
    print(f"Total Patients: {total_patients}")
    print(f"Patients with Any Bleeding Events: {bleeding_patients} ({bleeding_percentage:.1f}%)")
    print(f"Patients with Major Bleeding Events: {major_bleeding_patients} ({major_bleeding_percentage:.1f}%)")
    print(f"Patients with Minor Bleeding Events: {minor_bleeding_patients} ({minor_bleeding_percentage:.1f}%)")
    print(f"Average Total Bleeding Events per Patient: {group_data['bleeding_events'].mean():.1f}")
    print(f"Average Major Bleeding Events per Patient: {group_data['major_bleeding_events'].mean():.1f}")
    print(f"Average Minor Bleeding Events per Patient: {group_data['minor_bleeding_events'].mean():.1f}")
    print("-" * 50)

# Overall summary
print("\n=== OVERALL SUMMARY ===")
total_patients = len(df)
total_bleeding_patients = len(df[df['bleeding_event_occurred'] == True])
total_bleeding_percentage = (total_bleeding_patients / total_patients) * 100

print(f"Total Patients in Study: {total_patients}")
print(f"Total Patients with Bleeding Events: {total_bleeding_patients} ({total_bleeding_percentage:.1f}%)")