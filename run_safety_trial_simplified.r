# =============================================================================
# SIMPLIFIED ENHANCED VIRTUAL CLINICAL TRIAL FOR SAFETY ASSESSMENT
# =============================================================================
# This script runs a simplified version of the enhanced virtual clinical trial
# focusing on safety assessment without the problematic comprehensive model

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
  library(survival)
  library(gridExtra)
})

# Set working directory and seed for reproducibility
set.seed(12345)

cat("=== SIMPLIFIED ENHANCED VIRTUAL CLINICAL TRIAL ===\n")
cat("Focus: Comprehensive safety assessment for Ibrutinib\n\n")

# Load file utilities
source("ml_file_utils.r")

# Load existing synthetic population
cat("Loading existing synthetic patient population...\n")
results_dir <- get_ml_results_dir()
population_file <- build_path(results_dir, "synthetic_patient_population.csv")

if (file.exists(population_file)) {
  population <- read.csv(population_file, stringsAsFactors = FALSE)
  cat("✓ Loaded", nrow(population), "patients from existing population\n")
  
  # Use subset for testing
  n_patients <- min(2000, nrow(population))
  population <- population[1:n_patients, ]
  cat("✓ Using", n_patients, "patients for safety trial\n\n")
} else {
  stop("Synthetic population file not found. Please generate population first.")
}

# Enhanced safety outcome simulation
cat("Simulating enhanced safety outcomes...\n")

# Initialize enhanced safety outcomes
safety_outcomes <- data.frame(
  patient_id = population$patient_id,
  treatment_arm = population$treatment_arm,
  
  # Basic bleeding events (from existing data if available)
  bleeding_events = ifelse("bleeding_events" %in% colnames(population), 
                          population$bleeding_events, 
                          rpois(n_patients, 0.3)),
  
  # Enhanced CTCAE v5.0 classification
  ctcae_max_grade = 0,
  major_bleeding_occurred = FALSE,
  minor_bleeding_occurred = FALSE,
  crnm_bleeding_occurred = FALSE,
  fatal_bleeding = FALSE,
  intracranial_hemorrhage = FALSE,
  gastrointestinal_bleeding = FALSE,
  
  # Time-to-event outcomes
  time_to_first_bleeding = population$treatment_duration_months * 30.44,
  time_to_first_major_bleeding = population$treatment_duration_months * 30.44,
  
  # Dose modifications
  dose_reductions = 0,
  dose_interruptions = 0,
  permanent_discontinuation = FALSE,
  discontinuation_reason = "None",
  
  stringsAsFactors = FALSE
)

# Enhanced bleeding classification using CTCAE v5.0
cat("Applying CTCAE v5.0 bleeding classification...\n")

for (i in 1:n_patients) {
  if (safety_outcomes$bleeding_events[i] > 0) {
    
    # Risk factor-based severity assignment
    age_factor <- ifelse(population$age[i] >= 75, 1.5, 1.0)
    anticoag_factor <- ifelse(population$anticoagulants[i], 2.0, 1.0)
    comorbidity_factor <- ifelse(population$high_comorbidity_score[i], 1.3, 1.0)
    platelet_factor <- ifelse(population$platelet_count[i] < 100, 1.8, 1.0)
    
    risk_multiplier <- age_factor * anticoag_factor * comorbidity_factor * platelet_factor
    
    # Adjusted probabilities based on risk
    if (risk_multiplier > 2.0) {
      grade_probs <- c(0.25, 0.30, 0.30, 0.13, 0.02)  # High risk
    } else if (risk_multiplier > 1.5) {
      grade_probs <- c(0.30, 0.32, 0.25, 0.11, 0.02)  # Moderate-high risk
    } else {
      grade_probs <- c(0.40, 0.35, 0.20, 0.045, 0.005)  # Low risk
    }
    
    # Assign CTCAE grade
    safety_outcomes$ctcae_max_grade[i] <- sample(1:5, 1, prob = grade_probs)
    
    # Classify bleeding types
    if (safety_outcomes$ctcae_max_grade[i] >= 3) {
      safety_outcomes$major_bleeding_occurred[i] <- TRUE
      
      if (safety_outcomes$ctcae_max_grade[i] == 5) {
        safety_outcomes$fatal_bleeding[i] <- TRUE
      }
      
      # Location assignment for major bleeding
      location <- sample(c("ICH", "GI", "Other"), 1, prob = c(0.08, 0.65, 0.27))
      if (location == "ICH") {
        safety_outcomes$intracranial_hemorrhage[i] <- TRUE
      } else if (location == "GI") {
        safety_outcomes$gastrointestinal_bleeding[i] <- TRUE
      }
      
    } else if (safety_outcomes$ctcae_max_grade[i] == 2) {
      safety_outcomes$crnm_bleeding_occurred[i] <- TRUE
    } else if (safety_outcomes$ctcae_max_grade[i] == 1) {
      safety_outcomes$minor_bleeding_occurred[i] <- TRUE
    }
    
    # Time to first bleeding (exponential distribution)
    if (population$treatment_arm[i] == "Ibrutinib_420mg") {
      # Higher hazard for ibrutinib patients
      hazard_rate <- 0.02 * risk_multiplier  # Monthly hazard
      time_to_bleeding <- rexp(1, hazard_rate) * 30.44  # Convert to days
      safety_outcomes$time_to_first_bleeding[i] <- min(time_to_bleeding, 
                                                      population$treatment_duration_months[i] * 30.44)
      
      if (safety_outcomes$major_bleeding_occurred[i]) {
        safety_outcomes$time_to_first_major_bleeding[i] <- safety_outcomes$time_to_first_bleeding[i]
      }
    }
  }
}

# Dose modification simulation
cat("Simulating dose modifications...\n")

for (i in 1:n_patients) {
  if (population$treatment_arm[i] == "Ibrutinib_420mg") {
    
    # Major bleeding triggers dose modifications
    if (safety_outcomes$major_bleeding_occurred[i]) {
      if (safety_outcomes$fatal_bleeding[i] || safety_outcomes$intracranial_hemorrhage[i]) {
        safety_outcomes$permanent_discontinuation[i] <- TRUE
        safety_outcomes$discontinuation_reason[i] <- "Major_bleeding"
      } else {
        safety_outcomes$dose_reductions[i] <- 1
      }
    }
    
    # CRNM bleeding may trigger interruptions
    if (safety_outcomes$crnm_bleeding_occurred[i] && runif(1) < 0.3) {
      safety_outcomes$dose_interruptions[i] <- 1
    }
    
    # Multiple bleeding events increase modification risk
    if (safety_outcomes$bleeding_events[i] > 2) {
      safety_outcomes$dose_reductions[i] <- safety_outcomes$dose_reductions[i] + 1
    }
  }
}

cat("✓ Enhanced safety outcomes simulated\n\n")

# Combine with population data
trial_results <- cbind(population, safety_outcomes[, !colnames(safety_outcomes) %in% colnames(population)])

# Save results
results_file <- build_path(results_dir, "simplified_enhanced_trial_results.csv")
safe_csv_save(trial_results, results_file)
cat("✓ Trial results saved to:", results_file, "\n\n")

# Generate comprehensive safety summary
cat("=== COMPREHENSIVE SAFETY SUMMARY ===\n")

# Overall safety metrics
ibrutinib_patients <- trial_results[trial_results$treatment_arm == "Ibrutinib_420mg", ]
control_patients <- trial_results[trial_results$treatment_arm == "Control", ]

cat("IBRUTINIB PATIENTS (n =", nrow(ibrutinib_patients), "):\n")
cat("- Any bleeding rate:", round(mean(ibrutinib_patients$bleeding_events > 0) * 100, 1), "%\n")
cat("- Major bleeding rate:", round(mean(ibrutinib_patients$major_bleeding_occurred) * 100, 1), "%\n")
cat("- CRNM bleeding rate:", round(mean(ibrutinib_patients$crnm_bleeding_occurred) * 100, 1), "%\n")
cat("- Minor bleeding rate:", round(mean(ibrutinib_patients$minor_bleeding_occurred) * 100, 1), "%\n")
cat("- Fatal bleeding rate:", round(mean(ibrutinib_patients$fatal_bleeding) * 100, 1), "%\n")
cat("- ICH rate:", round(mean(ibrutinib_patients$intracranial_hemorrhage) * 100, 1), "%\n")
cat("- GI bleeding rate:", round(mean(ibrutinib_patients$gastrointestinal_bleeding) * 100, 1), "%\n")
cat("- Dose reduction rate:", round(mean(ibrutinib_patients$dose_reductions > 0) * 100, 1), "%\n")
cat("- Discontinuation rate:", round(mean(ibrutinib_patients$permanent_discontinuation) * 100, 1), "%\n")

if (nrow(control_patients) > 0) {
  cat("\nCONTROL PATIENTS (n =", nrow(control_patients), "):\n")
  cat("- Any bleeding rate:", round(mean(control_patients$bleeding_events > 0) * 100, 1), "%\n")
  cat("- Major bleeding rate:", round(mean(control_patients$major_bleeding_occurred) * 100, 1), "%\n")
}

cat("\n=== SIMPLIFIED ENHANCED VIRTUAL CLINICAL TRIAL COMPLETED ===\n")
cat("✓ Comprehensive safety assessment completed successfully\n")
cat("✓ CTCAE v5.0 classification implemented\n")
cat("✓ Dose modification patterns analyzed\n")
cat("✓ Results saved for further analysis\n")
