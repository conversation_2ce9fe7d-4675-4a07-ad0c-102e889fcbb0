# =============================================================================
# COMPREHENSIVE SAFETY VISUALIZATIONS AND TABLES
# =============================================================================
# Generate regulatory-standard safety visualizations and ICH E3 tables

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
  library(survival)
  library(survminer)
  library(gridExtra)
})

cat("=== GENERATING COMPREHENSIVE SAFETY VISUALIZATIONS ===\n\n")

# Load file utilities and safety data
source("ml_file_utils.r")
results_dir <- get_ml_results_dir()
safety_file <- build_path(results_dir, "comprehensive_safety_analysis.csv")

safety_data <- read.csv(safety_file, stringsAsFactors = FALSE)
cat("Loaded safety data for", nrow(safety_data), "patients\n\n")

# 1. ICH E3 Compliant Safety Tables
cat("Generating ICH E3 compliant safety tables...\n")

# Overall Safety Summary Table
safety_summary <- safety_data %>%
  group_by(treatment_arm) %>%
  summarise(
    N = n(),
    `Any Bleeding n (%)` = paste0(sum(any_bleeding), " (", round(mean(any_bleeding)*100, 1), ")"),
    `Major Bleeding n (%)` = paste0(sum(major_bleeding), " (", round(mean(major_bleeding)*100, 1), ")"),
    `CRNM Bleeding n (%)` = paste0(sum(crnm_bleeding), " (", round(mean(crnm_bleeding)*100, 1), ")"),
    `Minor Bleeding n (%)` = paste0(sum(minor_bleeding), " (", round(mean(minor_bleeding)*100, 1), ")"),
    `Fatal Bleeding n (%)` = paste0(sum(fatal_bleeding), " (", round(mean(fatal_bleeding)*100, 1), ")"),
    `ICH n (%)` = paste0(sum(ich_bleeding), " (", round(mean(ich_bleeding)*100, 1), ")"),
    `GI Bleeding n (%)` = paste0(sum(gi_bleeding), " (", round(mean(gi_bleeding)*100, 1), ")"),
    `Dose Reduction n (%)` = paste0(sum(dose_reduction), " (", round(mean(dose_reduction)*100, 1), ")"),
    `Discontinuation n (%)` = paste0(sum(permanent_discontinuation), " (", round(mean(permanent_discontinuation)*100, 1), ")"),
    .groups = 'drop'
  )

# CTCAE Grade Distribution
ctcae_summary <- safety_data %>%
  filter(any_bleeding) %>%
  group_by(treatment_arm) %>%
  summarise(
    N_with_bleeding = n(),
    `Grade 1 n (%)` = paste0(sum(ctcae_max_grade == 1), " (", round(mean(ctcae_max_grade == 1)*100, 1), ")"),
    `Grade 2 n (%)` = paste0(sum(ctcae_max_grade == 2), " (", round(mean(ctcae_max_grade == 2)*100, 1), ")"),
    `Grade 3 n (%)` = paste0(sum(ctcae_max_grade == 3), " (", round(mean(ctcae_max_grade == 3)*100, 1), ")"),
    `Grade 4 n (%)` = paste0(sum(ctcae_max_grade == 4), " (", round(mean(ctcae_max_grade == 4)*100, 1), ")"),
    `Grade 5 n (%)` = paste0(sum(ctcae_max_grade == 5), " (", round(mean(ctcae_max_grade == 5)*100, 1), ")"),
    .groups = 'drop'
  )

# Save tables
safe_csv_save(safety_summary, build_path(results_dir, "ich_e3_safety_summary.csv"))
safe_csv_save(ctcae_summary, build_path(results_dir, "ctcae_grade_distribution.csv"))

cat("✓ ICH E3 safety tables generated\n\n")

# 2. Kaplan-Meier Survival Curves
cat("Generating Kaplan-Meier survival curves...\n")

# Prepare survival data
surv_data <- safety_data %>%
  filter(treatment_arm == "Ibrutinib_420mg") %>%
  mutate(
    bleeding_event = as.numeric(any_bleeding),
    major_bleeding_event = as.numeric(major_bleeding)
  )

# Any bleeding survival curve
km_any <- survfit(Surv(time_to_first_bleeding_days, bleeding_event) ~ 1, data = surv_data)

p_km_any <- ggsurvplot(
  km_any,
  data = surv_data,
  title = "Time to First Bleeding Event (Ibrutinib 420mg)",
  xlab = "Time (days)",
  ylab = "Bleeding-free survival probability",
  risk.table = TRUE,
  conf.int = TRUE,
  palette = "blue",
  ggtheme = theme_minimal()
)

# Major bleeding survival curve
km_major <- survfit(Surv(time_to_major_bleeding_days, major_bleeding_event) ~ 1, data = surv_data)

p_km_major <- ggsurvplot(
  km_major,
  data = surv_data,
  title = "Time to First Major Bleeding Event (Ibrutinib 420mg)",
  xlab = "Time (days)",
  ylab = "Major bleeding-free survival probability",
  risk.table = TRUE,
  conf.int = TRUE,
  palette = "red",
  ggtheme = theme_minimal()
)

# Save Kaplan-Meier plots
ggsave(build_path(results_dir, "kaplan_meier_any_bleeding.png"), 
       p_km_any$plot, width = 12, height = 8)
ggsave(build_path(results_dir, "kaplan_meier_major_bleeding.png"), 
       p_km_major$plot, width = 12, height = 8)

cat("✓ Kaplan-Meier curves generated\n\n")

# 3. Safety Subgroup Analysis Forest Plot
cat("Generating forest plot for subgroup analysis...\n")

# Prepare subgroup data
subgroup_data <- safety_data %>%
  filter(treatment_arm == "Ibrutinib_420mg") %>%
  mutate(
    age_group = case_when(
      age < 65 ~ "Age <65",
      age >= 65 & age < 75 ~ "Age 65-74",
      age >= 75 ~ "Age ≥75"
    ),
    anticoag_group = ifelse(anticoagulants, "Anticoagulant use", "No anticoagulant"),
    comorbidity_group = ifelse(high_comorbidity_score, "High comorbidity", "Low comorbidity")
  )

# Calculate subgroup rates
forest_data <- data.frame()

subgroups <- list(
  "Age" = c("Age <65", "Age 65-74", "Age ≥75"),
  "Anticoagulant" = c("Anticoagulant use", "No anticoagulant"),
  "Comorbidity" = c("High comorbidity", "Low comorbidity")
)

for (category in names(subgroups)) {
  for (subgroup in subgroups[[category]]) {
    
    if (category == "Age") {
      subset_data <- subgroup_data[subgroup_data$age_group == subgroup, ]
    } else if (category == "Anticoagulant") {
      subset_data <- subgroup_data[subgroup_data$anticoag_group == subgroup, ]
    } else if (category == "Comorbidity") {
      subset_data <- subgroup_data[subgroup_data$comorbidity_group == subgroup, ]
    }
    
    if (nrow(subset_data) > 10) {
      n_patients <- nrow(subset_data)
      n_events <- sum(subset_data$major_bleeding)
      rate <- mean(subset_data$major_bleeding) * 100
      
      # Calculate 95% CI
      if (n_events > 0) {
        ci <- binom.test(n_events, n_patients)$conf.int * 100
      } else {
        ci <- c(0, 0)
      }
      
      forest_data <- rbind(forest_data, data.frame(
        Category = category,
        Subgroup = subgroup,
        N = n_patients,
        Events = n_events,
        Rate = rate,
        Lower_CI = ci[1],
        Upper_CI = ci[2],
        stringsAsFactors = FALSE
      ))
    }
  }
}

# Create forest plot
p_forest <- ggplot(forest_data, aes(x = Rate, y = reorder(paste(Category, Subgroup, sep = ": "), Rate))) +
  geom_point(size = 3, color = "blue") +
  geom_errorbarh(aes(xmin = Lower_CI, xmax = Upper_CI), height = 0.2) +
  geom_text(aes(label = paste0(Events, "/", N, " (", sprintf("%.1f", Rate), "%)")), 
            hjust = -0.1, size = 3) +
  labs(
    title = "Major Bleeding Rates by Subgroup (Ibrutinib 420mg)",
    subtitle = "Forest plot with 95% confidence intervals",
    x = "Major Bleeding Rate (%)",
    y = "Subgroup"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 12),
    axis.text.y = element_text(size = 10),
    axis.text.x = element_text(size = 10)
  ) +
  xlim(0, max(forest_data$Upper_CI) * 1.3)

ggsave(build_path(results_dir, "safety_forest_plot.png"), p_forest, width = 12, height = 8)

cat("✓ Forest plot generated\n\n")

# 4. Additional Safety Visualizations
cat("Generating additional safety visualizations...\n")

# Bleeding severity distribution
p_severity <- safety_data %>%
  filter(treatment_arm == "Ibrutinib_420mg" & any_bleeding) %>%
  mutate(
    severity = case_when(
      ctcae_max_grade == 1 ~ "Grade 1 (Minor)",
      ctcae_max_grade == 2 ~ "Grade 2 (CRNM)",
      ctcae_max_grade == 3 ~ "Grade 3 (Major)",
      ctcae_max_grade == 4 ~ "Grade 4 (Severe)",
      ctcae_max_grade == 5 ~ "Grade 5 (Fatal)"
    )
  ) %>%
  ggplot(aes(x = severity, fill = severity)) +
  geom_bar() +
  labs(
    title = "Distribution of Bleeding Severity (CTCAE v5.0)",
    subtitle = "Ibrutinib 420mg patients with bleeding events",
    x = "CTCAE Grade",
    y = "Number of Patients"
  ) +
  theme_minimal() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1),
    legend.position = "none"
  ) +
  scale_fill_brewer(palette = "Reds")

ggsave(build_path(results_dir, "bleeding_severity_distribution.png"), p_severity, width = 10, height = 6)

# Time to bleeding histogram
p_time <- safety_data %>%
  filter(treatment_arm == "Ibrutinib_420mg" & any_bleeding) %>%
  ggplot(aes(x = time_to_first_bleeding_days)) +
  geom_histogram(bins = 30, fill = "steelblue", alpha = 0.7) +
  labs(
    title = "Time to First Bleeding Event",
    subtitle = "Ibrutinib 420mg patients",
    x = "Time to First Bleeding (days)",
    y = "Number of Patients"
  ) +
  theme_minimal()

ggsave(build_path(results_dir, "time_to_bleeding_histogram.png"), p_time, width = 10, height = 6)

cat("✓ Additional visualizations generated\n\n")

# Print summary
cat("=== SAFETY VISUALIZATION SUMMARY ===\n")
cat("Generated files:\n")
cat("- ich_e3_safety_summary.csv\n")
cat("- ctcae_grade_distribution.csv\n")
cat("- kaplan_meier_any_bleeding.png\n")
cat("- kaplan_meier_major_bleeding.png\n")
cat("- safety_forest_plot.png\n")
cat("- bleeding_severity_distribution.png\n")
cat("- time_to_bleeding_histogram.png\n\n")

cat("=== COMPREHENSIVE SAFETY VISUALIZATIONS COMPLETED ===\n")
cat("✓ ICH E3 compliant tables generated\n")
cat("✓ Kaplan-Meier survival curves created\n")
cat("✓ Forest plots for subgroup analysis completed\n")
cat("✓ Additional safety visualizations generated\n")
