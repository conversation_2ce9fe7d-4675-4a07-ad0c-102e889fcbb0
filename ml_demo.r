# =============================================================================
# ML DEMONSTRATION SCRIPT
# =============================================================================
# This script demonstrates the ML optimization strategies
# using only commonly available R packages

# Load file utilities first
if (file.exists("ml_file_utils.r")) {
  source("ml_file_utils.r")
} else {
  cat("Warning: ml_file_utils.r not found. Using basic file operations.\n")
}

# Load required libraries with error handling
required_packages <- c("randomForest", "xgboost", "glmnet", "caret", "pROC", "dplyr", 
                      "parallel", "doParallel")

# Load required packages
for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
    stop(paste("Required package", pkg, "is not installed. Please install it using: install.packages('", pkg, "')"))
  }
}

cat("ML Demo module loaded successfully\n")

set.seed(42)

# =============================================================================
# ADVANCED FEATURE ENGINEERING
# =============================================================================

#' Advanced Feature Engineering
advanced_feature_engineering <- function(data, target_col, verbose = TRUE) {
  
  if (verbose) cat("Applying advanced feature engineering...\n")
  
  # Separate features and target
  if (target_col %in% names(data)) {
    features <- data[, !names(data) %in% target_col]
    target <- data[[target_col]]
  } else {
    features <- data
    target <- NULL
  }
  
  # Convert all features to numeric (encode categorical variables)
  for (col in names(features)) {
    if (is.character(features[[col]]) || is.factor(features[[col]])) {
      # Convert categorical to numeric using simple encoding
      unique_vals <- unique(features[[col]])
      unique_vals <- unique_vals[!is.na(unique_vals)]
      
      if (length(unique_vals) <= 10) {
        # One-hot encoding for low cardinality
        for (val in unique_vals) {
          new_col_name <- paste0(col, "_", gsub("[^A-Za-z0-9]", "_", val))
          features[[new_col_name]] <- as.numeric(features[[col]] == val)
        }
        # Remove original column
        features[[col]] <- NULL
      } else {
        # Label encoding for high cardinality
        features[[col]] <- as.numeric(as.factor(features[[col]]))
      }
    }
  }
  
  # Ensure all columns are numeric
  numeric_cols <- sapply(features, is.numeric)
  features <- features[, numeric_cols, drop = FALSE]
  
  # Handle missing values with advanced imputation
  for (col in names(features)) {
    if (any(is.na(features[[col]]))) {
      # Use median for all numeric features
      features[[col]][is.na(features[[col]])] <- median(features[[col]], na.rm = TRUE)
    }
  }
  
  # Create interaction features for top variables
  if (ncol(features) >= 2) {
    # Create interactions between top correlated features
    cor_matrix <- cor(features, use = "complete.obs")
    
    # Find top correlated pairs
    cor_matrix[upper.tri(cor_matrix, diag = TRUE)] <- NA
    high_cor_pairs <- which(abs(cor_matrix) > 0.3, arr.ind = TRUE)
    
    if (nrow(high_cor_pairs) > 0 && nrow(high_cor_pairs) <= 5) {
      for (i in 1:min(nrow(high_cor_pairs), 3)) {
        row_idx <- high_cor_pairs[i, 1]
        col_idx <- high_cor_pairs[i, 2]
        
        col1 <- colnames(features)[row_idx]
        col2 <- colnames(features)[col_idx]
        
        interaction_name <- paste0(col1, "_x_", col2)
        features[[interaction_name]] <- features[[col1]] * features[[col2]]
      }
    }
  }
  
  # Normalize all numeric features
  for (col in names(features)) {
    col_mean <- mean(features[[col]], na.rm = TRUE)
    col_sd <- sd(features[[col]], na.rm = TRUE)
    if (col_sd > 0) {
      features[[col]] <- (features[[col]] - col_mean) / col_sd
    } else {
      features[[col]] <- 0  # Constant features set to 0
    }
  }
  
  # Final check: ensure all features are numeric
  features <- as.data.frame(lapply(features, as.numeric))
  
  if (verbose) {
    cat(sprintf("  Created %d numeric features\n", ncol(features)))
  }
  
  return(list(features = features, target = target))
}

# =============================================================================
# ENSEMBLE METHODS
# =============================================================================

#' Simple Stacking Ensemble
simple_stacking_ensemble <- function(X_train, y_train, X_test, y_test, verbose = TRUE) {
  
  if (verbose) cat("Training stacking ensemble...\n")
  
  # Create cross-validation folds for meta-features
  cv_folds <- createFolds(y_train, k = 5)
  
  # Initialize meta-features
  meta_features_train <- matrix(0, nrow = nrow(X_train), ncol = 3)
  meta_features_test <- matrix(0, nrow = nrow(X_test), ncol = 3)
  
  base_models <- list()
  
  # Base learner 1: Random Forest
  fold_predictions_rf <- numeric(nrow(X_train))
  test_predictions_rf <- list()
  
  for (fold in 1:5) {
    train_idx <- unlist(cv_folds[-fold])
    val_idx <- cv_folds[[fold]]
    
    # Train RF on fold
    rf_model <- randomForest(x = X_train[train_idx, ], y = as.factor(y_train[train_idx]), 
                            ntree = 100, mtry = sqrt(ncol(X_train)))
    
    # Predict on validation fold
    fold_pred <- predict(rf_model, X_train[val_idx, ], type = "prob")[, 2]
    fold_predictions_rf[val_idx] <- fold_pred
    
    # Predict on test set
    test_pred <- predict(rf_model, X_test, type = "prob")[, 2]
    test_predictions_rf[[fold]] <- test_pred
  }
  
  meta_features_train[, 1] <- fold_predictions_rf
  meta_features_test[, 1] <- Reduce("+", test_predictions_rf) / length(test_predictions_rf)
  
  # Train final RF model
  base_models$rf <- randomForest(x = X_train, y = as.factor(y_train), ntree = 100)
  
  # Base learner 2: XGBoost
  fold_predictions_xgb <- numeric(nrow(X_train))
  test_predictions_xgb <- list()
  
  for (fold in 1:5) {
    train_idx <- unlist(cv_folds[-fold])
    val_idx <- cv_folds[[fold]]
    
    # Train XGBoost on fold
    dtrain <- xgb.DMatrix(data = as.matrix(X_train[train_idx, ]), label = y_train[train_idx])
    dval <- xgb.DMatrix(data = as.matrix(X_train[val_idx, ]))
    dtest <- xgb.DMatrix(data = as.matrix(X_test))
    
    xgb_model <- xgb.train(
      params = list(objective = "binary:logistic", max_depth = 6, eta = 0.1),
      data = dtrain, nrounds = 100, verbose = 0
    )
    
    fold_pred <- predict(xgb_model, dval)
    fold_predictions_xgb[val_idx] <- fold_pred
    
    test_pred <- predict(xgb_model, dtest)
    test_predictions_xgb[[fold]] <- test_pred
  }
  
  meta_features_train[, 2] <- fold_predictions_xgb
  meta_features_test[, 2] <- Reduce("+", test_predictions_xgb) / length(test_predictions_xgb)
  
  # Train final XGBoost model
  dtrain_full <- xgb.DMatrix(data = as.matrix(X_train), label = y_train)
  base_models$xgb <- xgb.train(
    params = list(objective = "binary:logistic", max_depth = 6, eta = 0.1),
    data = dtrain_full, nrounds = 100, verbose = 0
  )
  
  # Base learner 3: Logistic Regression
  fold_predictions_glm <- numeric(nrow(X_train))
  test_predictions_glm <- list()
  
  for (fold in 1:5) {
    train_idx <- unlist(cv_folds[-fold])
    val_idx <- cv_folds[[fold]]
    
    # Train GLM on fold
    glm_model <- cv.glmnet(as.matrix(X_train[train_idx, ]), y_train[train_idx], 
                          family = "binomial", alpha = 0.5)
    
    fold_pred <- predict(glm_model, as.matrix(X_train[val_idx, ]), 
                        s = "lambda.min", type = "response")[, 1]
    fold_predictions_glm[val_idx] <- fold_pred
    
    test_pred <- predict(glm_model, as.matrix(X_test), 
                        s = "lambda.min", type = "response")[, 1]
    test_predictions_glm[[fold]] <- test_pred
  }
  
  meta_features_train[, 3] <- fold_predictions_glm
  meta_features_test[, 3] <- Reduce("+", test_predictions_glm) / length(test_predictions_glm)
  
  # Train final GLM model
  base_models$glm <- cv.glmnet(as.matrix(X_train), y_train, family = "binomial", alpha = 0.5)
  
  # Train meta-learner (simple logistic regression)
  meta_model <- glm(y_train ~ ., data = data.frame(meta_features_train), family = "binomial")
  
  # Make final predictions
  final_predictions <- predict(meta_model, data.frame(meta_features_test), type = "response")
  
  # Calculate performance
  performance <- calculate_performance_metrics(y_test, final_predictions)
  
  if (verbose) {
    cat(sprintf("  Stacking Ensemble AUC: %.4f\n", performance$auc))
  }
  
  return(list(
    predictions = final_predictions,
    performance = performance,
    base_models = base_models,
    meta_model = meta_model,
    method = "stacking"
  ))
}

# =============================================================================
# HYPERPARAMETER OPTIMIZATION
# =============================================================================

#' Simple Grid Search for XGBoost
simple_xgb_tuning <- function(X_train, y_train, X_val, y_val, verbose = TRUE) {
  
  if (verbose) cat("Tuning XGBoost hyperparameters...\n")
  
  # Define parameter grid
  param_grid <- expand.grid(
    max_depth = c(3, 6, 9),
    eta = c(0.01, 0.1, 0.3),
    nrounds = c(50, 100, 200)
  )
  
  best_auc <- 0
  best_params <- NULL
  
  dtrain <- xgb.DMatrix(data = as.matrix(X_train), label = y_train)
  dval <- xgb.DMatrix(data = as.matrix(X_val))
  
  for (i in 1:nrow(param_grid)) {
    params <- param_grid[i, ]
    
    model <- xgb.train(
      params = list(
        objective = "binary:logistic",
        max_depth = params$max_depth,
        eta = params$eta
      ),
      data = dtrain,
      nrounds = params$nrounds,
      verbose = 0
    )
    
    pred <- predict(model, dval)
    
    if (length(unique(y_val)) > 1) {
      roc_obj <- roc(y_val, pred, quiet = TRUE)
      auc_score <- as.numeric(roc_obj$auc)
      
      if (auc_score > best_auc) {
        best_auc <- auc_score
        best_params <- params
      }
    }
  }
  
  if (verbose && !is.null(best_params)) {
    cat(sprintf("  Best XGBoost AUC: %.4f\n", best_auc))
    cat(sprintf("  Best params: max_depth=%d, eta=%.2f, nrounds=%d\n", 
                best_params$max_depth, best_params$eta, best_params$nrounds))
  }
  
  return(list(
    best_params = best_params,
    best_auc = best_auc
  ))
}

# =============================================================================
# CLASS IMBALANCE HANDLING
# =============================================================================

#' Simple SMOTE-like Oversampling
simple_oversampling <- function(X, y, target_ratio = 0.5, verbose = TRUE) {
  
  if (verbose) cat("Applying oversampling for class imbalance...\n")
  
  class_counts <- table(y)
  minority_class <- names(class_counts)[which.min(class_counts)]
  majority_class <- names(class_counts)[which.max(class_counts)]
  
  minority_indices <- which(y == minority_class)
  majority_indices <- which(y == majority_class)
  
  if (verbose) {
    cat(sprintf("  Original distribution: %s=%d, %s=%d\n", 
                minority_class, length(minority_indices),
                majority_class, length(majority_indices)))
  }
  
  # Calculate how many minority samples to generate
  target_minority_count <- round(length(majority_indices) * target_ratio / (1 - target_ratio))
  samples_to_generate <- target_minority_count - length(minority_indices)
  
  if (samples_to_generate > 0) {
    # Simple oversampling with noise
    oversample_indices <- sample(minority_indices, samples_to_generate, replace = TRUE)
    
    X_oversample <- X[oversample_indices, ]
    y_oversample <- y[oversample_indices]
    
    # Add small amount of noise to numeric features
    numeric_cols <- sapply(X_oversample, is.numeric)
    for (col in names(X_oversample)[numeric_cols]) {
      noise <- rnorm(nrow(X_oversample), 0, sd(X_oversample[[col]], na.rm = TRUE) * 0.1)
      X_oversample[[col]] <- X_oversample[[col]] + noise
    }
    
    X_balanced <- rbind(X, X_oversample)
    y_balanced <- c(y, y_oversample)
  } else {
    X_balanced <- X
    y_balanced <- y
  }
  
  if (verbose) {
    balanced_counts <- table(y_balanced)
    cat(sprintf("  Balanced distribution: %s\n", 
                paste(names(balanced_counts), balanced_counts, collapse = ", ")))
  }
  
  return(list(X = X_balanced, y = y_balanced))
}

# =============================================================================
# PERFORMANCE METRICS
# =============================================================================

# Load standardized performance utilities
if (!exists("calculate_standardized_performance")) {
  source("ml_performance_utils.r")
}

#' Calculate Performance Metrics (Wrapper for standardized function)
calculate_performance_metrics <- function(y_true, y_pred, threshold = 0.5) {
  
  # Use standardized performance calculation
  performance <- calculate_standardized_performance(
    y_true = y_true,
    y_pred = y_pred,
    threshold = threshold,
    task_type = "classification"
  )
  
  return(performance)
}

# =============================================================================
# MAIN DEMONSTRATION FUNCTION
# =============================================================================

#' ML Demonstration
ml_demo <- function() {
  
  cat("\n" %+% rep("=", 60) %+% "\n")
  cat("MACHINE LEARNING DEMONSTRATION\n")
  cat(rep("=", 60) %+% "\n")
  
  # Load data
  if (file.exists("virtual_clinical_trial_results.csv")) {
    trial_data <- safe_csv_load("virtual_clinical_trial_results.csv")
  } else if (file.exists("enhanced_virtual_clinical_trial_results.csv")) {
    trial_data <- safe_csv_load("enhanced_virtual_clinical_trial_results.csv")
  } else {
    cat("Error: Virtual clinical trial data not found.\n")
    cat("Please run the complete system first to generate trial data.\n")
    return(NULL)
  }
  
  # Apply column mappings for compatibility
  column_mappings <- list(
    "bleeding_event" = c("bleeding_event_occurred", "bleeding_event"),
    "major_bleeding" = c("major_bleeding_events", "major_bleeding"),
    "bleeding_risk_score" = c("annual_bleeding_risk", "bleeding_risk_score"),
    "bleeding_risk" = c("annual_bleeding_risk", "bleeding_risk_score", "bleeding_risk")
  )
  
  available_cols <- names(trial_data)
  for (mapped_name in names(column_mappings)) {
    mapped_cols <- column_mappings[[mapped_name]]
    found_col <- intersect(mapped_cols, available_cols)
    if (length(found_col) > 0 && !mapped_name %in% names(trial_data)) {
      trial_data[[mapped_name]] <- trial_data[[found_col[1]]]
    }
  }
  
  cat(sprintf("Loaded trial data: %d patients, %d variables\n", nrow(trial_data), ncol(trial_data)))
  
  # Process each target
  targets <- c("bleeding_risk", "major_bleeding", "collagen_inhibition")
  results <- list()
  
  for (target in targets) {
    cat(sprintf("\n" %+% rep("-", 40) %+% "\n"))
    cat(sprintf("PROCESSING TARGET: %s\n", toupper(target)))
    cat(rep("-", 40) %+% "\n")
    
    # Create target variable
    if (target == "bleeding_risk") {
      if ("bleeding_event" %in% names(trial_data)) {
        trial_data$bleeding_risk <- as.numeric(trial_data$bleeding_event > 0)
      } else if ("bleeding_risk_score" %in% names(trial_data)) {
        trial_data$bleeding_risk <- as.numeric(trial_data$bleeding_risk_score > median(trial_data$bleeding_risk_score, na.rm = TRUE))
      } else {
        cat(sprintf("Warning: Cannot create target %s - required columns not found\n", target))
        next
      }
    } else if (target == "major_bleeding") {
      if ("major_bleeding_event" %in% names(trial_data)) {
        trial_data$major_bleeding <- as.numeric(trial_data$major_bleeding_event > 0)
      } else if ("bleeding_risk_score" %in% names(trial_data)) {
        trial_data$major_bleeding <- as.numeric(trial_data$bleeding_risk_score > quantile(trial_data$bleeding_risk_score, 0.75, na.rm = TRUE))
      } else {
        cat(sprintf("Warning: Cannot create target %s - required columns not found\n", target))
        next
      }
    } else if (target == "collagen_inhibition") {
      if ("collagen_inhibition_score" %in% names(trial_data)) {
        trial_data$collagen_inhibition <- as.numeric(trial_data$collagen_inhibition_score > median(trial_data$collagen_inhibition_score, na.rm = TRUE))
      } else if ("platelet_aggregation" %in% names(trial_data)) {
        # Use platelet aggregation as proxy for collagen inhibition
        trial_data$collagen_inhibition <- as.numeric(trial_data$platelet_aggregation < median(trial_data$platelet_aggregation, na.rm = TRUE))
      } else {
        cat(sprintf("Warning: Cannot create target %s - required columns not found\n", target))
        next
      }
    }
    
    # Check if target exists and has variation
    if (!target %in% names(trial_data)) {
      cat(sprintf("Warning: Target %s not found, skipping...\n", target))
      next
    }
    
    target_values <- trial_data[[target]]
    if (length(unique(target_values)) < 2 || sum(!is.na(target_values)) < 50) {
      cat(sprintf("Warning: Insufficient data for target %s, skipping...\n", target))
      next
    }
    
    # Step 1: Advanced Feature Engineering
    cat("\n1. Advanced Feature Engineering\n")
    feature_data <- advanced_feature_engineering(trial_data, target, verbose = TRUE)
    
    X <- feature_data$features
    y <- feature_data$target
    
    # Remove rows with missing target
    complete_cases <- !is.na(y)
    X <- X[complete_cases, ]
    y <- y[complete_cases]
    
    # Step 2: Handle Class Imbalance
    cat("\n2. Class Imbalance Handling\n")
    class_dist <- table(y)
    minority_ratio <- min(class_dist) / sum(class_dist)
    
    if (minority_ratio < 0.3) {
      balanced_data <- simple_oversampling(X, y, verbose = TRUE)
      X_balanced <- balanced_data$X
      y_balanced <- balanced_data$y
    } else {
      cat("  Classes are reasonably balanced, no resampling needed.\n")
      X_balanced <- X
      y_balanced <- y
    }
    
    # Step 3: Train-Test Split
    cat("\n3. Data Splitting\n")
    set.seed(42)
    train_idx <- createDataPartition(y_balanced, p = 0.7, list = FALSE)
    val_idx <- createDataPartition(y_balanced[-train_idx], p = 0.5, list = FALSE)
    
    X_train <- X_balanced[train_idx, ]
    y_train <- y_balanced[train_idx]
    
    remaining_idx <- setdiff(1:nrow(X_balanced), train_idx)
    X_val <- X_balanced[remaining_idx[val_idx], ]
    y_val <- y_balanced[remaining_idx[val_idx]]
    X_test <- X_balanced[remaining_idx[-val_idx], ]
    y_test <- y_balanced[remaining_idx[-val_idx]]
    
    cat(sprintf("  Training: %d, Validation: %d, Test: %d samples\n", 
                nrow(X_train), nrow(X_val), nrow(X_test)))
    
    # Step 4: Model Training and Optimization
    cat("\n4. Advanced Model Training\n")
    
    target_results <- list()
    
    # 4a. Baseline Random Forest
    cat("\n4a. Baseline Random Forest\n")
    rf_model <- randomForest(x = X_train, y = as.factor(y_train), ntree = 200)
    rf_pred <- predict(rf_model, X_test, type = "prob")[, 2]
    rf_performance <- calculate_performance_metrics(y_test, rf_pred)
    target_results$baseline_rf <- list(predictions = rf_pred, performance = rf_performance)
    cat(sprintf("  Baseline RF AUC: %.4f\n", rf_performance$auc))
    
    # 4b. Hyperparameter Tuned XGBoost
    cat("\n4b. Hyperparameter Tuned XGBoost\n")
    xgb_tuning <- simple_xgb_tuning(X_train, y_train, X_val, y_val, verbose = TRUE)
    
    # Train final XGBoost with best parameters
    if (!is.null(xgb_tuning$best_params)) {
      dtrain <- xgb.DMatrix(data = as.matrix(X_train), label = y_train)
      dtest <- xgb.DMatrix(data = as.matrix(X_test))
      
      xgb_model <- xgb.train(
        params = list(
          objective = "binary:logistic",
          max_depth = xgb_tuning$best_params$max_depth,
          eta = xgb_tuning$best_params$eta
        ),
        data = dtrain,
        nrounds = xgb_tuning$best_params$nrounds,
        verbose = 0
      )
      
      xgb_pred <- predict(xgb_model, dtest)
      xgb_performance <- calculate_performance_metrics(y_test, xgb_pred)
      target_results$tuned_xgb <- list(predictions = xgb_pred, performance = xgb_performance)
      cat(sprintf("  Tuned XGBoost AUC: %.4f\n", xgb_performance$auc))
    }
    
    # 4c. Regularized Logistic Regression
    cat("\n4c. Regularized Logistic Regression\n")
    glm_model <- cv.glmnet(as.matrix(X_train), y_train, family = "binomial", alpha = 0.5)
    glm_pred <- predict(glm_model, as.matrix(X_test), s = "lambda.min", type = "response")[, 1]
    glm_performance <- calculate_performance_metrics(y_test, glm_pred)
    target_results$regularized_glm <- list(predictions = glm_pred, performance = glm_performance)
    cat(sprintf("  Regularized GLM AUC: %.4f\n", glm_performance$auc))
    
    # 4d. Stacking Ensemble
    cat("\n4d. Stacking Ensemble\n")
    if (nrow(X_train) >= 100) {  # Only if enough data
      stacking_result <- simple_stacking_ensemble(X_train, y_train, X_test, y_test, verbose = TRUE)
      target_results$stacking <- stacking_result
    } else {
      cat("  Insufficient data for stacking ensemble\n")
    }
    
    # Step 5: Performance Comparison
    cat("\n5. Performance Comparison\n")
    
    comparison_df <- data.frame(
      Model = character(),
      AUC = numeric(),
      Accuracy = numeric(),
      Sensitivity = numeric(),
      Specificity = numeric(),
      stringsAsFactors = FALSE
    )
    
    for (model_name in names(target_results)) {
      perf <- target_results[[model_name]]$performance
      comparison_df <- rbind(comparison_df, data.frame(
        Model = model_name,
        AUC = ifelse(is.na(perf$auc), 0, perf$auc),
        Accuracy = ifelse(is.na(perf$accuracy), 0, perf$accuracy),
        Sensitivity = ifelse(is.na(perf$sensitivity), 0, perf$sensitivity),
        Specificity = ifelse(is.na(perf$specificity), 0, perf$specificity)
      ))
    }
    
    # Sort by AUC
    comparison_df <- comparison_df[order(comparison_df$AUC, decreasing = TRUE), ]
    
    cat("\nModel Performance Comparison:\n")
    print(comparison_df)
    
    if (nrow(comparison_df) > 0) {
      best_model <- comparison_df$Model[1]
      best_auc <- comparison_df$AUC[1]
      
      cat(sprintf("\nBEST MODEL FOR %s: %s (AUC: %.4f)\n", 
                  toupper(target), best_model, best_auc))
      
      results[[target]] <- list(
        best_model = best_model,
        best_auc = best_auc,
        comparison = comparison_df,
        all_results = target_results
      )
    }
  }
  
  # Final Summary
  cat("\n" %+% rep("=", 60) %+% "\n")
  cat("ML DEMONSTRATION SUMMARY\n")
  cat(rep("=", 60) %+% "\n")
  
  summary_df <- data.frame(
    Target = character(),
    Best_Model = character(),
    Best_AUC = numeric(),
    Improvement = character(),
    stringsAsFactors = FALSE
  )
  
  for (target in names(results)) {
    result <- results[[target]]
    
    # Calculate improvement vs baseline
    baseline_auc <- 0.5  # Random classifier
    if ("baseline_rf" %in% result$comparison$Model) {
      baseline_auc <- result$comparison$AUC[result$comparison$Model == "baseline_rf"]
    }
    
    improvement <- sprintf("%.1f%%", (result$best_auc - baseline_auc) / baseline_auc * 100)
    
    summary_df <- rbind(summary_df, data.frame(
      Target = target,
      Best_Model = result$best_model,
      Best_AUC = result$best_auc,
      Improvement = improvement
    ))
  }
  
  cat("\nFINAL RESULTS SUMMARY:\n")
  print(summary_df)
  
  if (nrow(summary_df) > 0) {
    avg_auc <- mean(summary_df$Best_AUC)
    cat(sprintf("\nAverage AUC across all targets: %.4f\n", avg_auc))
    
    # Save results using safe file utilities
    results_dir <- get_ml_results_dir()
    safe_csv_save(summary_df, build_path(results_dir, "ml_demo_results.csv"))
    safe_file_save(results, build_path(results_dir, "ml_demo_detailed_results.rds"))
    
    cat("\nResults saved to:\n")
    cat(sprintf("  - %s\n", build_path(results_dir, "ml_demo_results.csv")))
    cat(sprintf("  - %s\n", build_path(results_dir, "ml_demo_detailed_results.rds")))
  }
  
  return(results)
}

# String concatenation helper
`%+%` <- function(a, b) paste0(a, b)

# Run the demonstration
cat("ML Demonstration Script Loaded!\n")
cat("\nTo run the demonstration, execute: ml_demo()\n")

# Auto-run if script is executed directly
if (!interactive()) {
  ml_demo()
}