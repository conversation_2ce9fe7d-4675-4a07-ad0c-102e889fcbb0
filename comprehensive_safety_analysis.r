# =============================================================================
# COMPREHENSIVE SAFETY ANALYSIS WITH REALISTIC BLEEDING SIMULATION
# =============================================================================
# This script generates realistic bleeding events and comprehensive safety analysis

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
  library(survival)
  library(survminer)
  library(gridExtra)
})

set.seed(12345)

cat("=== COMPREHENSIVE SAFETY ANALYSIS FOR IBRUTINIB ===\n")
cat("Generating realistic bleeding events and safety outcomes\n\n")

# Load file utilities and population
source("ml_file_utils.r")
results_dir <- get_ml_results_dir()
population_file <- build_path(results_dir, "synthetic_patient_population.csv")

population <- read.csv(population_file, stringsAsFactors = FALSE)
n_patients <- min(2000, nrow(population))
population <- population[1:n_patients, ]

cat("Loaded", n_patients, "patients for comprehensive safety analysis\n\n")

# Generate realistic bleeding events based on clinical data
cat("Generating realistic bleeding events based on clinical evidence...\n")

# Initialize comprehensive safety data
safety_data <- data.frame(
  patient_id = population$patient_id,
  treatment_arm = population$treatment_arm,
  age = population$age,
  anticoagulants = population$anticoagulants,
  high_comorbidity_score = population$high_comorbidity_score,
  platelet_count = population$platelet_count,
  treatment_duration_months = population$treatment_duration_months,
  
  # Bleeding outcomes
  any_bleeding = FALSE,
  major_bleeding = FALSE,
  minor_bleeding = FALSE,
  crnm_bleeding = FALSE,
  fatal_bleeding = FALSE,
  ich_bleeding = FALSE,
  gi_bleeding = FALSE,
  
  # CTCAE grades
  ctcae_max_grade = 0,
  
  # Time-to-event
  time_to_first_bleeding_days = NA,
  time_to_major_bleeding_days = NA,
  
  # Dose modifications
  dose_reduction = FALSE,
  dose_interruption = FALSE,
  permanent_discontinuation = FALSE,
  
  stringsAsFactors = FALSE
)

# Realistic bleeding rates based on clinical trials
# Ibrutinib: ~19% any bleeding, ~6% major bleeding
# Control: ~2.5% any bleeding, ~1% major bleeding

for (i in 1:n_patients) {
  
  # Calculate individual bleeding risk
  base_risk <- ifelse(population$treatment_arm[i] == "Ibrutinib_420mg", 0.19, 0.025)
  
  # Risk modifiers
  age_multiplier <- ifelse(population$age[i] >= 75, 2.5, 
                          ifelse(population$age[i] >= 70, 1.8, 1.0))
  anticoag_multiplier <- ifelse(population$anticoagulants[i], 2.5, 1.0)
  comorbidity_multiplier <- ifelse(population$high_comorbidity_score[i], 1.4, 1.0)
  platelet_multiplier <- ifelse(population$platelet_count[i] < 100, 1.8, 1.0)
  
  # Combined risk
  individual_risk <- base_risk * age_multiplier * anticoag_multiplier * 
                    comorbidity_multiplier * platelet_multiplier
  individual_risk <- min(individual_risk, 0.8)  # Cap at 80%
  
  # Simulate bleeding occurrence
  if (runif(1) < individual_risk) {
    safety_data$any_bleeding[i] <- TRUE
    
    # Time to bleeding (exponential distribution)
    max_time <- population$treatment_duration_months[i] * 30.44
    time_to_bleeding <- rexp(1, rate = 2/max_time)  # Average 2 events per treatment period
    safety_data$time_to_first_bleeding_days[i] <- min(time_to_bleeding, max_time)
    
    # Determine bleeding severity
    major_bleeding_prob <- ifelse(population$treatment_arm[i] == "Ibrutinib_420mg", 0.32, 0.40)
    major_bleeding_prob <- major_bleeding_prob * age_multiplier * anticoag_multiplier
    major_bleeding_prob <- min(major_bleeding_prob, 0.8)
    
    if (runif(1) < major_bleeding_prob) {
      # Major bleeding
      safety_data$major_bleeding[i] <- TRUE
      safety_data$time_to_major_bleeding_days[i] <- safety_data$time_to_first_bleeding_days[i]
      
      # CTCAE grade for major bleeding (3-5)
      grade_probs <- c(0.6, 0.3, 0.1)  # Grade 3, 4, 5
      safety_data$ctcae_max_grade[i] <- sample(3:5, 1, prob = grade_probs)
      
      # Fatal bleeding (Grade 5)
      if (safety_data$ctcae_max_grade[i] == 5) {
        safety_data$fatal_bleeding[i] <- TRUE
      }
      
      # Location of major bleeding
      location_probs <- c(0.08, 0.65, 0.27)  # ICH, GI, Other
      location <- sample(c("ICH", "GI", "Other"), 1, prob = location_probs)
      
      if (location == "ICH") {
        safety_data$ich_bleeding[i] <- TRUE
      } else if (location == "GI") {
        safety_data$gi_bleeding[i] <- TRUE
      }
      
    } else {
      # Non-major bleeding
      if (runif(1) < 0.6) {
        safety_data$crnm_bleeding[i] <- TRUE
        safety_data$ctcae_max_grade[i] <- 2
      } else {
        safety_data$minor_bleeding[i] <- TRUE
        safety_data$ctcae_max_grade[i] <- 1
      }
    }
  } else {
    # No bleeding
    safety_data$time_to_first_bleeding_days[i] <- population$treatment_duration_months[i] * 30.44
    safety_data$time_to_major_bleeding_days[i] <- population$treatment_duration_months[i] * 30.44
  }
  
  # Dose modifications for ibrutinib patients
  if (population$treatment_arm[i] == "Ibrutinib_420mg") {
    
    if (safety_data$major_bleeding[i]) {
      if (safety_data$fatal_bleeding[i] || safety_data$ich_bleeding[i]) {
        safety_data$permanent_discontinuation[i] <- TRUE
      } else {
        safety_data$dose_reduction[i] <- TRUE
      }
    }
    
    if (safety_data$crnm_bleeding[i] && runif(1) < 0.3) {
      safety_data$dose_interruption[i] <- TRUE
    }
  }
}

cat("✓ Realistic bleeding events generated\n\n")

# Save comprehensive safety data
safety_file <- build_path(results_dir, "comprehensive_safety_analysis.csv")
safe_csv_save(safety_data, safety_file)

# Generate comprehensive safety summary
cat("=== COMPREHENSIVE SAFETY RESULTS ===\n\n")

# Overall safety summary
ibr_patients <- safety_data[safety_data$treatment_arm == "Ibrutinib_420mg", ]
ctrl_patients <- safety_data[safety_data$treatment_arm == "Control", ]

cat("IBRUTINIB 420mg (n =", nrow(ibr_patients), "):\n")
cat("- Any bleeding:", sum(ibr_patients$any_bleeding), "(", round(mean(ibr_patients$any_bleeding)*100, 1), "%)\n")
cat("- Major bleeding:", sum(ibr_patients$major_bleeding), "(", round(mean(ibr_patients$major_bleeding)*100, 1), "%)\n")
cat("- CRNM bleeding:", sum(ibr_patients$crnm_bleeding), "(", round(mean(ibr_patients$crnm_bleeding)*100, 1), "%)\n")
cat("- Minor bleeding:", sum(ibr_patients$minor_bleeding), "(", round(mean(ibr_patients$minor_bleeding)*100, 1), "%)\n")
cat("- Fatal bleeding:", sum(ibr_patients$fatal_bleeding), "(", round(mean(ibr_patients$fatal_bleeding)*100, 1), "%)\n")
cat("- ICH:", sum(ibr_patients$ich_bleeding), "(", round(mean(ibr_patients$ich_bleeding)*100, 1), "%)\n")
cat("- GI bleeding:", sum(ibr_patients$gi_bleeding), "(", round(mean(ibr_patients$gi_bleeding)*100, 1), "%)\n")
cat("- Dose reduction:", sum(ibr_patients$dose_reduction), "(", round(mean(ibr_patients$dose_reduction)*100, 1), "%)\n")
cat("- Discontinuation:", sum(ibr_patients$permanent_discontinuation), "(", round(mean(ibr_patients$permanent_discontinuation)*100, 1), "%)\n")

cat("\nCONTROL (n =", nrow(ctrl_patients), "):\n")
cat("- Any bleeding:", sum(ctrl_patients$any_bleeding), "(", round(mean(ctrl_patients$any_bleeding)*100, 1), "%)\n")
cat("- Major bleeding:", sum(ctrl_patients$major_bleeding), "(", round(mean(ctrl_patients$major_bleeding)*100, 1), "%)\n")

# Risk ratio calculation
ibr_any_rate <- mean(ibr_patients$any_bleeding)
ctrl_any_rate <- mean(ctrl_patients$any_bleeding)
risk_ratio <- ifelse(ctrl_any_rate > 0, ibr_any_rate / ctrl_any_rate, NA)

cat("\nRISK RATIOS:\n")
cat("- Any bleeding RR:", round(risk_ratio, 1), "\n")

ibr_major_rate <- mean(ibr_patients$major_bleeding)
ctrl_major_rate <- mean(ctrl_patients$major_bleeding)
major_risk_ratio <- ifelse(ctrl_major_rate > 0, ibr_major_rate / ctrl_major_rate, NA)
cat("- Major bleeding RR:", round(major_risk_ratio, 1), "\n")

cat("\n=== COMPREHENSIVE SAFETY ANALYSIS COMPLETED ===\n")
cat("✓ Realistic bleeding events simulated based on clinical evidence\n")
cat("✓ CTCAE v5.0 grading applied\n")
cat("✓ Dose modification patterns analyzed\n")
cat("✓ Risk ratios calculated\n")
cat("✓ Results saved to:", safety_file, "\n")
