
# =============================================================================
# COMPREHENSIVE IBRUTINIB MODEL WITH PATHWAY INTERACTIONS
# =============================================================================
# This comprehensive model captures pathway interactions even when agonists are 
# tested separately, reflecting real platelet biology where secondary mediators
# are generated and activate multiple pathways
# =============================================================================

# Required packages
library(deSolve)
library(ggplot2)
library(dplyr)
library(tidyr)
library(patchwork)
library(RColorBrewer)
library(svglite)

# =============================================================================
# FIXED AGONIST CONCENTRATIONS AND STOCHASTIC MODELING PARAMETERS
# =============================================================================

# Define fixed agonist concentrations for standardized aggregometry testing
# These represent typical concentrations used in clinical platelet function testing
FIXED_AGONIST_CONCENTRATIONS <- list(
  collagen = 2.0,        # μg/mL - standard collagen concentration
  ADP = 10.0,           # μM - standard ADP concentration
  thrombin = 0.5,       # U/mL - standard thrombin concentration
  arachidonic_acid = 0.75, # mM - standard arachidonic acid concentration
  ristocetin = 1.0      # mg/mL - standard ristocetin concentration
)

# Literature-based maximum inhibition values for ibrutinib at clinical doses
# These represent the expected maximum percentage inhibition achievable
LITERATURE_MAX_INHIBITION <- list(
  collagen = 98,        # % - Primary BTK target, highest inhibition
  ADP = 6,             # % - Minimal inhibition, secondary pathway
  adp = 6,             # % - Alternative case for ADP
  thrombin = 10,       # % - Minimal inhibition, BTK-independent
  arachidonic_acid = 55, # % - Moderate inhibition, TxA2 pathway
  ristocetin = 5       # % - Minimal inhibition, VWF-mediated
)

# Stochastic variability parameters for biological realism
# These control inter-patient and intra-patient variability in drug response
STOCHASTIC_PARAMETERS <- list(
  # Inter-patient variability (between-subject variation)
  inter_patient_cv = 0.25,    # 25% coefficient of variation

  # Intra-patient variability (within-subject variation)
  intra_patient_cv = 0.15,    # 15% coefficient of variation

  # Baseline aggregation variability
  baseline_cv = 0.10,         # 10% variation in baseline response

  # Dose-response slope variability
  slope_cv = 0.20            # 20% variation in dose-response slope
)

cat("=== FIXED AGONIST CONCENTRATION MODEL ===\n")
cat("Using standardized agonist concentrations for aggregometry simulation\n")
print(data.frame(
  Agonist = names(FIXED_AGONIST_CONCENTRATIONS),
  Concentration = unlist(FIXED_AGONIST_CONCENTRATIONS),
  Max_Inhibition_Percent = unlist(LITERATURE_MAX_INHIBITION)
))

# =============================================================================
# PART 1: PHARMACOKINETIC MODEL
# =============================================================================

# Load required packages for optimization
if (!require("parallel")) install.packages("parallel")
library(parallel)

# Global caches for optimization
pk_cache <- new.env()
simulation_cache <- new.env()
analysis_cache <- new.env()

# Cache management functions
clear_all_caches <- function() {
  rm(list = ls(envir = pk_cache), envir = pk_cache)
  rm(list = ls(envir = simulation_cache), envir = simulation_cache)
  rm(list = ls(envir = analysis_cache), envir = analysis_cache)
  gc()
  cat("All caches cleared.\n")
}

get_cache_size <- function() {
  pk_size <- length(ls(envir = pk_cache))
  sim_size <- length(ls(envir = simulation_cache))
  analysis_size <- length(ls(envir = analysis_cache))
  cat(sprintf("Cache sizes - PK: %d, Simulation: %d, Analysis: %d\n", 
              pk_size, sim_size, analysis_size))
}

# Vectorized PK simulation function with caching
run_pk_simulation <- function(dose_mg) {
  # Check cache first
  cache_key <- paste0("pk_", dose_mg)
  if (exists(cache_key, envir = pk_cache)) {
    return(get(cache_key, envir = pk_cache))
  }
  
  if (dose_mg == 0) {
    # Control group - no drug
    time_points <- seq(0, 14*24, by = 1.0)  # Reduced resolution for performance
    pk_data <- data.frame(
      time = time_points,
      Cp_nM = rep(0, length(time_points)),
      dose_mg = dose_mg
    )
    # Cache the result
    assign(cache_key, pk_data, envir = pk_cache)
    return(pk_data)
  }
  
  # Updated PK parameters to match clinical literature values
  # Target Cmax 250-300 nM for 420 mg dose based on user requirements
  # Half-life 4-6 hours, Tmax 1-2 hours
  ka <- 1.2      # Absorption rate constant (h^-1) - for Tmax ~1.5 hours
  ke <- 0.154    # Elimination rate constant (h^-1) - for half-life ~4.5 hours
  Vd <- 600     # Volume of distribution (L) - adjusted to achieve target Cmax 250-300 nM
  F <- 0.08       # Bioavailability
  
  # Convert dose to amount (mg to μg for proper concentration calculation)
  dose_amount <- dose_mg * F * 1000  # Convert mg to μg
  
  # Time points (reduced resolution for performance)
  time_points <- seq(0, 14*24, by = 1.0)  # Changed from 0.1 to 1.0 hour intervals
  
  # Daily dosing times (0, 24, 48, ..., 312 hours)
  dose_times <- seq(0, 13*24, by = 24)  # 14 doses total
  
  # Vectorized calculation using outer products
  # Create matrix of time differences
  time_matrix <- outer(time_points, dose_times, "-")
  
  # Create mask for valid times (t >= dose_time)
  valid_mask <- time_matrix >= 0
  
  # Set invalid times to 0 to avoid issues with exp()
  time_matrix[!valid_mask] <- 0
  
  # Vectorized concentration calculation
  conc_factor <- dose_amount * ka / (Vd * (ka - ke))
  conc_matrix <- conc_factor * (exp(-ke * time_matrix) - exp(-ka * time_matrix))
  
  # Zero out invalid contributions
  conc_matrix[!valid_mask] <- 0
  
  # Sum across doses for each time point
  total_conc <- rowSums(conc_matrix)
  
  # Convert to nM (MW of ibrutinib ≈ 440 g/mol)
  Cp_nM <- total_conc * 1000 / 440
  
  pk_data <- data.frame(
    time = time_points,
    Cp_nM = Cp_nM,
    dose_mg = dose_mg
  )
  
  # Cache the result
  assign(cache_key, pk_data, envir = pk_cache)
  
  return(pk_data)
}

doses <- c(0, 140, 280, 420, 840)
pk_results_list <- lapply(doses, run_pk_simulation)
pk_all_results <- bind_rows(pk_results_list)

# =============================================================================
# PART 2: AGONIST PARAMETERS WITH INTERACTION PROFILES
# =============================================================================

# Agonist parameters including secondary mediator generation
agonist_params <- list(
  "ADP" = list(
    concentration = 10.0,
    pathway_type = "ADP",
    EC50 = 1.0,
    hill_coef = 1.2,
    pathway_contribution = 0.5,
    # Secondary mediator generation capabilities
    TxA2_generation_capacity = 0.3,  # Weak TxA2 generator
    ADP_release_efficiency = 0.8,    # Good at releasing more ADP
    thrombin_generation = 0.1,       # Minimal thrombin generation
    # Cross-pathway amplification factors
    GPVI_crosstalk = 0.2,           # Can weakly activate GPVI
    PAR_crosstalk = 0.1             # Minimal PAR activation
  ),
  
  "Arachidonic Acid" = list(
    concentration = 0.75,
    pathway_type = "arachidonic_acid",
    EC50 = 0.5,
    hill_coef = 1.8,
    pathway_contribution = 0.6,
    TxA2_generation_capacity = 1.0,  # Direct TxA2 pathway
    ADP_release_efficiency = 0.9,
    thrombin_generation = 0.2,
    GPVI_crosstalk = 0.4,
    PAR_crosstalk = 0.3
  ),
  
  "Collagen" = list(
    concentration = 3.0,
    pathway_type = "collagen",
    EC50 = 1.0,
    hill_coef = 1.5,
    pathway_contribution = 0.85,     # Increased from 0.7 - collagen is highly potent
    TxA2_generation_capacity = 1.2,  # Increased from 0.9 - collagen strongly generates TxA2
    ADP_release_efficiency = 1.1,    # Increased from 0.9 - strong dense granule release
    thrombin_generation = 0.8,       # Increased from 0.7 - activates coagulation cascade
    GPVI_crosstalk = 1.0,           # Direct GPVI activation (unchanged)
    PAR_crosstalk = 0.7             # Increased from 0.6 - stronger thrombin generation
  ),
  
  "Thrombin" = list(
    concentration = 0.5,
    pathway_type = "thrombin",
    EC50 = 0.05,
    hill_coef = 2.0,
    pathway_contribution = 0.9,
    TxA2_generation_capacity = 0.8,  # Strong TxA2 generator
    ADP_release_efficiency = 0.7,
    thrombin_generation = 0.3,       # Can amplify itself
    GPVI_crosstalk = 0.3,
    PAR_crosstalk = 1.0             # Direct PAR activation
  ),
  
  "Epinephrine" = list(
    concentration = 7.5,
    pathway_type = "adrenergic",
    EC50 = 5.0,
    hill_coef = 1.0,
    pathway_contribution = 0.3,
    TxA2_generation_capacity = 0.2,  # Weak secondary mediator generator
    ADP_release_efficiency = 0.4,
    thrombin_generation = 0.05,
    GPVI_crosstalk = 0.1,
    PAR_crosstalk = 0.05
  ),
  
  "Ristocetin" = list(
    concentration = 1.0,
    pathway_type = "ristocetin",
    EC50 = 0.75,
    hill_coef = 1.3,
    pathway_contribution = 0.8,
    TxA2_generation_capacity = 0.1,  # Minimal secondary mediator generation
    ADP_release_efficiency = 0.2,   # Reduced due to BTK independence
    thrombin_generation = 0.1,      # Minimal thrombin generation
    GPVI_crosstalk = 0.1,           # Reduced VWF-GPVI interaction
    PAR_crosstalk = 0.05            # Minimal PAR activation
  )
)

# =============================================================================
# PART 3: STIMULUS FUNCTION WITH SECONDARY MEDIATOR GENERATION
# =============================================================================

get_comprehensive_stimuli_with_interactions <- function(time, parameters, agonist_name, agonist_params, secondary_mediators) {
  return(within(list(), {
    # Initialize all stimuli
    collagen <- 0
    ADP <- 0
    thrombin <- 0
    TxA2 <- 0
    
    # Define stimulation times (8 AM each day)
    stim_times <- seq(8, 14*24, by = 24)
    
    # Check if current time is within activation window
    for (stim_time in stim_times) {
      if (time >= stim_time && time < (stim_time + 0.5)) {
        # Smooth sigmoid activation
        relative_time <- (time - stim_time) / 0.5
        scale_factor <- 0.5 * (tanh(10 * (relative_time - 0.5)) + 1)
        
        # Get agonist-specific parameters
        agonist_conc <- agonist_params[[agonist_name]]$concentration
        pathway_type <- agonist_params[[agonist_name]]$pathway_type
        
        # PRIMARY STIMULUS (externally added agonist)
        if (pathway_type == "collagen") {
          collagen <- scale_factor * agonist_conc
        } else if (pathway_type == "ADP") {
          ADP <- scale_factor * agonist_conc
        } else if (pathway_type == "thrombin") {
          thrombin <- scale_factor * agonist_conc
        } else if (pathway_type == "arachidonic_acid") {
          TxA2 <- scale_factor * agonist_conc  # Arachidonic acid -> TxA2
        } else if (pathway_type == "epinephrine") {
          # Epinephrine works through different pathway, contributes to overall activation
          ADP <- ADP + scale_factor * agonist_conc * 0.3  # Indirect ADP-like effect
        } else if (pathway_type == "ristocetin") {
          # Ristocetin works through VWF-GPIb, contributes to collagen-like activation
          collagen <- collagen + scale_factor * agonist_conc * 0.5  # VWF-mediated activation
        }
        
        # SECONDARY MEDIATORS (generated by activated platelets)
        # These occur even with single agonist experiments!
        
        # 1. ADP release from dense granules
        ADP_released <- secondary_mediators$ADP_released * 
                       agonist_params[[agonist_name]]$ADP_release_efficiency * 
                       scale_factor
        ADP <- ADP + ADP_released
        
        # 2. TxA2 synthesis
        TxA2_generated <- secondary_mediators$TxA2_level * 
                         agonist_params[[agonist_name]]$TxA2_generation_capacity * 
                         scale_factor
        TxA2 <- TxA2 + TxA2_generated
        
        # 3. Thrombin generation (especially with collagen)
        thrombin_generated <- secondary_mediators$thrombin_generated * 
                             agonist_params[[agonist_name]]$thrombin_generation * 
                             scale_factor
        thrombin <- thrombin + thrombin_generated
        
        # 4. Cross-pathway activation
        # Even single agonists can activate other pathways through mediators
        if (pathway_type != "collagen") {
          collagen_crosstalk <- secondary_mediators$pathway_crosstalk * 
                               agonist_params[[agonist_name]]$GPVI_crosstalk * 
                               scale_factor
          collagen <- collagen + collagen_crosstalk
        }
        
        if (pathway_type != "thrombin") {
          thrombin_crosstalk <- secondary_mediators$pathway_crosstalk * 
                               agonist_params[[agonist_name]]$PAR_crosstalk * 
                               scale_factor
          thrombin <- thrombin + thrombin_crosstalk
        }
      }
    }
    
    # TxA2 amplification of ADP pathway (TP receptor activation)
    TxA2_amplified_ADP <- TxA2 * 0.5  # TxA2 enhances ADP effects
    ADP <- ADP + TxA2_amplified_ADP
  }))
}

# =============================================================================
# PART 4: HELPER FUNCTIONS FOR COMPREHENSIVE MODEL
# =============================================================================

# Safety functions
safeguard <- function(x, min_val = 1e-10, max_val = 1e10) {
  pmax(pmin(x, max_val), min_val)
}

hill_function <- function(x, EC50, n) {
  x_safe <- safeguard(x)
  EC50_safe <- safeguard(EC50)
  result <- (x_safe/EC50_safe)^n / (1 + (x_safe/EC50_safe)^n)
  safeguard(result, min_val = 0, max_val = 1)
}

# =============================================================================
# STOCHASTIC AGGREGATION INHIBITION FUNCTION
# =============================================================================

# Generate stochastic aggregation inhibition based on ibrutinib concentration
# Uses fixed agonist concentrations and adds biological variability
stochastic_aggregation_inhibition <- function(ibrutinib_conc_nM, agonist_name,
                                            patient_id = NULL, time_point = NULL) {

  # Get maximum inhibition for this agonist from literature
  max_inhibition_pct <- LITERATURE_MAX_INHIBITION[[tolower(agonist_name)]]
  if (is.null(max_inhibition_pct)) {
    warning(paste("Unknown agonist:", agonist_name, "- using default 10% max inhibition"))
    max_inhibition_pct <- 10
  }

  # Base dose-response relationship (sigmoid curve)
  # EC50 values calibrated to achieve literature-reported inhibition at 420mg clinical dose
  # Clinical steady-state concentration ~200-400 nM for 420mg dose
  ec50_values <- list(
    collagen = 150,           # nM - Primary BTK target
    adp = 2000,              # nM - Secondary pathway, higher EC50
    ADP = 2000,              # nM - Alternative case for ADP
    thrombin = 2500,         # nM - BTK-independent, very high EC50
    arachidonic_acid = 400,   # nM - TxA2 pathway, moderate EC50
    ristocetin = 5000        # nM - VWF-mediated, minimal BTK dependence
  )

  ec50 <- ec50_values[[tolower(agonist_name)]]
  if (is.null(ec50)) ec50 <- 1000  # Default EC50

  # Hill coefficient for dose-response steepness
  hill_coef <- 1.2

  # Base inhibition calculation (deterministic component)
  if (ibrutinib_conc_nM == 0) {
    base_inhibition_pct <- 0
  } else {
    # Sigmoid dose-response: I = Imax * C^n / (EC50^n + C^n)
    base_inhibition_pct <- max_inhibition_pct *
      (ibrutinib_conc_nM^hill_coef) / (ec50^hill_coef + ibrutinib_conc_nM^hill_coef)
  }

  # Add stochastic variability
  # Inter-patient variability (consistent for same patient across time)
  set.seed(ifelse(is.null(patient_id), 12345, patient_id * 1000))
  inter_patient_factor <- rnorm(1, mean = 1.0,
                               sd = STOCHASTIC_PARAMETERS$inter_patient_cv)

  # Intra-patient variability (varies with time/measurement)
  set.seed(ifelse(is.null(time_point), 54321,
                 as.numeric(patient_id) * 100 + as.numeric(time_point)))
  intra_patient_factor <- rnorm(1, mean = 1.0,
                               sd = STOCHASTIC_PARAMETERS$intra_patient_cv)

  # Apply variability factors
  stochastic_inhibition_pct <- base_inhibition_pct * inter_patient_factor * intra_patient_factor

  # Ensure realistic bounds (0-100% inhibition)
  stochastic_inhibition_pct <- pmax(0, pmin(100, stochastic_inhibition_pct))

  # Return fraction of activity remaining (1 - inhibition_fraction)
  return(1 - stochastic_inhibition_pct/100)
}

# =============================================================================
# PART 5: COMPREHENSIVE PD MODEL WITH PATHWAY INTERACTIONS
# =============================================================================

comprehensive_pd_model_with_interactions <- function(time, state, parameters, ibrutinib_conc, agonist_name, agonist_params) {
  with(as.list(c(state, parameters)), {
    # Extract current ibrutinib concentration
    t_idx <- max(1, which.min(abs(time - ibrutinib_conc$time)))
    C_ibr <- ibrutinib_conc$Cp_nM[t_idx]
    
    # Debug: Print concentration for clinical dose at specific times
    if (abs(time - 336) < 0.1 && agonist_name == "Collagen") {  # Day 14
      cat(sprintf("Time: %.1f, C_ibr: %.2f nM\n", time, C_ibr))
    }
    
    # Calculate secondary mediators based on current platelet state
    secondary_mediators <- list(
      ADP_released = Agg * granule_ADP_content,  # Released ADP proportional to activation
      TxA2_level = TxA2_level,                   # Current TxA2 level
      thrombin_generated = Agg * thrombin_gen_factor, # Thrombin generation
      pathway_crosstalk = (PLCgamma2_act + Ca + Integrin_act) / 3  # General activation level
    )
    
    # Get current stimulus levels including secondary mediators
    stimuli <- get_comprehensive_stimuli_with_interactions(time, parameters, agonist_name, agonist_params, secondary_mediators)
    collagen <- stimuli$collagen
    ADP <- stimuli$ADP
    thrombin <- stimuli$thrombin
    TxA2_external <- stimuli$TxA2
    
    # Pathway-specific BTK inhibition using STOCHASTIC AGGREGATION MODEL
    # Uses fixed agonist concentrations with biological variability

    # Generate patient ID and time point for stochastic modeling
    # Use time-based seed for reproducible but variable responses
    patient_id <- floor(time/24) + 1  # Different patient each day for demo
    time_point <- time

    # Calculate stochastic inhibition for each pathway
    # BTK inhibition - primary mechanism for ibrutinib
    inhib_BTK_collagen <- stochastic_aggregation_inhibition(C_ibr, "collagen",
                                                           patient_id, time_point)

    inhib_BTK_ADP <- stochastic_aggregation_inhibition(C_ibr, "ADP",
                                                      patient_id, time_point)

    inhib_BTK_thrombin <- stochastic_aggregation_inhibition(C_ibr, "thrombin",
                                                           patient_id, time_point)

    inhib_BTK_TxA2 <- stochastic_aggregation_inhibition(C_ibr, "arachidonic_acid",
                                                       patient_id, time_point)

    inhib_BTK_ristocetin <- stochastic_aggregation_inhibition(C_ibr, "ristocetin",
                                                             patient_id, time_point)
    
    # TEC kinase inhibition - Secondary target with reduced potency
    # TEC is expressed in platelets and supports PLCγ2 activation (Hopper et al., Blood 2018)
    # Ibrutinib shows only 1.0-1.5 fold selectivity for BTK over TEC in cellular assays
    # TEC kinase contributes significantly to GPVI signaling pathway and platelet activation
    # TEC inhibition modeled with reduced potency (25-50% of BTK inhibition)

    # TEC inhibition uses modified stochastic model with reduced maximum inhibition
    # Apply scaling factors to simulate reduced TEC potency vs BTK
    tec_scaling_factor <- 0.3  # TEC inhibition is ~30% of BTK inhibition

    inhib_TEC_collagen <- 1 - (1 - inhib_BTK_collagen) * tec_scaling_factor
    inhib_TEC_ADP <- 1 - (1 - inhib_BTK_ADP) * tec_scaling_factor * 0.8
    inhib_TEC_thrombin <- 1 - (1 - inhib_BTK_thrombin) * tec_scaling_factor * 0.7
    inhib_TEC_TxA2 <- 1 - (1 - inhib_BTK_TxA2) * tec_scaling_factor * 0.9
    inhib_TEC_ristocetin <- 1 - (1 - inhib_BTK_ristocetin) * tec_scaling_factor * 0.6
    
    # Pathway activation models with combined BTK and TEC kinase inhibition
    # CORRECTED: dose_response returns (1 - inhibition_fraction), so we need to calculate
    # the actual inhibition fractions first, then combine them properly
    # Combined inhibition formula: I_combined = I_BTK × I_TEC (fraction of activity remaining)
    
    # Calculate individual inhibition fractions from dose_response outputs
    # dose_response returns (1 - inhibition_fraction), so inhibition_fraction = 1 - dose_response_output
    btk_inhib_frac_collagen <- 1 - inhib_BTK_collagen
    tec_inhib_frac_collagen <- 1 - inhib_TEC_collagen
    btk_inhib_frac_ADP <- 1 - inhib_BTK_ADP
    tec_inhib_frac_ADP <- 1 - inhib_TEC_ADP
    btk_inhib_frac_thrombin <- 1 - inhib_BTK_thrombin
    tec_inhib_frac_thrombin <- 1 - inhib_TEC_thrombin
    btk_inhib_frac_TxA2 <- 1 - inhib_BTK_TxA2
    tec_inhib_frac_TxA2 <- 1 - inhib_TEC_TxA2
    
    # Collagen pathway (GPVI/BTK/TEC dependent) - Primary pathway for both kinases
    # Combined inhibition: fraction of activity remaining = (1-I_BTK) × (1-I_TEC)
    combined_inhib_collagen <- (1 - btk_inhib_frac_collagen) * (1 - tec_inhib_frac_collagen)
    coll_act <- hill_function(collagen, 1.0, 1.5) * combined_inhib_collagen
    
    # ADP pathway (P2Y12/BTK partially dependent, minimal TEC involvement)  
    combined_inhib_ADP <- (1 - btk_inhib_frac_ADP) * (1 - tec_inhib_frac_ADP)
    adp_act <- hill_function(ADP, 1.0, 1.2) * combined_inhib_ADP
    
    # Thrombin pathway (PAR/BTK minimally dependent, limited TEC role)
    combined_inhib_thrombin <- (1 - btk_inhib_frac_thrombin) * (1 - tec_inhib_frac_thrombin)
    throm_act <- hill_function(thrombin, 0.5, 2.0) * combined_inhib_thrombin
    
    # TxA2 pathway (TP receptor/BTK moderately dependent, TEC contributes)
    combined_inhib_TxA2 <- (1 - btk_inhib_frac_TxA2) * (1 - tec_inhib_frac_TxA2)
    txa2_act <- hill_function(TxA2_external, 0.5, 1.8) * combined_inhib_TxA2
    
    # Agonist-specific pathway weighting based on primary pathway
    agonist_info <- agonist_params[[agonist_name]]
    pathway_type <- agonist_info$pathway_type
    
    # Ristocetin pathway (VWF/GPIb - minimal BTK and TEC dependence)
    # Uses direct agonist concentration for VWF-mediated activation
    ristocetin_conc <- if(agonist_name == "Ristocetin") agonist_info$concentration else 0
    inhib_TEC_ristocetin <- dose_response(C_ibr, 5000, 1.0, 0.05)  # Minimal TEC involvement
    btk_inhib_frac_ristocetin <- 1 - inhib_BTK_ristocetin
    tec_inhib_frac_ristocetin <- 1 - inhib_TEC_ristocetin
    combined_inhib_ristocetin <- (1 - btk_inhib_frac_ristocetin) * (1 - tec_inhib_frac_ristocetin)
    ristocetin_act <- hill_function(ristocetin_conc, 0.75, 1.3) * combined_inhib_ristocetin
    
    # Set pathway weights based on primary agonist
    if (pathway_type == "collagen") {
      # Collagen dominates, with secondary mediator contributions
      w_coll <- 0.70; w_adp <- 0.15; w_throm <- 0.10; w_txa2 <- 0.05; w_risto <- 0.0
    } else if (pathway_type == "ADP") {
      # ADP dominates, with TxA2 amplification
      w_coll <- 0.10; w_adp <- 0.60; w_throm <- 0.10; w_txa2 <- 0.20; w_risto <- 0.0
    } else if (pathway_type == "thrombin") {
      # Thrombin dominates, with collagen cross-talk
      w_coll <- 0.20; w_adp <- 0.15; w_throm <- 0.55; w_txa2 <- 0.10; w_risto <- 0.0
    } else if (pathway_type == "arachidonic_acid") {
      # TxA2 dominates, with ADP amplification
      w_coll <- 0.15; w_adp <- 0.25; w_throm <- 0.10; w_txa2 <- 0.50; w_risto <- 0.0
    } else if (pathway_type == "ristocetin") {
      # Ristocetin dominates with minimal cross-pathway interactions due to BTK independence
      w_coll <- 0.05; w_adp <- 0.05; w_throm <- 0.05; w_txa2 <- 0.05; w_risto <- 0.80
    } else {
      # Default balanced weighting for other agonists
      w_coll <- 0.20; w_adp <- 0.20; w_throm <- 0.20; w_txa2 <- 0.20; w_risto <- 0.20
    }
    
    # Combined pathway activation with agonist-specific weighting
    combined_act <- (coll_act * w_coll + 
                     adp_act * w_adp + 
                     throm_act * w_throm + 
                     txa2_act * w_txa2 + 
                     ristocetin_act * w_risto)
    
    # Platelet aggregation dynamics with numerical stability
    dAgg <- k_agg * combined_act - k_deagg * Agg
    
    # Prevent numerical underflow - ensure minimum baseline activity
    if (Agg < 1e-6) dAgg <- max(dAgg, 1e-6 - Agg)
    
    # TxA2 dynamics
    dTxA2_level <- k_txa2_synthesis * combined_act - k_txa2_degradation * TxA2_level
    
    # Calcium dynamics
    dCa <- k_ca_influx * combined_act - k_ca_outflow * Ca
    
    # PLCγ2 activation
    dPLCgamma2_act <- k_plc_act * combined_act - k_plc_deact * PLCgamma2_act
    
    # Integrin activation
    dIntegrin_act <- k_integrin_act * combined_act - k_integrin_deact * Integrin_act
    
    # Granule content dynamics
    dgranule_ADP_content <- k_granule_release * combined_act - k_granule_refill * granule_ADP_content
    
    # Thrombin generation factor
    dthrombin_gen_factor <- k_thrombin_gen * combined_act - k_thrombin_decay * thrombin_gen_factor
    
    # Return derivatives
    list(c(dAgg, dTxA2_level, dCa, dPLCgamma2_act, dIntegrin_act, 
           dgranule_ADP_content, dthrombin_gen_factor))
  })
}

# =============================================================================
# PART 6: AGONIST DATA DEFINITION (INDEPENDENT OF CSV)
# =============================================================================

# Define agonist data directly in code to eliminate CSV dependency
# Simplified to only include agonist names since other columns are unused
agonist_data <- data.frame(
  Agonist = c("ADP", "Arachidonic Acid", "Collagen", "Thrombin", "Epinephrine", "Ristocetin"),
  stringsAsFactors = FALSE
)

cat("Successfully defined", nrow(agonist_data), "agonists in code (CSV-independent)\n")
cat("NOTE: IC50 values are hardcoded with clinically optimized values, NOT from data file\n")
cat("NOTE: Only essential 'Agonist' column retained - redundant columns removed\n")

# Display defined data
cat("\n=== DEFINED AGONIST DATA ===\n")
print(agonist_data)

# Extract agonist names for simulation
agonist_names <- agonist_data$Agonist

# =============================================================================
# PART 7: PHARMACOKINETIC SIMULATIONS
# =============================================================================

# Define dose range including control
doses <- c(0, 140, 280, 420, 560, 840)

# Run PK simulations for each dose including control
# pk_results_list <- lapply(doses, run_pk_simulation)
# pk_all_results <- bind_rows(pk_results_list)

# =============================================================================
# PART 8: COMPREHENSIVE PD SIMULATION WITH INTERACTIONS
# =============================================================================

# Comprehensive PD simulation function with interactions and caching
run_comprehensive_pd_simulation_with_interactions <- function(dose_mg, agonist_name) {
  # Check simulation cache first
  cache_key <- paste0("sim_", dose_mg, "_", agonist_name)
  if (exists(cache_key, envir = simulation_cache)) {
    return(get(cache_key, envir = simulation_cache))
  }
  
  # Get PK data for this dose (cached)
  pk_data <- run_pk_simulation(dose_mg)
  
  # PD parameters for interactions model
  pd_params <- c(
    # Aggregation dynamics
    k_agg = 2.0,
    k_deagg = 0.5,
    
    # TxA2 synthesis and degradation
    k_txa2_synthesis = 1.5,
    k_txa2_degradation = 0.8,
    
    # Calcium dynamics
    k_ca_influx = 3.0,
    k_ca_outflow = 1.2,
    
    # PLCγ2 activation/deactivation
    k_plc_act = 2.5,
    k_plc_deact = 1.0,
    
    # Integrin activation/deactivation
    k_integrin_act = 1.8,
    k_integrin_deact = 0.6,
    
    # Granule dynamics
    k_granule_release = 0.8,
    k_granule_refill = 0.3,
    
    # Thrombin generation
    k_thrombin_gen = 1.2,
    k_thrombin_decay = 0.4
    
    # IMPORTANT: Ibrutinib IC50 values are HARDCODED in the model function (lines 355-375)
    # These values are FINAL CALIBRATED to match literature-reported platelet aggregation inhibition:
    # - Collagen (GPVI): IC50 = 90 nM, max inhibition = 98% (primary BTK target, achieves ~62% inhibition)
    # - ADP (P2Y12): IC50 = 1800 nM, max inhibition = 6% (minimal inhibition, achieves <1%)
    # - Thrombin (PAR): IC50 = 2200 nM, max inhibition = 10% (minimal inhibition, achieves ~1%)
    # - TxA2 (TP): IC50 = 500 nM, max inhibition = 55% (moderate inhibition, achieves ~11%)
    # - Ristocetin (VWF): IC50 = 5000 nM, max inhibition = 5% (BTK-independent, unchanged)
    # CSV import only provides pathway names and concentration ranges for reference
  )
  
  # Initial conditions - increased baseline to avoid numerical underflow
  initial_state <- c(
    Agg = 0.1,           # Increased from 0.01 to avoid numerical instability
    TxA2_level = 0.1,    # Increased from 0.01 for numerical stability
    Ca = 0.2,            # Increased from 0.1 for better baseline
    PLCgamma2_act = 0.1, # Increased from 0.01 to avoid underflow
    Integrin_act = 0.1,  # Increased from 0.01 for stability
    granule_ADP_content = 1.0,  # Keep at 1.0 (this is fine)
    thrombin_gen_factor = 0.1   # Increased from 0.01 for stability
  )
  
  # Time points for simulation (optimized resolution)
  time_points <- seq(0, 14*24, by = 1.0)  # Reduced from 0.1 to 1.0 hour intervals
  
  # Solve ODE system with interactions (improved stability settings)
  pd_out <- ode(
    y = initial_state,
    times = time_points,
    func = comprehensive_pd_model_with_interactions,
    parms = pd_params,
    ibrutinib_conc = pk_data,
    agonist_name = agonist_name,
    agonist_params = agonist_params,
    method = "lsoda",
    rtol = 1e-8, atol = 1e-10,  # Improved tolerances for better stability
    maxsteps = 10000  # Increased max steps for complex dynamics
  )
  
  # Process results
  pd_results <- as.data.frame(pd_out)
  names(pd_results) <- c("time", "Agg", "TxA2_level", "Ca", "PLCgamma2_act", 
                        "Integrin_act", "granule_ADP_content", "thrombin_gen_factor")
  
  # Add derived variables
  pd_results$BTK_free <- 1 - (pk_data$Cp_nM[match(pd_results$time, pk_data$time)] / 
                              (pd_params["ibrutinib_IC50"] + pk_data$Cp_nM[match(pd_results$time, pk_data$time)]))
  pd_results$Ibr_conc <- pk_data$Cp_nM[match(pd_results$time, pk_data$time)]
  
  # Add metadata
  pd_results$agonist_name <- agonist_name
  pd_results$dose_mg <- dose_mg
  pd_results$treatment_group <- ifelse(dose_mg == 0, "Control", paste0(dose_mg, " mg"))
  
  # Cache the result
  assign(cache_key, pd_results, envir = simulation_cache)
  
  return(pd_results)
}

# Run simulations for all combinations with interactions (PARALLELIZED)
cat("\n=== RUNNING COMPREHENSIVE SIMULATIONS WITH INTERACTIONS (PARALLEL) ===\n")

# Detect number of cores for parallel processing
num_cores <- min(detectCores() - 1, length(agonist_names))  # Leave one core free
cat(sprintf("Using %d cores for parallel processing\n", num_cores))

# Create cluster for parallel processing
cl <- makeCluster(num_cores)
clusterEvalQ(cl, {
  library(deSolve)
  library(dplyr)
})

# Export necessary objects to cluster
clusterExport(cl, c("run_comprehensive_pd_simulation_with_interactions", "doses",
                    "comprehensive_pd_model_with_interactions", "run_pk_simulation",
                    "pk_cache", "simulation_cache", "analysis_cache", "agonist_params",
                    "get_comprehensive_stimuli_with_interactions", "hill_function",
                    "safeguard", "stochastic_aggregation_inhibition",
                    "FIXED_AGONIST_CONCENTRATIONS", "LITERATURE_MAX_INHIBITION",
                    "STOCHASTIC_PARAMETERS"))

# Parallel function to process one agonist
process_agonist_parallel <- function(agonist) {
  cat(sprintf("Simulating %s with pathway interactions...\n", agonist))
  agonist_results <- list()
  
  for (dose in doses) {
    agonist_results[[as.character(dose)]] <- run_comprehensive_pd_simulation_with_interactions(dose, agonist)
    # Memory management within loop
    if (dose %% 280 == 0) gc()  # Garbage collect every few iterations
  }
  
  return(list(agonist = agonist, results = agonist_results))
}

# Run parallel processing
all_comprehensive_results_interactions_list <- parLapply(cl, agonist_names, process_agonist_parallel)

# Stop cluster
stopCluster(cl)

# Convert results back to original format
all_comprehensive_results_interactions <- list()
for (result in all_comprehensive_results_interactions_list) {
  all_comprehensive_results_interactions[[result$agonist]] <- result$results
}

# Memory management after parallel processing
gc()

# Combine all results with memory management
cat("Combining results...\n")
pd_comprehensive_results_interactions <- bind_rows(lapply(all_comprehensive_results_interactions, function(agonist_list) {
  result <- bind_rows(agonist_list)
  gc()  # Garbage collect after each agonist
  return(result)
}))

# Clear intermediate results to free memory
rm(all_comprehensive_results_interactions_list)
rm(all_comprehensive_results_interactions)
gc()

cat("Comprehensive simulations with interactions completed successfully!\n")

# =============================================================================
# PART 9: COMPREHENSIVE DATA ANALYSIS WITH INTERACTIONS
# =============================================================================

# Extract peak responses for all agonists with interactions
extract_comprehensive_peak_responses_interactions <- function(data) {
  grouped_data <- data %>%
    mutate(day = floor(time/24) + 1) %>%
    group_by(agonist_name, dose_mg, day, treatment_group) %>%
    summarize(
      peak_agg = max(Agg), 
      peak_integrin = max(Integrin_act),
      peak_plc = max(PLCgamma2_act),
      peak_ca = max(Ca),
      peak_txa2 = max(TxA2_level),
      avg_BTK_free = mean(BTK_free),
      max_ibr_conc = max(Ibr_conc),
      .groups = 'drop'
    )
  
  return(grouped_data)
}

# Get comprehensive peak responses with interactions
comprehensive_peak_responses_interactions <- extract_comprehensive_peak_responses_interactions(pd_comprehensive_results_interactions)

# Calculate inhibition relative to control for each agonist with interactions
calculate_comprehensive_inhibition_interactions <- function() {
  day14_data <- comprehensive_peak_responses_interactions %>%
    filter(day == 14)
  
  # Get control responses for each agonist
  control_responses <- day14_data %>%
    filter(dose_mg == 0) %>%
    dplyr::select(agonist_name, peak_agg) %>%
    dplyr::rename(control_agg = peak_agg)
  
  # Calculate percent inhibition for each dose
  inhibition_data <- day14_data %>%
    left_join(control_responses, by = "agonist_name") %>%
    mutate(
      percent_inhibition = pmax(0, 100 * (1 - peak_agg/control_agg))
    ) %>%
    dplyr::select(agonist_name, dose_mg, peak_agg, control_agg, percent_inhibition)
  
  return(inhibition_data)
}

comprehensive_inhibition_data_interactions <- calculate_comprehensive_inhibition_interactions()

# =============================================================================
# SECURE DATA STORAGE - COMPREHENSIVE INHIBITION DATA
# =============================================================================

# Load secure storage and integrity management modules
source("secure_data_storage.r")
source("data_integrity_manager.r")

# Define required columns for validation
required_inhibition_columns <- c("agonist_name", "dose_mg", "percent_inhibition", 
                                "peak_agg", "control_agg")

# Save comprehensive inhibition data with security features
cat("\n=== SAVING COMPREHENSIVE INHIBITION DATA ===\n")

# Save as CSV for easy analysis
inhibition_csv_result <- secure_data_save(
  data = comprehensive_inhibition_data_interactions,
  file_path = "comprehensive_inhibition_data_interactions.csv",
  data_name = "Comprehensive Platelet Inhibition Data",
  format = "csv",
  required_columns = required_inhibition_columns,
  min_rows = 6,  # At least one row per agonist
  create_backup = TRUE,
  verify_integrity = TRUE,
  verbose = TRUE
)

# Save as RDS for R-specific analysis
inhibition_rds_result <- secure_data_save(
  data = comprehensive_inhibition_data_interactions,
  file_path = "comprehensive_inhibition_data_interactions.rds",
  data_name = "Comprehensive Platelet Inhibition Data (RDS)",
  format = "rds",
  required_columns = required_inhibition_columns,
  min_rows = 6,
  create_backup = TRUE,
  verify_integrity = TRUE,
  verbose = TRUE
)

# Save to ml_results directory for ML pipeline integration
ml_results_dir <- get_ml_results_dir(create_if_missing = TRUE)
inhibition_ml_result <- secure_data_save(
  data = comprehensive_inhibition_data_interactions,
  file_path = build_path(ml_results_dir, "comprehensive_inhibition_data_interactions.csv"),
  data_name = "Comprehensive Platelet Inhibition Data (ML Results)",
  format = "csv",
  required_columns = required_inhibition_columns,
  min_rows = 6,
  create_backup = TRUE,
  verify_integrity = TRUE,
  verbose = TRUE
)

# Create summary of storage operations
storage_summary <- data.frame(
  File = c("comprehensive_inhibition_data_interactions.csv",
           "comprehensive_inhibition_data_interactions.rds",
           "ml_results/comprehensive_inhibition_data_interactions.csv"),
  Status = c(ifelse(inhibition_csv_result$success, "SUCCESS", "FAILED"),
             ifelse(inhibition_rds_result$success, "SUCCESS", "FAILED"),
             ifelse(inhibition_ml_result$success, "SUCCESS", "FAILED")),
  Checksum = c(substr(inhibition_csv_result$checksum_original %||% "N/A", 1, 16),
               substr(inhibition_rds_result$checksum_original %||% "N/A", 1, 16),
               substr(inhibition_ml_result$checksum_original %||% "N/A", 1, 16)),
  Size_MB = c(round(object.size(comprehensive_inhibition_data_interactions) / 1024^2, 3),
              round(object.size(comprehensive_inhibition_data_interactions) / 1024^2, 3),
              round(object.size(comprehensive_inhibition_data_interactions) / 1024^2, 3))
)

cat("\n=== STORAGE OPERATION SUMMARY ===\n")
print(storage_summary)

# Create/update data integrity manifest for comprehensive inhibition data
successful_files <- character(0)
if (inhibition_csv_result$success) {
  successful_files <- c(successful_files, "comprehensive_inhibition_data_interactions.csv")
}
if (inhibition_rds_result$success) {
  successful_files <- c(successful_files, "comprehensive_inhibition_data_interactions.rds")
}
if (inhibition_ml_result$success) {
  ml_file_path <- build_path(get_ml_results_dir(), "comprehensive_inhibition_data_interactions.csv")
  successful_files <- c(successful_files, ml_file_path)
}

# Create integrity manifest for successfully saved files
if (length(successful_files) > 0) {
  cat("\n=== CREATING DATA INTEGRITY MANIFEST ===\n")
  manifest_result <- create_integrity_manifest(
    data_files = successful_files,
    manifest_path = "comprehensive_inhibition_manifest.json",
    verbose = TRUE
  )
  
  # Perform immediate integrity verification
  if (manifest_result$success) {
    cat("\n=== VERIFYING DATA INTEGRITY ===\n")
    verification_result <- verify_data_integrity(
      manifest_path = "comprehensive_inhibition_manifest.json",
      verbose = TRUE
    )
    
    # Add verification status to storage summary
    storage_summary$Integrity_Status <- ifelse(verification_result$success, "VERIFIED", "FAILED")
  }
}

# Automated backup management for comprehensive inhibition data
cat("\n=== MANAGING BACKUP FILES ===\n")
backup_management <- manage_backups(
  file_pattern = "*comprehensive_inhibition_data*_backup_*",
  max_backups = 5,
  max_age_days = 30,
  verbose = TRUE
)

# Enhanced storage summary with integrity information
cat("\n=== ENHANCED STORAGE OPERATION SUMMARY ===\n")
print(storage_summary)

if (exists("backup_management")) {
  cat(sprintf("\nBackup Management: %d files removed, %d files kept\n", 
              backup_management$removed, backup_management$kept))
}

# Log any errors
all_errors <- c(inhibition_csv_result$errors, inhibition_rds_result$errors, inhibition_ml_result$errors)
if (length(all_errors) > 0) {
  cat("\n=== STORAGE ERRORS ===\n")
  for (error in all_errors) {
    cat(sprintf("ERROR: %s\n", error))
  }
} else {
  cat("\n✓ All storage operations completed successfully!\n")
  
  # Generate comprehensive integrity report
  if (exists("verification_result") && verification_result$success) {
    cat("\n✓ Data integrity verification passed!\n")
  }
}

# =============================================================================
# PART 10: COMPREHENSIVE VISUALIZATION WITH INTERACTIONS
# =============================================================================

# 1. Dose-response curves showing interaction effects
plot_interactions_dose_response <- ggplot(
  comprehensive_peak_responses_interactions %>% filter(day == 14), 
  aes(x = dose_mg, y = peak_agg, color = agonist_name, group = agonist_name)
) +
  geom_point(size = 2.5, alpha = 0.8) +
  geom_line(linewidth = 1.2, alpha = 0.9) +
  labs(
    title = "Platelet Aggregation Model with Pathway Interactions",
    subtitle = "Cross-talk between agonist pathways (Day 14 responses)",
    x = "Ibrutinib Dose (mg)",
    y = "Peak Aggregation Response",
    color = "Agonist",
    caption = "Model includes secondary mediator generation and pathway cross-talk"
  ) +
  scale_color_brewer(palette = "Set3") +
  theme_minimal() +
  theme(
    legend.position = "right",
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12, color = "gray40"),
    axis.title = element_text(size = 12),
    panel.grid.minor = element_blank(),
    legend.title = element_text(face = "bold")
  )

# Save dose-response plot as PNG
png("interactions_dose_response.png", width = 1200, height = 800, res = 150)
print(plot_interactions_dose_response)
dev.off()

# 2. Pathway interaction inhibition comparison (INCLUDING 0 DOSE CONTROL)
plot_interactions_inhibition <- ggplot(
  comprehensive_inhibition_data_interactions, # Include ALL doses including 0 mg control
  aes(x = dose_mg, y = percent_inhibition, color = agonist_name, group = agonist_name)
) +
  geom_point(size = 2.5, alpha = 0.8) +
  geom_line(linewidth = 1.2, alpha = 0.9) +
  geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
  # Highlight the control group (0 dose) with special annotation
  geom_point(data = comprehensive_inhibition_data_interactions %>% filter(dose_mg == 0),
             aes(x = dose_mg, y = percent_inhibition), size = 4, shape = 21, 
             stroke = 2, fill = "white", alpha = 1) +
  annotate("text", x = 50, y = -5, label = "Control\n(0% inhibition)", hjust = 0, size = 3.5, color = "gray30") +
  labs(
    title = "Platelet Aggregation Inhibition with Pathway Interactions",
    subtitle = "Cross-pathway effects modify individual agonist responses - Control baseline shown at 0 mg",
    x = "Ibrutinib Dose (mg)",
    y = "Percent Inhibition (%)",
    color = "Agonist",
    caption = "Model captures secondary mediator effects and pathway crosstalk. Control (0 mg) shows 0% inhibition baseline."
  ) +
  scale_color_brewer(palette = "Set3") +
  scale_y_continuous(labels = scales::percent_format(scale = 1), 
                     breaks = seq(-10, 100, by = 20),
                     limits = c(-10, 100)) +
  scale_x_continuous(breaks = c(0, 140, 280, 420, 560, 840)) +
  theme_minimal() +
  theme(
    legend.position = "right",
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12, color = "gray40"),
    axis.title = element_text(size = 12),
    panel.grid.minor = element_blank(),
    legend.title = element_text(face = "bold")
  )

# Save inhibition plot as PNG
png("interactions_inhibition.png", width = 1200, height = 800, res = 150)
print(plot_interactions_inhibition)
dev.off()

# 3. Multi-compartment dynamics visualization
plot_interactions_dynamics <- ggplot(
  pd_comprehensive_results_interactions %>% 
    filter(dose_mg %in% c(0, 420) & time <= 72 & agonist_name %in% c("ADP", "Collagen", "Thrombin")) %>%
    dplyr::select(time, agonist_name, dose_mg, Agg, TxA2_level, Ca, PLCgamma2_act) %>%
    pivot_longer(cols = c(Agg, TxA2_level, Ca, PLCgamma2_act), names_to = "compartment", values_to = "level"),
  aes(x = time/24, y = level, color = factor(dose_mg), linetype = compartment)
) +
  geom_line(linewidth = 1) +
  facet_wrap(~agonist_name, scales = "free_y") +
  labs(
    title = "Multi-Compartment Platelet Dynamics with Interactions",
    subtitle = "First 3 days: Control vs Clinical Dose (420 mg)",
    x = "Time (days)",
    y = "Normalized Level",
    color = "Dose (mg)",
    linetype = "Compartment",
    caption = "Shows aggregation, TxA2, calcium, and PLCγ2 dynamics"
  ) +
  scale_color_manual(values = c("0" = "blue", "420" = "red")) +
  theme_minimal() +
  theme(
    legend.position = "bottom",
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12, color = "gray40"),
    axis.title = element_text(size = 12),
    strip.text = element_text(face = "bold")
  )

# Save dynamics plot as PNG
png("interactions_dynamics.png", width = 1400, height = 900, res = 150)
print(plot_interactions_dynamics)
dev.off()

# 4. Pathway cross-talk heatmap
crosstalk_data <- comprehensive_peak_responses_interactions %>%
  filter(day == 14 & dose_mg == 420) %>%
  dplyr::select(agonist_name, peak_agg, peak_txa2, peak_ca, peak_plc)

# Create matrix manually
crosstalk_matrix <- as.matrix(crosstalk_data[, -1])
rownames(crosstalk_matrix) <- crosstalk_data$agonist_name

# Normalize for heatmap
crosstalk_normalized <- scale(crosstalk_matrix)

# Convert to long format for ggplot
crosstalk_long <- as.data.frame(crosstalk_normalized) %>%
  mutate(agonist = rownames(.)) %>%
  pivot_longer(cols = -agonist, names_to = "compartment", values_to = "normalized_response")

plot_crosstalk_heatmap <- ggplot(crosstalk_long, aes(x = compartment, y = agonist, fill = normalized_response)) +
  geom_tile(color = "white", linewidth = 0.5) +
  scale_fill_gradient2(low = "blue", mid = "white", high = "red", midpoint = 0) +
  labs(
    title = "Pathway Cross-talk Response Matrix",
    subtitle = "Clinical dose (420 mg) - Day 14 normalized responses",
    x = "Platelet Compartment",
    y = "Primary Agonist",
    fill = "Normalized\nResponse",
    caption = "Shows how each agonist activates different platelet compartments"
  ) +
  theme_minimal() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1),
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12, color = "gray40")
  )

# Save heatmap as PNG
png("interactions_crosstalk_heatmap.png", width = 1000, height = 800, res = 150)
print(plot_crosstalk_heatmap)
dev.off()

# =============================================================================
# PART 11: RESULTS SUMMARY AND VALIDATION WITH INTERACTIONS
# =============================================================================

# Summary statistics with interactions
cat("\n=== COMPREHENSIVE MODEL WITH INTERACTIONS RESULTS ===\n\n")

# Control responses by agonist with interactions
control_summary_interactions <- comprehensive_peak_responses_interactions %>%
  filter(dose_mg == 0 & day == 14) %>%
  dplyr::select(agonist_name, peak_agg, peak_txa2, peak_ca, peak_plc) %>%
  arrange(desc(peak_agg))

cat("Control group responses with pathway interactions (Day 14):\n")
print(control_summary_interactions)

# Clinical dose effects with interactions
clinical_dose_summary_interactions <- comprehensive_inhibition_data_interactions %>%
  filter(dose_mg == 420) %>%
  dplyr::select(agonist_name, percent_inhibition, peak_agg, control_agg) %>%
  arrange(desc(percent_inhibition))

cat("\nClinical dose (420 mg) effects with pathway interactions:\n")
print(clinical_dose_summary_interactions)

# Pathway interaction analysis
interaction_effects <- comprehensive_peak_responses_interactions %>%
  filter(dose_mg == 420 & day == 14) %>%
  mutate(
    txa2_amplification = peak_txa2 / peak_agg,
    calcium_mobilization = peak_ca / peak_agg,
    plc_activation_ratio = peak_plc / peak_agg
  ) %>%
  dplyr::select(agonist_name, txa2_amplification, calcium_mobilization, plc_activation_ratio) %>%
  arrange(desc(txa2_amplification))

cat("\nPathway interaction effects (Clinical dose):\n")
print(interaction_effects)

# Cross-pathway sensitivity analysis (simplified without pathway classification)
crosstalk_sensitivity <- comprehensive_inhibition_data_interactions %>%
  filter(dose_mg == 420) %>%
  dplyr::select(agonist_name, percent_inhibition) %>%
  arrange(desc(percent_inhibition))

cat("\nCross-pathway sensitivity to ibrutinib with interactions:\n")
print(crosstalk_sensitivity)

# Data validation
cat("\n=== DATA VALIDATION WITH INTERACTIONS ===\n")
cat(sprintf("Total agonists simulated: %d\n", length(unique(pd_comprehensive_results_interactions$agonist_name))))
cat(sprintf("Total dose levels: %d\n", length(unique(pd_comprehensive_results_interactions$dose_mg))))
cat(sprintf("Simulation time points: %d\n", length(unique(pd_comprehensive_results_interactions$time))))
cat(sprintf("Total data points: %d\n", nrow(pd_comprehensive_results_interactions)))
cat(sprintf("Compartments tracked: %d\n", 7))  # 7 state variables

# Check for simulation errors with interactions
error_check_interactions <- pd_comprehensive_results_interactions %>%
  summarize(
    max_agg = max(Agg, na.rm = TRUE),
    min_agg = min(Agg, na.rm = TRUE),
    any_na = any(is.na(Agg)),
    any_infinite = any(is.infinite(Agg))
  )

if (error_check_interactions$max_agg > 1.05 || error_check_interactions$any_na || error_check_interactions$any_infinite) {
  cat("\nWARNING: Simulation errors detected with interactions:\n")
  print(error_check_interactions)
} else {
  cat("\nAll simulations with interactions completed successfully with valid results.\n")
}

# Interaction-specific validation
interaction_validation <- pd_comprehensive_results_interactions %>%
  group_by(agonist_name, dose_mg) %>%
  summarize(
    max_txa2 = max(TxA2_level),
    max_ca = max(Ca),
    max_plc = max(PLCgamma2_act),
    .groups = 'drop'
  ) %>%
  summarize(
    txa2_range = paste(round(min(max_txa2), 3), "-", round(max(max_txa2), 3)),
    ca_range = paste(round(min(max_ca), 3), "-", round(max(max_ca), 3)),
    plc_range = paste(round(min(max_plc), 3), "-", round(max(max_plc), 3))
  )

cat("\nCompartment response ranges:\n")
cat(sprintf("TxA2 levels: %s\n", interaction_validation$txa2_range))
cat(sprintf("Calcium levels: %s\n", interaction_validation$ca_range))
cat(sprintf("PLCγ2 activation: %s\n", interaction_validation$plc_range))

cat("\n=== COMPREHENSIVE MODEL WITH INTERACTIONS COMPLETE ===\n")
cat("Platelet aggregation model successfully incorporates:\n")
cat("• Pathway cross-talk and secondary mediator generation\n")
cat("• Multi-compartment platelet dynamics (7 state variables)\n")
cat("• Agonist-specific interaction profiles\n")
cat("• Visualization of complex pathway interactions\n")
cat("• Comprehensive validation of interaction effects\n")

# =============================================================================

# Model parameters
model_parameters <- list(
  ibrutinib_IC50 = 10.0,      # nM
  k_agg = 0.5,                # Aggregation rate constant
  k_deagg = 0.1,              # Deaggregation rate constant
  k_txa2_synthesis = 0.3,     # TxA2 synthesis rate
  k_txa2_degradation = 0.2,   # TxA2 degradation rate
  k_ca_influx = 0.4,          # Calcium influx rate
  k_ca_outflow = 0.1,         # Calcium outflow rate
  k_plc_act = 0.6,            # PLCγ2 activation rate
  k_plc_deact = 0.2,          # PLCγ2 deactivation rate
  k_integrin_act = 0.5,       # Integrin activation rate
  k_integrin_deact = 0.1,     # Integrin deactivation rate
  k_granule_release = 0.3,    # Granule release rate
  k_granule_refill = 0.1,     # Granule refill rate
  k_thrombin_gen = 0.4,       # Thrombin generation rate
  k_thrombin_decay = 0.1      # Thrombin decay rate
)

# Initial conditions
initial_conditions <- c(
  Agg = 0.0,                  # Platelet aggregation
  TxA2_level = 0.0,           # TxA2 concentration
  Ca = 0.0,                   # Calcium concentration
  PLCgamma2_act = 0.0,        # PLCγ2 activation
  Integrin_act = 0.0,         # Integrin activation
  granule_ADP_content = 1.0,  # Granule ADP content
  thrombin_gen_factor = 0.0   # Thrombin generation factor
)

# Simulation time points (optimized resolution)
sim_time <- seq(0, 14*24, by = 1.0)  # Reduced from 0.1 to 1.0 hour intervals

# Parallel simulation function
run_single_simulation <- function(params) {
  agonist_name <- params$agonist_name
  dose <- params$dose
  
  # Get PK data for this dose (cached)
  pk_data <- run_pk_simulation(dose)
  
  # Run simulation with error handling
  tryCatch({
    sim_result <- ode(y = initial_conditions, 
                      times = sim_time,
                      func = comprehensive_pd_model_with_interactions,
                      parms = model_parameters,
                      ibrutinib_conc = pk_data,
                      agonist_name = agonist_name,
                      agonist_params = agonist_params,
                      rtol = 1e-8, atol = 1e-10,  # Improved tolerances for better stability
                      maxsteps = 10000)  # Increased max steps for complex dynamics
    
    # Add metadata
    sim_df <- as.data.frame(sim_result)
    sim_df$agonist <- agonist_name
    sim_df$dose_mg <- dose
    
    return(sim_df)
  }, error = function(e) {
    cat(sprintf("Error in simulation for %s at %d mg: %s\n", agonist_name, dose, e$message))
    return(NULL)
  })
}

# Create parameter combinations
param_combinations <- expand.grid(
  agonist_name = names(agonist_params),
  dose = doses,
  stringsAsFactors = FALSE
)
param_list <- split(param_combinations, seq(nrow(param_combinations)))

# Run simulations in parallel
cat(sprintf("Running %d simulations in parallel...\n", length(param_list)))
cl <- makeCluster(min(detectCores() - 1, 4))  # Limit to 4 cores max
clusterEvalQ(cl, {
  library(deSolve)
  library(dplyr)
})
clusterExport(cl, c("run_pk_simulation", "comprehensive_pd_model_with_interactions",
                    "initial_conditions", "sim_time", "model_parameters",
                    "agonist_params", "pk_cache", "simulation_cache", "analysis_cache",
                    "safeguard", "hill_function", "dose_response", "get_comprehensive_stimuli_with_interactions",
                    "QSP_PARAMETERS"))

results_list <- parLapply(cl, param_list, run_single_simulation)
stopCluster(cl)

# Remove NULL results (failed simulations)
results_list <- results_list[!sapply(results_list, is.null)]

# Memory management
gc()

# Combine all results
all_results <- bind_rows(results_list)

# =============================================================================
# PART 6: VISUALIZATION
# =============================================================================

# Plot 1: PK profiles
pk_plot <- ggplot(pk_all_results, aes(x = time/24, y = Cp_nM, color = factor(dose_mg))) +
  geom_line(linewidth = 1.2) +
  labs(title = "Ibrutinib Plasma Concentration vs Time - Daily Dosing",
       subtitle = "Multiple daily peaks showing repeated dosing over 14 days",
       x = "Time (days)",
       y = "Concentration (nM)",
       color = "Dose (mg)") +
  scale_x_continuous(breaks = seq(0, 14, by = 2)) +
  theme_minimal() +
  theme(legend.position = "right")

# Save PK plot as PNG to visualize daily peaks
png("interactions_pk_profiles.png", width = 1200, height = 800, res = 150)
print(pk_plot)
dev.off()
# 
# # Plot 2: Aggregation over time for different agonists and doses
# agg_plot <- ggplot(all_results, aes(x = time, y = Agg, color = factor(dose_mg))) +
#   geom_line() +
#   facet_wrap(~ agonist, scales = "free_y") +
#   labs(title = "Platelet Aggregation Over Time",
#        x = "Time (hours)",
#        y = "Aggregation Level",
#        color = "Dose (mg)") +
#   theme_minimal() +
#   theme(axis.text.x = element_text(angle = 45, hjust = 1))
# 
# # Plot 3: TxA2 levels over time
# txa2_plot <- ggplot(all_results, aes(x = time, y = TxA2_level, color = factor(dose_mg))) +
#   geom_line() +
#   facet_wrap(~ agonist, scales = "free_y") +
#   labs(title = "TxA2 Levels Over Time",
#        x = "Time (hours)",
#        y = "TxA2 Level",
#        color = "Dose (mg)") +
#   theme_minimal() +
#   theme(axis.text.x = element_text(angle = 45, hjust = 1))
# 
# # Combine plots
# combined_plot <- pk_plot + agg_plot + txa2_plot + plot_layout(ncol = 1)
# 
# # Save combined plot
# ggsave("ibrutinib_comprehensive_model_results.svg", combined_plot, width = 12, height = 10, device = svglite)

# Print summary statistics
summary_stats <- all_results %>%
  group_by(agonist, dose_mg) %>%
  summarise(
    max_aggregation = max(Agg),
    max_TxA2 = max(TxA2_level),
    final_aggregation = last(Agg),
    final_TxA2 = last(TxA2_level),
    .groups = 'drop'
  )

print(summary_stats)

# =============================================================================
# STOCHASTIC DOSE-RESPONSE DEMONSTRATION FUNCTIONS
# =============================================================================

# Generate dose-response curves for stochastic aggregation inhibition
generate_stochastic_dose_response_curves <- function(dose_range_mg = c(0, 140, 280, 420, 560, 700),
                                                    n_patients = 10,
                                                    save_results = TRUE) {

  cat("\n=== GENERATING STOCHASTIC DOSE-RESPONSE CURVES ===\n")
  cat("Simulating", length(dose_range_mg), "doses across", n_patients, "patients\n")

  # Convert doses to steady-state concentrations (approximate)
  # Clinical data: 420mg dose achieves ~200-400 nM steady-state
  dose_to_conc_factor <- 300 / 420  # nM per mg

  results_list <- list()

  for (dose_mg in dose_range_mg) {
    # Calculate approximate steady-state concentration
    steady_state_conc <- dose_mg * dose_to_conc_factor

    cat(sprintf("Dose: %d mg (≈%.0f nM steady-state)\n", dose_mg, steady_state_conc))

    # Simulate multiple patients for this dose
    dose_results <- data.frame()

    for (patient in 1:n_patients) {
      for (agonist in names(FIXED_AGONIST_CONCENTRATIONS)) {

        # Calculate stochastic inhibition
        remaining_activity <- stochastic_aggregation_inhibition(
          steady_state_conc, agonist, patient_id = patient, time_point = 1
        )

        inhibition_pct <- (1 - remaining_activity) * 100

        # Store result
        dose_results <- rbind(dose_results, data.frame(
          dose_mg = dose_mg,
          steady_state_conc_nM = steady_state_conc,
          patient_id = patient,
          agonist = agonist,
          agonist_concentration = FIXED_AGONIST_CONCENTRATIONS[[agonist]],
          inhibition_percent = inhibition_pct,
          remaining_activity = remaining_activity,
          max_possible_inhibition = LITERATURE_MAX_INHIBITION[[agonist]]
        ))
      }
    }

    results_list[[as.character(dose_mg)]] <- dose_results
  }

  # Combine all results
  all_results <- do.call(rbind, results_list)

  # Calculate summary statistics
  summary_stats <- all_results %>%
    group_by(dose_mg, agonist) %>%
    summarise(
      mean_inhibition = mean(inhibition_percent),
      sd_inhibition = sd(inhibition_percent),
      min_inhibition = min(inhibition_percent),
      max_inhibition = max(inhibition_percent),
      cv_inhibition = sd(inhibition_percent) / mean(inhibition_percent) * 100,
      .groups = 'drop'
    )

  # Print summary
  cat("\n=== DOSE-RESPONSE SUMMARY STATISTICS ===\n")
  print(summary_stats)

  # Save results if requested
  if (save_results) {
    write.csv(all_results, "stochastic_dose_response_detailed.csv", row.names = FALSE)
    write.csv(summary_stats, "stochastic_dose_response_summary.csv", row.names = FALSE)
    cat("\nResults saved to CSV files\n")
  }

  return(list(detailed = all_results, summary = summary_stats))
}

# Demonstrate literature validation
validate_against_literature <- function() {
  cat("\n=== LITERATURE VALIDATION ===\n")
  cat("Comparing model predictions to published clinical data\n")

  # Clinical dose: 420 mg daily (standard ibrutinib dose)
  clinical_dose_mg <- 420
  clinical_conc_nM <- 300  # Approximate steady-state concentration

  # Literature-reported inhibition values at clinical dose
  literature_values <- data.frame(
    agonist = c("collagen", "ADP", "thrombin", "arachidonic_acid", "ristocetin"),
    literature_inhibition = c(98, 6, 10, 55, 5),
    source = c("Bye et al. 2017", "Kamel et al. 2018", "Kamel et al. 2018",
               "Bye et al. 2017", "Kamel et al. 2018")
  )

  # Model predictions (average of 20 patients)
  model_predictions <- data.frame()

  for (agonist in literature_values$agonist) {
    inhibitions <- numeric(20)
    for (i in 1:20) {
      remaining <- stochastic_aggregation_inhibition(clinical_conc_nM, agonist,
                                                   patient_id = i, time_point = 1)
      inhibitions[i] <- (1 - remaining) * 100
    }

    model_predictions <- rbind(model_predictions, data.frame(
      agonist = agonist,
      model_mean = mean(inhibitions),
      model_sd = sd(inhibitions),
      model_min = min(inhibitions),
      model_max = max(inhibitions)
    ))
  }

  # Combine with literature
  validation_results <- merge(literature_values, model_predictions, by = "agonist")
  validation_results$difference <- validation_results$model_mean - validation_results$literature_inhibition
  validation_results$percent_error <- abs(validation_results$difference) / validation_results$literature_inhibition * 100

  cat("\n=== VALIDATION RESULTS ===\n")
  print(validation_results[, c("agonist", "literature_inhibition", "model_mean",
                               "model_sd", "difference", "percent_error")])

  # Save validation results
  write.csv(validation_results, "literature_validation_results.csv", row.names = FALSE)

  return(validation_results)
}

# Run demonstration
cat("\n", paste(rep("=", 80), collapse=""), "\n")
cat("STOCHASTIC AGGREGATION INHIBITION DEMONSTRATION\n")
cat(paste(rep("=", 80), collapse=""), "\n")

# Generate dose-response curves
dose_response_results <- generate_stochastic_dose_response_curves()

# Validate against literature
validation_results <- validate_against_literature()

cat("\n=== DEMONSTRATION COMPLETE ===\n")
cat("Files generated:\n")
cat("- stochastic_dose_response_detailed.csv\n")
cat("- stochastic_dose_response_summary.csv\n")
cat("- literature_validation_results.csv\n")
