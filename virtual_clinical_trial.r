# =============================================================================
# EVIDENCE-BASED VIRTUAL SAFETY TRIAL SIMULATOR FOR IBRUTINIB
# =============================================================================
# Updated with comprehensive bleeding risk parameters from 14,000+ patient meta-analyses
# Incorporates multiplicative risk interactions, temporal evolution, and pathway weighting

# Load file utilities first
if (file.exists("ml_file_utils.r")) {
  source("ml_file_utils.r")
} else {
  cat("Warning: ml_file_utils.r not found. Using basic file operations.\n")
}

library(dplyr)
library(ggplot2)
library(survival)
library(survminer)
library(gridExtra)
library(splines)
library(mgcv)
set.seed(123)

# Source the updated population generator and enhanced platelet model
source("synthetic_population_generator.r")
source("ibrutinib_comprehensive_model_with_interactions.r")

# Load QSP-derived parameters for bleeding risk calculations
if (file.exists("qsp_derived_parameters.csv")) {
  qsp_params <- read.csv("qsp_derived_parameters.csv", stringsAsFactors = FALSE)
  cat("✓ Using QSP-derived parameters for bleeding risk calculations\n")
} else {
  cat("Warning: QSP parameters file not found. Using fallback literature values.\n")
  # Fallback to literature values if QSP parameters not available
  qsp_params <- data.frame(
    agonist = c("collagen", "adp", "thrombin", "arachidonic_acid", "ristocetin"),
    ic50_nM = c(150, 1800, 2200, 500, 5000),
    max_inhibition_percent = c(70, 6, 10, 55, 5),
    hill_coefficient = c(1.0, 1.0, 1.0, 1.0, 1.0)
  )
}

# =============================================================================
# PART 1: ENHANCED PHARMACOKINETIC MODEL WITH CLINICAL VARIABILITY
# =============================================================================

calculate_clinical_ibrutinib_pk <- function(population, dose_mg) {

  # ENHANCED: Clinical PK parameters for multiple dosing regimens
  # Standard dosing: 420mg daily (CLL/SLL), 560mg daily (MCL),
  # 280mg daily (dose reduction)

  # Base PK parameters by dose with clinical variability
  pk_parameters <- list(
    "560" = list(cmax = 200, auc = 1270),  # MCL dose
    "420" = list(cmax = 150, auc = 953),   # CLL/SLL dose
    "280" = list(cmax = 105, auc = 667),   # First dose reduction
    "140" = list(cmax = 55, auc = 333)     # Second dose reduction
  )

  dose_key <- as.character(dose_mg)
  if (!dose_key %in% names(pk_parameters)) {
    # Linear scaling for other doses
    base_cmax <- dose_mg * (150 / 420)  # Scale from 420mg reference
    base_auc <- dose_mg * (953 / 420)
  } else {
    base_cmax <- pk_parameters[[dose_key]]$cmax
    base_auc <- pk_parameters[[dose_key]]$auc
  }
  
  n_patients <- nrow(population)
  
  # ENHANCED: Individual variability factors with interaction effects
  
  # Age effect (exponential increase in exposure for elderly)
  # Hazard ratio 7.49 for age >70 translates to exposure changes
  age_factor <- ifelse(population$age >= 70, 1.25, 
                      ifelse(population$age >= 65, 1.15, 1.0)) * 
                (1 + (population$age - 65) * 0.008)
  
  # Weight effect (allometric scaling with BMI interaction)
  weight_factor <- (population$weight / 70)^0.75
  bmi_factor <- ifelse(population$bmi > 30, 1.1, ifelse(population$bmi < 20, 0.9, 1.0))
  
  # Kidney function effect (enhanced for elderly CLL patients)
  renal_factor <- pmax(population$creatinine_clearance / 90, 0.4)
  
  # Liver function effect (disease and age related)
  liver_factor <- pmax(1 - (population$alt - 25) * 0.008, 0.25)
  
  # UPDATED: Genetic metabolizer effect with population-specific distributions
  genetic_factor <- case_when(
    population$metabolizer_phenotype == "Poor" ~ 2.8,      # Increased variability
    population$metabolizer_phenotype == "Intermediate" ~ 1.6,
    population$metabolizer_phenotype == "Normal" ~ 1.0,
    population$metabolizer_phenotype == "Ultrarapid" ~ 0.55
  )
  
  # ENHANCED: Drug interaction effects with dose-dependent relationships
  # CYP3A4 inhibitors: stronger effect at higher doses
  cyp3a4_inhibitor_factor <- ifelse(population$cyp3a4_inhibitors, 
                                   1.8 + (dose_mg/420) * 0.4, 1.0)
  
  # CYP3A4 inducers: more pronounced reduction
  cyp3a4_inducer_factor <- ifelse(population$cyp3a4_inducers, 
                                 0.45 - (dose_mg/420) * 0.1, 1.0)
  
  # PPI interaction (reduced absorption)
  ppi_factor <- ifelse(population$ppi_use, 0.75, 1.0)
  
  # Sex effect (higher exposure in females, especially elderly)
  sex_factor <- ifelse(population$sex == "Female", 
                      1.25 + (population$age - 65) * 0.005, 1.0)
  
  # Disease severity effect (advanced CLL affects metabolism)
  disease_factor <- case_when(
    population$cll_stage %in% c("III", "IV") ~ 1.15,
    population$high_comorbidity_score ~ 1.12,
    TRUE ~ 1.0
  )
  
  # Calculate individual exposure with all factors
  exposure_factor <- age_factor * genetic_factor * cyp3a4_inhibitor_factor * 
                    cyp3a4_inducer_factor * ppi_factor * sex_factor * 
                    disease_factor / (weight_factor * bmi_factor) * 
                    renal_factor * liver_factor
  
  individual_cmax <- base_cmax * exposure_factor
  individual_auc <- base_auc * exposure_factor
  
  # ENHANCED: Random variability with correlated PK parameters
  # Higher CV in clinical practice (50% vs 40% in trials)
  cv_cmax <- 0.50
  cv_auc <- 0.45
  
  # Correlated random effects (r=0.8 between Cmax and AUC)
  sigma_matrix <- matrix(c(cv_cmax^2, cv_cmax*cv_auc*0.8, 
                          cv_cmax*cv_auc*0.8, cv_auc^2), 2, 2)
  random_effects <- exp(MASS::mvrnorm(n_patients, c(0, 0), sigma_matrix))
  
  individual_cmax <- individual_cmax * random_effects[, 1]
  individual_auc <- individual_auc * random_effects[, 2]
  
  # Convert to nM for BTK inhibition calculations (MW = 440.5 g/mol)
  cmax_nm <- individual_cmax * 1000 / 440.5
  auc_nm_h <- individual_auc * 1000 / 440.5
  
  # Calculate steady-state concentrations for multiple dosing
  # Clinical patients often have adherence issues
  adherence_factor <- rbeta(n_patients, 9, 1.5)  # Mean ~85% adherence
  css_avg_nm <- cmax_nm * 0.4 * adherence_factor  # Average steady-state
  
  return(data.frame(
    cmax_ng_ml = round(individual_cmax, 1),
    auc_ng_h_ml = round(individual_auc, 1),
    cmax_nm = round(cmax_nm, 1),
    auc_nm_h = round(auc_nm_h, 1),
    css_avg_nm = round(css_avg_nm, 1),
    adherence_factor = round(adherence_factor, 3),
    exposure_factor = round(exposure_factor, 2)
  ))
}

# =============================================================================
# PART 2: COMPREHENSIVE BLEEDING RISK MODEL WITH ALL INTERACTIONS
# =============================================================================

calculate_comprehensive_bleeding_risk <- function(population, pk_data, treatment_arm) {
  
  n_patients <- nrow(population)
  
  # Initialize bleeding risk components
  risk_components <- data.frame(
    patient_id = population$patient_id,
    treatment_arm = treatment_arm,
    baseline_risk = numeric(n_patients),
    ibrutinib_risk = numeric(n_patients),
    interaction_risk = numeric(n_patients),
    temporal_risk = numeric(n_patients),
    total_risk = numeric(n_patients)
  )
  
  # UPDATED: Baseline bleeding risk from meta-analysis
  # Clinical control rate: 2.5% per year
  baseline_annual_risk <- 0.025
  
  # Age-dependent baseline risk (exponential relationship)
  # HR 7.49 for age ≥70 from SEER-Medicare analysis
  age_hr <- ifelse(population$age >= 75, 7.49,
                  ifelse(population$age >= 70, 4.2,
                        ifelse(population$age >= 65, 2.1, 1.0)))
  
  risk_components$baseline_risk <- baseline_annual_risk * age_hr
  
  # For control patients (no ibrutinib effect)
  control_patients <- treatment_arm == "Control"
  
  if (any(control_patients)) {
    # Control patients have baseline risk plus comorbidity effects
    comorbidity_multiplier <- 1 + 
      as.numeric(population$cardiovascular_disease) * 0.4 +
      as.numeric(population$high_comorbidity_score) * 0.3 +
      as.numeric(population$diabetes) * 0.2 +
      as.numeric(population$anticoagulants) * 0.8  # Still some anticoagulant risk
    
    risk_components$total_risk[control_patients] <- 
      risk_components$baseline_risk[control_patients] * comorbidity_multiplier[control_patients]
  }
  
  # For ibrutinib-treated patients - COMPREHENSIVE RISK MODEL
  treated_patients <- !control_patients
  
  if (any(treated_patients)) {
    
    # 1. IBRUTINIB DIRECT EFFECT
    # Base ibrutinib bleeding risk multiplier from meta-analysis
    # Clinical practice: 19% vs control 2.5% = 7.6× multiplier
    base_ibrutinib_multiplier <- 7.6
    
    # Concentration-dependent effect using average steady-state
    css_nm <- pk_data$css_avg_nm[treated_patients]
    
    # Dose-response relationship: higher exposure = higher bleeding risk
    # Plateau at high concentrations
    concentration_effect <- 1 + (css_nm / (css_nm + 50)) * 1.5  # Emax model
    
    ibrutinib_direct_risk <- baseline_annual_risk * base_ibrutinib_multiplier * concentration_effect
    risk_components$ibrutinib_risk[treated_patients] <- ibrutinib_direct_risk
    
    # 2. MULTIPLICATIVE DRUG INTERACTIONS
    
    # Anticoagulant interactions (MAJOR RISK FACTOR)
    # OR 2.54 from nested case-control study (614 cases, 2,407 controls)
    anticoag_multiplier <- case_when(
      population$anticoag_type[treated_patients] == "Warfarin" ~ 1.99,
      population$doac_type[treated_patients] == "Rivaroxaban" ~ 2.8,  # Higher risk DOAC
      population$doac_type[treated_patients] == "Apixaban" ~ 2.0,     # Lower risk DOAC
      population$anticoag_type[treated_patients] == "DOAC" ~ 2.48,    # Other DOACs
      population$anticoag_type[treated_patients] == "Other" ~ 3.40,   # Parenteral anticoagulants
      TRUE ~ 1.0  # No anticoagulants
    )
    
    # Combined anticoagulant + antiplatelet: HR 19.2
    combined_anticoag_antiplatelet <- as.numeric(population$anticoagulants[treated_patients] & 
                                                 population$antiplatelets[treated_patients])
    anticoag_multiplier[combined_anticoag_antiplatelet == 1] <- 19.2
    
    # Antiplatelet effect (when used alone)
    antiplatelet_multiplier <- ifelse(population$antiplatelets[treated_patients] & 
                                     !population$anticoagulants[treated_patients], 1.8, 1.0)
    
    # 3. PLATELET DYSFUNCTION MARKERS (MULTIPLICATIVE)
    # von Willebrand factor ≤100 IU/dL: HR 2.73
    vwf_multiplier <- ifelse(population$vwf_low[treated_patients], 2.73, 1.0)
    
    # Factor VIII ≤174 IU/dL: HR 3.73
    factor_viii_multiplier <- ifelse(population$factor_viii_low[treated_patients], 3.73, 1.0)
    
    # Epinephrine closure time ≥240 sec: HR 2.74
    epi_closure_multiplier <- ifelse(population$epi_closure_prolonged[treated_patients], 2.74, 1.0)
    
    # Platelet count effect (continuous relationship)
    # OR 0.9 per 10×10⁹/L decrease
    platelet_multiplier <- (population$platelet_count[treated_patients] / 150)^(-0.1)
    
    # 4. COMORBIDITY INTERACTIONS
    
    # Anemia effect: OR 2.34
    # Estimated from hemoglobin levels (age and disease related)
    anemia_prob <- pmin(0.1 + (population$age[treated_patients] - 60) * 0.01 + 
                       as.numeric(population$cll_stage[treated_patients] %in% c("III", "IV")) * 0.2, 0.6)
    anemia_present <- rbinom(sum(treated_patients), 1, anemia_prob)
    anemia_multiplier <- ifelse(anemia_present == 1, 2.34, 1.0)
    
    # Cardiovascular disease interaction
    cvd_multiplier <- ifelse(population$cardiovascular_disease[treated_patients], 1.6, 1.0)
    
    # High comorbidity score interaction
    comorbidity_multiplier <- ifelse(population$high_comorbidity_score[treated_patients], 1.4, 1.0)
    
    # 5. GENETIC INTERACTIONS
    
    # CYP3A4/CYP3A5 polymorphisms affect bleeding through exposure
    metabolizer_effect <- case_when(
      population$metabolizer_phenotype[treated_patients] == "Poor" ~ 1.8,
      population$metabolizer_phenotype[treated_patients] == "Intermediate" ~ 1.3,
      population$metabolizer_phenotype[treated_patients] == "Normal" ~ 1.0,
      population$metabolizer_phenotype[treated_patients] == "Ultrarapid" ~ 0.7
    )
    
    # PLCG2 variants (affect platelet signaling)
    plcg2_multiplier <- ifelse(population$plcg2_variants[treated_patients], 0.8, 1.0)  # Protective
    
    # 6. CALCULATE MULTIPLICATIVE INTERACTION RISK
    interaction_multiplier <- anticoag_multiplier * antiplatelet_multiplier * 
                             vwf_multiplier * factor_viii_multiplier * 
                             epi_closure_multiplier * platelet_multiplier *
                             anemia_multiplier * cvd_multiplier * 
                             comorbidity_multiplier * metabolizer_effect * 
                             plcg2_multiplier
    
    # Cap maximum interaction effect at 50× to prevent unrealistic values
    interaction_multiplier <- pmin(interaction_multiplier, 50)
    
    risk_components$interaction_risk[treated_patients] <- 
      ibrutinib_direct_risk * (interaction_multiplier - 1)
    
    # 7. TEMPORAL RISK EVOLUTION
    # Phase-specific multipliers from Lipsky study
    # Months 0-1: 4×, 1-3: 3.5×, 3-6: 2.5×, 6-12: 1.8×, 12+: 1.5×
    
    # Assume patients are at steady state (after initial months)
    # Use 6-12 month multiplier for primary analysis
    temporal_multiplier <- 1.8
    
    base_treated_risk <- risk_components$baseline_risk[treated_patients] +
                        risk_components$ibrutinib_risk[treated_patients] +
                        risk_components$interaction_risk[treated_patients]
    
    risk_components$temporal_risk[treated_patients] <- 
      base_treated_risk * (temporal_multiplier - 1)
    
    # 8. CALCULATE TOTAL RISK
    risk_components$total_risk[treated_patients] <- 
      risk_components$baseline_risk[treated_patients] +
      risk_components$ibrutinib_risk[treated_patients] +
      risk_components$interaction_risk[treated_patients] +
      risk_components$temporal_risk[treated_patients]
  }
  
  # Ensure risks are reasonable (max 95% annual risk)
  risk_components$total_risk <- pmin(risk_components$total_risk, 0.95)
  
  return(risk_components)
}

# =============================================================================
# PART 3: ENHANCED PLATELET AGGREGATION RESPONSE WITH PATHWAY WEIGHTING
# =============================================================================

simulate_enhanced_platelet_response <- function(population, pk_data, treatment_arm) {
  
  n_patients <- nrow(population)
  
  # Initialize results with enhanced pathway modeling
  results <- data.frame(
    patient_id = population$patient_id,
    treatment_arm = treatment_arm,
    baseline_aggregation = population$baseline_aggregation,
    collagen_inhibition = numeric(n_patients),
    adp_inhibition = numeric(n_patients),
    arachidonic_inhibition = numeric(n_patients),
    thrombin_inhibition = numeric(n_patients),
    epinephrine_inhibition = numeric(n_patients),
    ristocetin_inhibition = numeric(n_patients)
  )
  
  # For control patients
  control_patients <- treatment_arm == "Control"
  
  if (any(control_patients)) {
    # Control patients: small variability around baseline
    results$collagen_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 3)
    results$adp_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 2)
    results$arachidonic_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 2.5)
    results$thrombin_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 2)
    results$epinephrine_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 1.5)
    results$ristocetin_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 1)
  }
  
  # For ibrutinib-treated patients - ENHANCED PATHWAY MODELING
  treated_patients <- !control_patients
  
  if (any(treated_patients)) {
    
    # Get steady-state concentrations
    css_nm <- pk_data$css_avg_nm[treated_patients]
    
    # UPDATED: Evidence-based IC50 values and maximum inhibition
    # From comprehensive QSP model and literature validation
    
    # Extract QSP-derived parameters for each agonist
    collagen_params <- qsp_params[qsp_params$agonist == "collagen", ]
    adp_params <- qsp_params[qsp_params$agonist == "ADP", ]
    arachidonic_params <- qsp_params[qsp_params$agonist == "arachidonic_acid", ]
    thrombin_params <- qsp_params[qsp_params$agonist == "thrombin", ]
    ristocetin_params <- qsp_params[qsp_params$agonist == "ristocetin", ]
    
    # Use QSP-derived parameters for dose-response calculations
    # Primary BTK-dependent pathway: Collagen (GPVI)
    collagen_activity <- dose_response_emax(css_nm, 
                                           collagen_params$ic50_nM, 
                                           collagen_params$hill_coefficient, 
                                           collagen_params$max_inhibition_percent/100)
    collagen_inhibition <- (1 - collagen_activity) * 100
    
    # Secondary pathways with reduced BTK dependence
    # ADP: Using QSP-derived parameters
    adp_activity <- dose_response_emax(css_nm, 
                                      adp_params$ic50_nM, 
                                      adp_params$hill_coefficient, 
                                      adp_params$max_inhibition_percent/100)
    adp_inhibition <- (1 - adp_activity) * 100
    
    # Arachidonic acid (TxA2): Using QSP-derived parameters
    arachidonic_activity <- dose_response_emax(css_nm, 
                                              arachidonic_params$ic50_nM, 
                                              arachidonic_params$hill_coefficient, 
                                              arachidonic_params$max_inhibition_percent/100)
    arachidonic_inhibition <- (1 - arachidonic_activity) * 100
    
    # Thrombin: Using QSP-derived parameters
    thrombin_activity <- dose_response_emax(css_nm, 
                                           thrombin_params$ic50_nM, 
                                           thrombin_params$hill_coefficient, 
                                           thrombin_params$max_inhibition_percent/100)
    thrombin_inhibition <- (1 - thrombin_activity) * 100
    
    # Epinephrine: Use arachidonic acid parameters as proxy (similar TxA2 pathway)
    epinephrine_activity <- dose_response_emax(css_nm, 
                                              arachidonic_params$ic50_nM * 2, 
                                              arachidonic_params$hill_coefficient, 
                                              arachidonic_params$max_inhibition_percent/100 * 0.5)
    epinephrine_inhibition <- (1 - epinephrine_activity) * 100
    
    # Ristocetin (BTK-independent): Using QSP-derived parameters
    ristocetin_activity <- dose_response_emax(css_nm, 
                                             ristocetin_params$ic50_nM, 
                                             ristocetin_params$hill_coefficient, 
                                             ristocetin_params$max_inhibition_percent/100)
    ristocetin_inhibition <- (1 - ristocetin_activity) * 100
    
    # ENHANCED: Individual patient factors affecting response
    
    # Age effect (reduced response in very elderly due to comorbidities)
    age_response_factor <- pmax(1 - (population$age[treated_patients] - 75) * 0.008, 0.6)
    
    # Baseline platelet function
    baseline_factor <- population$baseline_aggregation[treated_patients] / 100
    
    # Disease severity effect
    disease_response_factor <- case_when(
      population$cll_stage[treated_patients] %in% c("III", "IV") ~ 0.85,
      population$high_comorbidity_score[treated_patients] ~ 0.90,
      TRUE ~ 1.0
    )
    
    # Concomitant medication effects
    antiplatelet_factor <- ifelse(population$antiplatelets[treated_patients], 1.4, 1.0)  # Additive
    anticoagulant_factor <- ifelse(population$anticoagulants[treated_patients], 1.15, 1.0)
    
    # Genetic factors
    plcg2_factor <- ifelse(population$plcg2_variants[treated_patients], 0.75, 1.0)
    btk_mutation_factor <- ifelse(population$btk_c481s_mutation[treated_patients], 0.3, 1.0)  # Resistance
    
    # Combined patient factor
    patient_factor <- age_response_factor * baseline_factor * disease_response_factor *
                     antiplatelet_factor * anticoagulant_factor * plcg2_factor * 
                     btk_mutation_factor
    
    # Apply patient factors to inhibition
    collagen_inhibition <- collagen_inhibition * patient_factor
    adp_inhibition <- adp_inhibition * patient_factor
    arachidonic_inhibition <- arachidonic_inhibition * patient_factor
    thrombin_inhibition <- thrombin_inhibition * patient_factor
    epinephrine_inhibition <- epinephrine_inhibition * patient_factor
    ristocetin_inhibition <- ristocetin_inhibition * patient_factor
    
    # Add inter-individual variability (higher in real-world)
    collagen_inhibition <- collagen_inhibition + rnorm(sum(treated_patients), 0, 12)
    adp_inhibition <- adp_inhibition + rnorm(sum(treated_patients), 0, 8)
    arachidonic_inhibition <- arachidonic_inhibition + rnorm(sum(treated_patients), 0, 10)
    thrombin_inhibition <- thrombin_inhibition + rnorm(sum(treated_patients), 0, 7)
    epinephrine_inhibition <- epinephrine_inhibition + rnorm(sum(treated_patients), 0, 6)
    ristocetin_inhibition <- ristocetin_inhibition + rnorm(sum(treated_patients), 0, 3)
    
    # Ensure realistic bounds
    collagen_inhibition <- pmax(pmin(collagen_inhibition, 98), 0)
    adp_inhibition <- pmax(pmin(adp_inhibition, 28), 0)
    arachidonic_inhibition <- pmax(pmin(arachidonic_inhibition, 58), 0)
    thrombin_inhibition <- pmax(pmin(thrombin_inhibition, 45), 0)
    epinephrine_inhibition <- pmax(pmin(epinephrine_inhibition, 25), 0)
    ristocetin_inhibition <- pmax(pmin(ristocetin_inhibition, 5), 0)
    
    # Store results
    results$collagen_inhibition[treated_patients] <- round(collagen_inhibition, 1)
    results$adp_inhibition[treated_patients] <- round(adp_inhibition, 1)
    results$arachidonic_inhibition[treated_patients] <- round(arachidonic_inhibition, 1)
    results$thrombin_inhibition[treated_patients] <- round(thrombin_inhibition, 1)
    results$epinephrine_inhibition[treated_patients] <- round(epinephrine_inhibition, 1)
    results$ristocetin_inhibition[treated_patients] <- round(ristocetin_inhibition, 1)
  }
  
  # ENHANCED: Evidence-based pathway weighting for bleeding risk
  # Based on meta-analysis of bleeding outcomes vs pathway inhibition
  
  # Handle NA values
  results[is.na(results)] <- 0
  
  # Updated pathway weights based on bleeding correlation analysis
  results$bleeding_risk_score <- (
    results$collagen_inhibition * 0.65 +        # Increased weight - primary target
    results$arachidonic_inhibition * 0.18 +     # TxA2 pathway important for hemostasis
    results$thrombin_inhibition * 0.10 +        # Coagulation cascade
    results$adp_inhibition * 0.05 +             # Minimal at therapeutic doses
    results$epinephrine_inhibition * 0.02       # Stress response (minimal)
    # Ristocetin excluded (BTK-independent)
  ) / 100
  
  return(results)
}

# Helper function for dose-response modeling
dose_response_emax <- function(conc, ic50, hill, max_inhib) {
  if (any(conc == 0)) {
    result <- rep(1.0, length(conc))
    result[conc > 0] <- 1 - (max_inhib * conc[conc > 0]^hill) / (ic50^hill + conc[conc > 0]^hill)
    return(result)
  }
  return(1 - (max_inhib * conc^hill) / (ic50^hill + conc^hill))
}

# =============================================================================
# PART 4: ENHANCED SAFETY OUTCOME MODELING WITH CTCAE v5.0
# =============================================================================

# CTCAE v5.0 Bleeding Classification
classify_bleeding_severity_ctcae <- function(bleeding_events, risk_factors) {

  n_events <- length(bleeding_events)
  severity_grades <- rep(0, n_events)

  # CTCAE v5.0 Bleeding Grades:
  # Grade 1: Mild; intervention not indicated
  # Grade 2: Moderate; minimal, local or noninvasive intervention indicated
  # Grade 3: Severe or medically significant; hospitalization or urgent intervention indicated
  # Grade 4: Life-threatening; urgent intervention indicated
  # Grade 5: Death

  for (i in 1:n_events) {
    if (bleeding_events[i] > 0) {
      # Base probability distribution for bleeding severity
      # Adjusted based on patient risk factors

      # Risk factor adjustments
      age_factor <- ifelse(risk_factors$age[i] >= 75, 1.5, 1.0)
      anticoag_factor <- ifelse(risk_factors$anticoagulants[i], 2.0, 1.0)
      comorbidity_factor <- ifelse(risk_factors$high_comorbidity_score[i], 1.3, 1.0)
      platelet_factor <- ifelse(risk_factors$platelet_count[i] < 100, 1.8, 1.0)

      # Combined risk multiplier
      risk_multiplier <- age_factor * anticoag_factor * comorbidity_factor * platelet_factor

      # Adjusted probabilities for each grade
      # Base probabilities: Grade 1: 40%, Grade 2: 35%, Grade 3: 20%, Grade 4: 4.5%, Grade 5: 0.5%
      base_probs <- c(0.40, 0.35, 0.20, 0.045, 0.005)

      # Adjust probabilities based on risk factors
      # Higher risk shifts distribution toward more severe grades
      if (risk_multiplier > 2.0) {
        adjusted_probs <- c(0.25, 0.30, 0.30, 0.13, 0.02)  # High risk
      } else if (risk_multiplier > 1.5) {
        adjusted_probs <- c(0.30, 0.32, 0.25, 0.11, 0.02)  # Moderate-high risk
      } else if (risk_multiplier > 1.2) {
        adjusted_probs <- c(0.35, 0.33, 0.22, 0.08, 0.02)  # Moderate risk
      } else {
        adjusted_probs <- base_probs  # Low risk
      }

      # Sample severity grade
      severity_grades[i] <- sample(1:5, 1, prob = adjusted_probs)
    }
  }

  return(severity_grades)
}

# Enhanced bleeding classification function
classify_bleeding_types <- function(severity_grades, patient_characteristics) {

  n_patients <- length(severity_grades)

  # Initialize bleeding type classifications
  bleeding_types <- data.frame(
    major_bleeding = rep(FALSE, n_patients),
    minor_bleeding = rep(FALSE, n_patients),
    clinically_relevant_non_major = rep(FALSE, n_patients),
    fatal_bleeding = rep(FALSE, n_patients),
    intracranial_hemorrhage = rep(FALSE, n_patients),
    gastrointestinal_bleeding = rep(FALSE, n_patients),
    other_major_bleeding = rep(FALSE, n_patients)
  )

  for (i in 1:n_patients) {
    if (severity_grades[i] > 0) {

      # Classification based on CTCAE grade and clinical criteria
      if (severity_grades[i] >= 3) {
        # Major bleeding (Grade 3-5)
        bleeding_types$major_bleeding[i] <- TRUE

        # Specific major bleeding subtypes
        if (severity_grades[i] == 5) {
          bleeding_types$fatal_bleeding[i] <- TRUE
        }

        # Location-specific probabilities for major bleeding
        # Based on ibrutinib clinical trial data
        location_probs <- c(
          intracranial = 0.08,      # 8% of major bleeds are intracranial
          gastrointestinal = 0.65,  # 65% are GI bleeding
          other = 0.27              # 27% other locations
        )

        bleeding_location <- sample(c("intracranial", "gastrointestinal", "other"),
                                   1, prob = location_probs)

        if (bleeding_location == "intracranial") {
          bleeding_types$intracranial_hemorrhage[i] <- TRUE
        } else if (bleeding_location == "gastrointestinal") {
          bleeding_types$gastrointestinal_bleeding[i] <- TRUE
        } else {
          bleeding_types$other_major_bleeding[i] <- TRUE
        }

      } else if (severity_grades[i] == 2) {
        # Clinically relevant non-major bleeding (Grade 2)
        bleeding_types$clinically_relevant_non_major[i] <- TRUE

      } else if (severity_grades[i] == 1) {
        # Minor bleeding (Grade 1)
        bleeding_types$minor_bleeding[i] <- TRUE
      }
    }
  }

  return(bleeding_types)
}

# =============================================================================
# PART 4B: TEMPORAL BLEEDING EVOLUTION MODEL (ENHANCED)
# =============================================================================

simulate_temporal_bleeding_events <- function(population, bleeding_risk, platelet_results) {

  n_patients <- nrow(population)

  # Initialize outcomes with enhanced safety variables
  outcomes <- data.frame(
    patient_id = population$patient_id,
    treatment_arm = bleeding_risk$treatment_arm,
    annual_bleeding_risk = bleeding_risk$total_risk,
    bleeding_risk_score = platelet_results$bleeding_risk_score
  )
  
  # ENHANCED: Time-dependent bleeding risk from Lipsky study
  # 51% bleed within 6 months, median time 49 days
  
  # Phase-specific hazard rates (per month)
  # Months 0-1: 4× baseline, 1-3: 3.5×, 3-6: 2.5×, 6-12: 1.8×, 12+: 1.5×
  
  time_phases <- data.frame(
    phase = c("0-1", "1-3", "3-6", "6-12", "12+"),
    duration_months = c(1, 2, 3, 6, 12),  # Duration of each phase
    hazard_multiplier = c(4.0, 3.5, 2.5, 1.8, 1.5)
  )
  
  # Convert annual risk to monthly baseline hazard
  monthly_baseline_hazard <- -log(1 - outcomes$annual_bleeding_risk) / 12
  
  # For each patient, simulate bleeding events over time
  max_followup_months <- max(population$treatment_duration_months)
  
  # Initialize enhanced event tracking with CTCAE v5.0 classification
  outcomes$bleeding_events <- 0
  outcomes$major_bleeding_events <- 0
  outcomes$minor_bleeding_events <- 0
  outcomes$clinically_relevant_non_major_events <- 0
  outcomes$time_to_first_bleeding <- NA
  outcomes$time_to_first_major_bleeding <- NA
  outcomes$bleeding_event_occurred <- FALSE
  outcomes$major_bleeding_occurred <- FALSE
  outcomes$ctcae_max_grade <- 0
  outcomes$fatal_bleeding <- FALSE
  outcomes$intracranial_hemorrhage <- FALSE
  outcomes$gastrointestinal_bleeding <- FALSE
  outcomes$other_major_bleeding <- FALSE
  
  for (i in 1:n_patients) {
    patient_followup <- population$treatment_duration_months[i]
    monthly_hazard <- monthly_baseline_hazard[i]
    
    if (!is.na(monthly_hazard) && !is.na(patient_followup) && monthly_hazard > 0 && patient_followup > 0) {
      current_time <- 0
      events <- 0
      first_event_time <- NA
      
      while (current_time < patient_followup) {
        # Determine current phase and hazard multiplier
        if (current_time < 1) {
          multiplier <- 4.0
        } else if (current_time < 3) {
          multiplier <- 3.5
        } else if (current_time < 6) {
          multiplier <- 2.5
        } else if (current_time < 12) {
          multiplier <- 1.8
        } else {
          multiplier <- 1.5
        }
        
        # Current hazard
        current_hazard <- monthly_hazard * multiplier
        
        # Time to next event (exponential distribution)
        time_to_event <- rexp(1, current_hazard)
        
        if (current_time + time_to_event <= patient_followup) {
          events <- events + 1
          if (is.na(first_event_time)) {
            first_event_time <- current_time + time_to_event
          }
          current_time <- current_time + time_to_event
        } else {
          break
        }
      }
      
      outcomes$bleeding_events[i] <- events
      if (!is.na(first_event_time)) {
        outcomes$time_to_first_bleeding[i] <- first_event_time * 30.44  # Convert to days
        outcomes$bleeding_event_occurred[i] <- TRUE
      } else {
        outcomes$time_to_first_bleeding[i] <- patient_followup * 30.44
      }
    } else {
      outcomes$time_to_first_bleeding[i] <- patient_followup * 30.44
    }
  }
  
  # Enhanced bleeding severity classification using CTCAE v5.0
  cat("   Classifying bleeding events using CTCAE v5.0 criteria...\n")

  # Prepare risk factors for classification
  risk_factors <- data.frame(
    age = population$age,
    anticoagulants = population$anticoagulants,
    high_comorbidity_score = population$high_comorbidity_score,
    platelet_count = population$platelet_count
  )

  # Classify severity for each patient's bleeding events
  severity_grades <- classify_bleeding_severity_ctcae(outcomes$bleeding_events, risk_factors)
  outcomes$ctcae_max_grade <- severity_grades

  # Classify bleeding types based on severity
  bleeding_types <- classify_bleeding_types(severity_grades, population)

  # Update outcomes with detailed bleeding classifications
  outcomes$major_bleeding_events <- as.numeric(bleeding_types$major_bleeding)
  outcomes$major_bleeding_occurred <- bleeding_types$major_bleeding
  outcomes$fatal_bleeding <- bleeding_types$fatal_bleeding
  outcomes$intracranial_hemorrhage <- bleeding_types$intracranial_hemorrhage
  outcomes$gastrointestinal_bleeding <- bleeding_types$gastrointestinal_bleeding
  outcomes$other_major_bleeding <- bleeding_types$other_major_bleeding

  # Calculate minor and CRNM bleeding events
  outcomes$clinically_relevant_non_major_events <- as.numeric(bleeding_types$clinically_relevant_non_major)
  outcomes$minor_bleeding_events <- as.numeric(bleeding_types$minor_bleeding)

  # Calculate time to first major bleeding for patients with major bleeding
  for (i in 1:n_patients) {
    if (outcomes$major_bleeding_occurred[i]) {
      # Assume major bleeding occurs at same time as first bleeding for simplicity
      # In reality, this could be modeled separately
      outcomes$time_to_first_major_bleeding[i] <- outcomes$time_to_first_bleeding[i]
    } else {
      outcomes$time_to_first_major_bleeding[i] <- population$treatment_duration_months[i] * 30.44
    }
  }

  cat("   ✓ Enhanced bleeding classification complete\n")
  cat("     - Major bleeding events:", sum(outcomes$major_bleeding_occurred), "\n")
  cat("     - CRNM bleeding events:", sum(outcomes$clinically_relevant_non_major_events), "\n")
  cat("     - Minor bleeding events:", sum(outcomes$minor_bleeding_events), "\n")
  cat("     - Fatal bleeding events:", sum(outcomes$fatal_bleeding), "\n")

  return(outcomes)
}

# =============================================================================
# PART 4C: DOSE MODIFICATION AND SAFETY MONITORING
# =============================================================================

# Implement dose modification logic based on safety events
simulate_dose_modifications <- function(population, bleeding_outcomes, pk_data) {

  n_patients <- nrow(population)

  # Initialize dose modification tracking
  # Handle existing treatment arms in synthetic population
  dose_modifications <- data.frame(
    patient_id = population$patient_id,
    initial_dose = ifelse(population$treatment_arm == "Ibrutinib_420mg", 420, 0),
    current_dose = ifelse(population$treatment_arm == "Ibrutinib_420mg", 420, 0),
    dose_reductions = 0,
    dose_interruptions = 0,
    total_interruption_days = 0,
    permanent_discontinuation = FALSE,
    discontinuation_reason = "None",
    stringsAsFactors = FALSE
  )

  # Dose modification rules based on bleeding severity
  for (i in 1:n_patients) {
    if (population$treatment_arm[i] != "Control") {

      # Major bleeding events trigger dose modifications
      if (bleeding_outcomes$major_bleeding_occurred[i]) {

        # Fatal or intracranial bleeding -> permanent discontinuation
        if (bleeding_outcomes$fatal_bleeding[i] ||
            bleeding_outcomes$intracranial_hemorrhage[i]) {
          dose_modifications$permanent_discontinuation[i] <- TRUE
          dose_modifications$discontinuation_reason[i] <- "Major_bleeding"
          dose_modifications$current_dose[i] <- 0

        } else {
          # Other major bleeding -> dose reduction
          dose_modifications$dose_reductions[i] <- 1

          if (dose_modifications$initial_dose[i] == 420) {
            dose_modifications$current_dose[i] <- 280  # CLL: 420->280mg
          } else if (dose_modifications$initial_dose[i] == 280) {
            dose_modifications$current_dose[i] <- 140  # 280->140mg
          } else {
            # Already at lowest dose -> discontinue
            dose_modifications$permanent_discontinuation[i] <- TRUE
            dose_modifications$discontinuation_reason[i] <- "Major_bleeding"
            dose_modifications$current_dose[i] <- 0
          }
        }
      }

      # CRNM bleeding may trigger dose interruption
      if (bleeding_outcomes$clinically_relevant_non_major_events[i] > 0) {
        # 30% chance of dose interruption for CRNM bleeding
        if (runif(1) < 0.3) {
          dose_modifications$dose_interruptions[i] <- 1
          dose_modifications$total_interruption_days[i] <- sample(7:21, 1)  # 1-3 weeks
        }
      }

      # Multiple bleeding events increase modification probability
      if (bleeding_outcomes$bleeding_events[i] > 2) {
        # Additional dose reduction for recurrent bleeding
        if (!dose_modifications$permanent_discontinuation[i]) {
          dose_modifications$dose_reductions[i] <-
            dose_modifications$dose_reductions[i] + 1

          # Further reduce dose
          current <- dose_modifications$current_dose[i]
          if (current == 420) {
            dose_modifications$current_dose[i] <- 280
          } else if (current == 280) {
            dose_modifications$current_dose[i] <- 140
          } else if (current == 140) {
            dose_modifications$permanent_discontinuation[i] <- TRUE
            dose_modifications$discontinuation_reason[i] <- "Recurrent_bleeding"
            dose_modifications$current_dose[i] <- 0
          }
        }
      }
    }
  }

  return(dose_modifications)
}

# Safety monitoring timepoints function
generate_safety_monitoring_schedule <- function(treatment_duration_months) {

  # Standard safety monitoring schedule for ibrutinib
  # Week 1, 2, 4, then monthly for first 6 months, then every 3 months

  monitoring_timepoints <- list()

  # First month: weekly monitoring
  monitoring_timepoints$week_1 <- 7
  monitoring_timepoints$week_2 <- 14
  monitoring_timepoints$week_4 <- 28

  # Months 2-6: monthly monitoring
  for (month in 2:min(6, treatment_duration_months)) {
    monitoring_timepoints[[paste0("month_", month)]] <- month * 30.44
  }

  # After 6 months: quarterly monitoring
  if (treatment_duration_months > 6) {
    quarters <- seq(9, treatment_duration_months, by = 3)
    for (quarter in quarters) {
      monitoring_timepoints[[paste0("quarter_", quarter)]] <- quarter * 30.44
    }
  }

  return(monitoring_timepoints)
}

# =============================================================================
# PART 5: MAIN ENHANCED VIRTUAL TRIAL FUNCTION
# =============================================================================

run_enhanced_virtual_clinical_trial <- function(n_patients = 5000, save_results = TRUE, use_existing_population = TRUE) {

  cat("=== ENHANCED VIRTUAL SAFETY TRIAL ===\n")
  cat("Based on 14,000+ patient meta-analyses and 2024 bleeding risk data\n")
  cat("Patients:", n_patients, "\n\n")

  # Step 1: Load or generate clinical population
  if (use_existing_population) {
    cat("1. Loading existing synthetic patient population...\n")

    # Try to load existing population data
    results_dir <- get_ml_results_dir()
    population_file <- build_path(results_dir, "synthetic_patient_population.csv")

    if (file.exists(population_file)) {
      population <- read.csv(population_file, stringsAsFactors = FALSE)

      # Verify population has required columns for safety analysis
      required_cols <- c("patient_id", "age", "sex", "treatment_arm", "anticoagulants",
                        "antiplatelets", "cardiovascular_disease", "high_comorbidity_score",
                        "platelet_count", "baseline_aggregation", "treatment_duration_months")

      missing_cols <- setdiff(required_cols, colnames(population))
      if (length(missing_cols) > 0) {
        cat("   Warning: Missing required columns:", paste(missing_cols, collapse = ", "), "\n")
        cat("   Regenerating population with complete safety variables...\n")
        population <- generate_synthetic_population(n_patients, save_file = FALSE)
      } else {
        # Subset to requested number of patients if needed
        if (nrow(population) > n_patients) {
          population <- population[1:n_patients, ]
        } else if (nrow(population) < n_patients) {
          cat("   Warning: Existing population has", nrow(population), "patients, requested", n_patients, "\n")
          cat("   Using available", nrow(population), "patients\n")
          n_patients <- nrow(population)
        }
        cat("   ✓ Loaded existing population with", nrow(population), "patients\n")
      }
    } else {
      cat("   No existing population found. Generating new population...\n")
      population <- generate_synthetic_population(n_patients, save_file = FALSE)
    }
  } else {
    cat("1. Generating new clinical synthetic patient population...\n")
    population <- generate_synthetic_population(n_patients, save_file = FALSE)
  }

  cat("   ✓ Clinical CLL population ready (median age", round(median(population$age), 1), ")\n\n")
  
  # Step 2: Enhanced PK modeling with multiple dosing regimens
  cat("2. Calculating enhanced pharmacokinetics with interactions...\n")

  # Separate by treatment arm (handle existing arms in synthetic population)
  ibr_420_patients <- population$treatment_arm == "Ibrutinib_420mg"
  control_patients <- population$treatment_arm == "Control"
  
  # Initialize PK dataframe
  pk_data <- data.frame(
    cmax_ng_ml = numeric(n_patients),
    auc_ng_h_ml = numeric(n_patients),
    cmax_nm = numeric(n_patients),
    auc_nm_h = numeric(n_patients),
    css_avg_nm = numeric(n_patients),
    adherence_factor = numeric(n_patients),
    exposure_factor = numeric(n_patients)
  )
  
  if (sum(ibr_420_patients) > 0) {
    cat("   Processing", sum(ibr_420_patients),
        "patients on 420mg dose with variability...\n")
    pk_420 <- calculate_clinical_ibrutinib_pk(
      population[ibr_420_patients, ], 420)
    pk_data[ibr_420_patients, ] <- pk_420
  }

  # Note: 560mg dosing can be added when synthetic population includes MCL patients

  if (sum(control_patients) > 0) {
    pk_data[control_patients, "adherence_factor"] <- 1.0
    pk_data[control_patients, "exposure_factor"] <- 0.0
  }
  
  cat("   ✓ Enhanced PK with 50% CV and interaction effects completed\n\n")
  
  # Step 3: Comprehensive bleeding risk assessment
  cat("3. Calculating comprehensive bleeding risk with all interactions...\n")
  bleeding_risk <- calculate_comprehensive_bleeding_risk(population, pk_data, population$treatment_arm)
  cat("   ✓ Multiplicative risk model with 2.54× anticoagulant effect completed\n\n")
  
  # Step 4: Enhanced platelet aggregation modeling
  cat("4. Simulating enhanced platelet responses with pathway weighting...\n")
  platelet_results <- simulate_enhanced_platelet_response(population, pk_data, population$treatment_arm)
  cat("   ✓ Evidence-based pathway inhibition with updated IC50 values completed\n\n")
  
  # Step 5: Temporal bleeding event simulation
  cat("5. Simulating temporal bleeding events with 6-month plateau...\n")
  clinical_outcomes <- simulate_temporal_bleeding_events(population,
                                                        bleeding_risk,
                                                        platelet_results)
  cat("   ✓ Time-dependent bleeding risk with phase-specific hazards completed\n\n")

  # Step 6: Dose modification simulation
  cat("6. Simulating dose modifications based on safety events...\n")
  dose_modifications <- simulate_dose_modifications(population,
                                                   clinical_outcomes,
                                                   pk_data)
  cat("   ✓ Dose modification patterns based on bleeding severity completed\n\n")
  
  # Step 7: Combine all enhanced data
  cat("7. Combining enhanced results...\n")
  
  # Safely combine all dataframes
  trial_data <- population
  
  # Add bleeding risk components
  bleeding_cols <- setdiff(colnames(bleeding_risk), colnames(trial_data))
  if (length(bleeding_cols) > 0) {
    trial_data <- cbind(trial_data, bleeding_risk[, bleeding_cols, drop = FALSE])
  }
  
  # Add platelet results
  platelet_cols <- setdiff(colnames(platelet_results), colnames(trial_data))
  if (length(platelet_cols) > 0) {
    trial_data <- cbind(trial_data, platelet_results[, platelet_cols, drop = FALSE])
  }
  
  # Add clinical outcomes
  outcome_cols <- setdiff(colnames(clinical_outcomes), colnames(trial_data))
  if (length(outcome_cols) > 0) {
    trial_data <- cbind(trial_data, clinical_outcomes[, outcome_cols, drop = FALSE])
  }
  
  # Add PK data
  trial_data <- cbind(trial_data, pk_data)

  # Add dose modification data
  dose_mod_cols <- setdiff(colnames(dose_modifications), colnames(trial_data))
  if (length(dose_mod_cols) > 0) {
    trial_data <- cbind(trial_data, dose_modifications[, dose_mod_cols, drop = FALSE])
  }

  cat("   ✓ Complete enhanced dataset ready:", nrow(trial_data), "patients\n")
  
  if (save_results) {
    # Save enhanced trial data using safe file utilities
    results_dir <- get_ml_results_dir()
    safe_csv_save(trial_data, build_path(results_dir, "enhanced_virtual_clinical_trial_results.csv"))
    cat("\nEnhanced trial results saved to 'enhanced_virtual_clinical_trial_results.csv'\n")
  }
  
  # Comprehensive summary with real-world benchmarks
  cat("\n=== ENHANCED SAFETY TRIAL SUMMARY ===\n")

  # Enhanced bleeding outcomes summary with CTCAE classification
  bleeding_summary <- trial_data %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      any_bleeding_rate = mean(bleeding_event_occurred, na.rm = TRUE) * 100,
      major_bleeding_rate = mean(major_bleeding_occurred, na.rm = TRUE) * 100,
      crnm_bleeding_rate = mean(clinically_relevant_non_major_events > 0, na.rm = TRUE) * 100,
      minor_bleeding_rate = mean(minor_bleeding_events > 0, na.rm = TRUE) * 100,
      fatal_bleeding_rate = mean(fatal_bleeding, na.rm = TRUE) * 100,
      ich_rate = mean(intracranial_hemorrhage, na.rm = TRUE) * 100,
      gi_bleeding_rate = mean(gastrointestinal_bleeding, na.rm = TRUE) * 100,
      mean_ctcae_grade = mean(ctcae_max_grade, na.rm = TRUE),
      mean_bleeding_risk = mean(annual_bleeding_risk, na.rm = TRUE) * 100,
      mean_events_per_patient = mean(bleeding_events, na.rm = TRUE),
      median_time_to_bleeding = median(time_to_first_bleeding[bleeding_event_occurred], na.rm = TRUE),
      median_time_to_major_bleeding = median(time_to_first_major_bleeding[major_bleeding_occurred], na.rm = TRUE),
      .groups = 'drop'
    )
  
  cat("\nBleeding Outcomes (Clinical Model):\n")
  print(bleeding_summary)
  
  # Compare to literature benchmarks
  ibr_bleeding_rate <- bleeding_summary$bleeding_rate[bleeding_summary$treatment_arm == "Ibrutinib_420mg"]
  control_bleeding_rate <- bleeding_summary$bleeding_rate[bleeding_summary$treatment_arm == "Control"]
  
  cat("\nClinical Benchmarks Comparison:\n")
  cat("  Simulated ibrutinib rate:", round(ibr_bleeding_rate, 1), "% (Target: 19%)\n")
  cat("  Simulated control rate:", round(control_bleeding_rate, 1), "% (Target: 2.5%)\n")
  cat("  Risk ratio:", round(ibr_bleeding_rate / control_bleeding_rate, 1), "× (Target: 7.6×)\n")
  
  # Platelet aggregation summary
  platelet_summary <- trial_data %>%
    group_by(treatment_arm) %>%
    summarise(
      collagen_inhibition = mean(collagen_inhibition, na.rm = TRUE),
      adp_inhibition = mean(adp_inhibition, na.rm = TRUE),
      arachidonic_inhibition = mean(arachidonic_inhibition, na.rm = TRUE),
      thrombin_inhibition = mean(thrombin_inhibition, na.rm = TRUE),
      epinephrine_inhibition = mean(epinephrine_inhibition, na.rm = TRUE),
      ristocetin_inhibition = mean(ristocetin_inhibition, na.rm = TRUE),
      .groups = 'drop'
    )
  
  cat("\nPlatelet Aggregation Inhibition (Enhanced Model):\n")
  platelet_summary_rounded <- platelet_summary
  platelet_summary_rounded[, -1] <- round(platelet_summary[, -1], 1)
  print(platelet_summary_rounded)
  
  # Dose modification summary
  dose_mod_summary <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    summarise(
      patients_with_dose_reduction = mean(dose_reductions > 0, na.rm = TRUE) * 100,
      patients_with_interruption = mean(dose_interruptions > 0, na.rm = TRUE) * 100,
      permanent_discontinuation_rate = mean(permanent_discontinuation, na.rm = TRUE) * 100,
      mean_dose_reductions = mean(dose_reductions, na.rm = TRUE),
      mean_interruption_days = mean(total_interruption_days, na.rm = TRUE),
      .groups = 'drop'
    )

  cat("\nDose Modification Summary (Ibrutinib Patients):\n")
  cat("  Patients with dose reduction:", round(dose_mod_summary$patients_with_dose_reduction, 1), "%\n")
  cat("  Patients with dose interruption:", round(dose_mod_summary$patients_with_interruption, 1), "%\n")
  cat("  Permanent discontinuation rate:", round(dose_mod_summary$permanent_discontinuation_rate, 1), "%\n")
  cat("  Mean dose reductions per patient:", round(dose_mod_summary$mean_dose_reductions, 2), "\n")
  cat("  Mean interruption days:", round(dose_mod_summary$mean_interruption_days, 1), "\n")

  # Risk factor analysis by treatment arm
  risk_factor_summary <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    summarise(
      anticoagulant_users = mean(anticoagulants, na.rm = TRUE) * 100,
      elderly_patients = mean(age >= 70, na.rm = TRUE) * 100,
      high_bleeding_risk = mean(annual_bleeding_risk > 0.15, na.rm = TRUE) * 100,
      median_exposure = median(css_avg_nm, na.rm = TRUE),
      adherence_rate = mean(adherence_factor, na.rm = TRUE) * 100,
      .groups = 'drop'
    )
  
  cat("\nRisk Factor Distribution (Ibrutinib Patients):\n")
  cat("  Anticoagulant users:", round(risk_factor_summary$anticoagulant_users, 1), "%\n")
  cat("  Elderly patients (≥70):", round(risk_factor_summary$elderly_patients, 1), "%\n")
  cat("  High bleeding risk (>15%/year):", round(risk_factor_summary$high_bleeding_risk, 1), "%\n")
  cat("  Median steady-state concentration:", round(risk_factor_summary$median_exposure, 0), "nM\n")
  cat("  Mean adherence rate:", round(risk_factor_summary$adherence_rate, 1), "%\n")

  # Safety subgroup analyses
  cat("\n=== SAFETY SUBGROUP ANALYSES ===\n")

  # Age subgroup analysis
  age_subgroups <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    mutate(age_group_safety = case_when(
      age < 65 ~ "Under 65",
      age >= 65 & age < 75 ~ "65-74",
      age >= 75 ~ "75+"
    )) %>%
    group_by(age_group_safety) %>%
    summarise(
      n = n(),
      major_bleeding_rate = mean(major_bleeding_occurred, na.rm = TRUE) * 100,
      any_bleeding_rate = mean(bleeding_event_occurred, na.rm = TRUE) * 100,
      discontinuation_rate = mean(permanent_discontinuation, na.rm = TRUE) * 100,
      .groups = 'drop'
    )

  cat("\nSafety by Age Group:\n")
  print(age_subgroups)

  # Anticoagulant subgroup analysis
  anticoag_subgroups <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    group_by(anticoagulants) %>%
    summarise(
      n = n(),
      major_bleeding_rate = mean(major_bleeding_occurred, na.rm = TRUE) * 100,
      any_bleeding_rate = mean(bleeding_event_occurred, na.rm = TRUE) * 100,
      fatal_bleeding_rate = mean(fatal_bleeding, na.rm = TRUE) * 100,
      .groups = 'drop'
    )

  cat("\nSafety by Anticoagulant Use:\n")
  print(anticoag_subgroups)

  # Comorbidity subgroup analysis
  comorbidity_subgroups <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    group_by(high_comorbidity_score) %>%
    summarise(
      n = n(),
      major_bleeding_rate = mean(major_bleeding_occurred, na.rm = TRUE) * 100,
      any_bleeding_rate = mean(bleeding_event_occurred, na.rm = TRUE) * 100,
      discontinuation_rate = mean(permanent_discontinuation, na.rm = TRUE) * 100,
      .groups = 'drop'
    )

  cat("\nSafety by Comorbidity Score:\n")
  print(comorbidity_subgroups)

  return(trial_data)
}

# =============================================================================
# PART 6: COMPREHENSIVE SAFETY ANALYSIS FUNCTIONS
# =============================================================================

# Generate ICH E3 compliant safety tables
generate_safety_tables <- function(trial_data) {

  cat("Generating ICH E3 compliant safety tables...\n")

  # Table 1: Overall Safety Summary
  safety_overview <- trial_data %>%
    filter(treatment_arm != "Control") %>%
    group_by(treatment_arm) %>%
    summarise(
      N = n(),
      `Any Bleeding (%)` = sprintf("%.1f", mean(bleeding_event_occurred, na.rm = TRUE) * 100),
      `Major Bleeding (%)` = sprintf("%.1f", mean(major_bleeding_occurred, na.rm = TRUE) * 100),
      `CRNM Bleeding (%)` = sprintf("%.1f", mean(clinically_relevant_non_major_events > 0, na.rm = TRUE) * 100),
      `Minor Bleeding (%)` = sprintf("%.1f", mean(minor_bleeding_events > 0, na.rm = TRUE) * 100),
      `Fatal Bleeding (%)` = sprintf("%.1f", mean(fatal_bleeding, na.rm = TRUE) * 100),
      `ICH (%)` = sprintf("%.1f", mean(intracranial_hemorrhage, na.rm = TRUE) * 100),
      `GI Bleeding (%)` = sprintf("%.1f", mean(gastrointestinal_bleeding, na.rm = TRUE) * 100),
      `Dose Reduction (%)` = sprintf("%.1f", mean(dose_reductions > 0, na.rm = TRUE) * 100),
      `Discontinuation (%)` = sprintf("%.1f", mean(permanent_discontinuation, na.rm = TRUE) * 100),
      .groups = 'drop'
    )

  # Table 2: CTCAE Grade Distribution
  ctcae_distribution <- trial_data %>%
    filter(treatment_arm != "Control" & bleeding_event_occurred) %>%
    group_by(treatment_arm) %>%
    summarise(
      N_with_bleeding = n(),
      `Grade 1 (%)` = sprintf("%.1f", mean(ctcae_max_grade == 1, na.rm = TRUE) * 100),
      `Grade 2 (%)` = sprintf("%.1f", mean(ctcae_max_grade == 2, na.rm = TRUE) * 100),
      `Grade 3 (%)` = sprintf("%.1f", mean(ctcae_max_grade == 3, na.rm = TRUE) * 100),
      `Grade 4 (%)` = sprintf("%.1f", mean(ctcae_max_grade == 4, na.rm = TRUE) * 100),
      `Grade 5 (%)` = sprintf("%.1f", mean(ctcae_max_grade == 5, na.rm = TRUE) * 100),
      .groups = 'drop'
    )

  # Table 3: Time-to-Event Summary
  tte_summary <- trial_data %>%
    filter(treatment_arm != "Control") %>%
    group_by(treatment_arm) %>%
    summarise(
      N = n(),
      `Events (n)` = sum(bleeding_event_occurred, na.rm = TRUE),
      `Median TTE (days)` = sprintf("%.1f", median(time_to_first_bleeding[bleeding_event_occurred], na.rm = TRUE)),
      `25th Percentile` = sprintf("%.1f", quantile(time_to_first_bleeding[bleeding_event_occurred], 0.25, na.rm = TRUE)),
      `75th Percentile` = sprintf("%.1f", quantile(time_to_first_bleeding[bleeding_event_occurred], 0.75, na.rm = TRUE)),
      `Major Events (n)` = sum(major_bleeding_occurred, na.rm = TRUE),
      `Median TTE Major (days)` = sprintf("%.1f", median(time_to_first_major_bleeding[major_bleeding_occurred], na.rm = TRUE)),
      .groups = 'drop'
    )

  # Save tables
  results_dir <- get_ml_results_dir()
  safe_csv_save(safety_overview, build_path(results_dir, "safety_overview_table.csv"))
  safe_csv_save(ctcae_distribution, build_path(results_dir, "ctcae_distribution_table.csv"))
  safe_csv_save(tte_summary, build_path(results_dir, "time_to_event_summary_table.csv"))

  cat("✓ Safety tables saved\n")

  return(list(
    safety_overview = safety_overview,
    ctcae_distribution = ctcae_distribution,
    tte_summary = tte_summary
  ))
}

# Generate Kaplan-Meier survival curves for bleeding events
generate_kaplan_meier_plots <- function(trial_data) {

  cat("Generating Kaplan-Meier survival curves...\n")

  # Prepare survival data
  surv_data <- trial_data %>%
    filter(treatment_arm != "Control") %>%
    mutate(
      time_days = time_to_first_bleeding,
      event = as.numeric(bleeding_event_occurred),
      major_event = as.numeric(major_bleeding_occurred),
      time_major_days = time_to_first_major_bleeding
    )

  # Kaplan-Meier for any bleeding
  km_any_bleeding <- survfit(Surv(time_days, event) ~ treatment_arm, data = surv_data)

  p1 <- ggsurvplot(
    km_any_bleeding,
    data = surv_data,
    title = "Time to First Bleeding Event",
    xlab = "Time (days)",
    ylab = "Bleeding-free survival probability",
    risk.table = TRUE,
    conf.int = TRUE,
    pval = TRUE,
    legend.title = "Treatment",
    palette = c("#E7B800", "#2E9FDF")
  )

  # Kaplan-Meier for major bleeding
  km_major_bleeding <- survfit(Surv(time_major_days, major_event) ~ treatment_arm, data = surv_data)

  p2 <- ggsurvplot(
    km_major_bleeding,
    data = surv_data,
    title = "Time to First Major Bleeding Event",
    xlab = "Time (days)",
    ylab = "Major bleeding-free survival probability",
    risk.table = TRUE,
    conf.int = TRUE,
    pval = TRUE,
    legend.title = "Treatment",
    palette = c("#E7B800", "#2E9FDF")
  )

  # Save plots
  results_dir <- get_ml_results_dir()
  ggsave(build_path(results_dir, "kaplan_meier_any_bleeding.png"),
         p1$plot, width = 12, height = 8)
  ggsave(build_path(results_dir, "kaplan_meier_major_bleeding.png"),
         p2$plot, width = 12, height = 8)

  cat("✓ Kaplan-Meier plots saved\n")

  return(list(any_bleeding = p1, major_bleeding = p2))
}

# =============================================================================
# PART 7: ENHANCED TRIAL VISUALIZATION FUNCTIONS
# =============================================================================

# Generate forest plots for safety subgroup analyses
generate_forest_plots <- function(trial_data) {

  cat("Generating forest plots for subgroup analyses...\n")

  # Prepare data for forest plot
  subgroup_data <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    mutate(
      age_subgroup = case_when(
        age < 65 ~ "Age <65",
        age >= 65 & age < 75 ~ "Age 65-74",
        age >= 75 ~ "Age ≥75"
      ),
      anticoag_subgroup = ifelse(anticoagulants, "Anticoagulant use", "No anticoagulant"),
      comorbidity_subgroup = ifelse(high_comorbidity_score, "High comorbidity", "Low comorbidity"),
      sex_subgroup = sex
    )

  # Calculate risk ratios for each subgroup
  forest_data <- data.frame(
    Category = character(0),
    Subgroup = character(0),
    N = numeric(0),
    Events = numeric(0),
    Rate = numeric(0),
    Lower_CI = numeric(0),
    Upper_CI = numeric(0),
    stringsAsFactors = FALSE
  )

  subgroups <- list(
    "Age" = c("Age <65", "Age 65-74", "Age ≥75"),
    "Sex" = c("Male", "Female"),
    "Anticoagulant" = c("Anticoagulant use", "No anticoagulant"),
    "Comorbidity" = c("High comorbidity", "Low comorbidity")
  )

  for (category in names(subgroups)) {
    for (subgroup in subgroups[[category]]) {

      if (category == "Age") {
        subset_data <- subgroup_data[subgroup_data$age_subgroup == subgroup, ]
      } else if (category == "Sex") {
        subset_data <- subgroup_data[subgroup_data$sex_subgroup == subgroup, ]
      } else if (category == "Anticoagulant") {
        subset_data <- subgroup_data[subgroup_data$anticoag_subgroup == subgroup, ]
      } else if (category == "Comorbidity") {
        subset_data <- subgroup_data[subgroup_data$comorbidity_subgroup == subgroup, ]
      }

      if (nrow(subset_data) > 10) {  # Minimum sample size
        bleeding_rate <- mean(subset_data$major_bleeding_occurred, na.rm = TRUE)
        n_patients <- nrow(subset_data)
        n_events <- sum(subset_data$major_bleeding_occurred, na.rm = TRUE)

        # Calculate 95% CI for proportion
        ci <- binom.test(n_events, n_patients)$conf.int

        forest_data <- rbind(forest_data, data.frame(
          Category = category,
          Subgroup = subgroup,
          N = n_patients,
          Events = n_events,
          Rate = bleeding_rate * 100,
          Lower_CI = ci[1] * 100,
          Upper_CI = ci[2] * 100
        ))
      }
    }
  }

  # Create forest plot
  p_forest <- ggplot(forest_data, aes(x = Rate, y = reorder(paste(Category, Subgroup, sep = ": "), Rate))) +
    geom_point(size = 3, color = "blue") +
    geom_errorbarh(aes(xmin = Lower_CI, xmax = Upper_CI), height = 0.2) +
    geom_text(aes(label = paste0(Events, "/", N, " (", sprintf("%.1f", Rate), "%)")),
              hjust = -0.1, size = 3) +
    labs(
      title = "Major Bleeding Rates by Subgroup",
      subtitle = "Forest plot with 95% confidence intervals",
      x = "Major Bleeding Rate (%)",
      y = "Subgroup"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5),
      plot.subtitle = element_text(hjust = 0.5)
    )

  # Save forest plot
  results_dir <- get_ml_results_dir()
  ggsave(build_path(results_dir, "safety_forest_plot.png"), p_forest, width = 12, height = 8)

  cat("✓ Forest plot saved\n")

  return(p_forest)
}

plot_enhanced_trial_results <- function(trial_data) {
  
  # Enhanced bleeding risk distribution with risk factor stratification
  p1 <- trial_data %>%
    mutate(risk_group = case_when(
      annual_bleeding_risk < 0.05 ~ "Low (<5%)",
      annual_bleeding_risk < 0.15 ~ "Moderate (5-15%)",
      annual_bleeding_risk < 0.30 ~ "High (15-30%)",
      TRUE ~ "Very High (>30%)"
    )) %>%
    ggplot(aes(x = treatment_arm, fill = risk_group)) +
    geom_bar(position = "fill") +
    labs(title = "Annual Bleeding Risk Distribution",
         subtitle = "Clinical risk stratification with multiplicative interactions",
         x = "Treatment Arm", y = "Proportion", fill = "Annual Bleeding Risk") +
    scale_fill_brewer(palette = "Reds", direction = 1) +
    theme_minimal()
  
  # Temporal bleeding pattern analysis
  bleeding_events <- trial_data %>%
    filter(bleeding_event_occurred) %>%
    mutate(time_months = time_to_first_bleeding / 30.44,
           time_phase = case_when(
             time_months < 1 ~ "0-1 month",
             time_months < 3 ~ "1-3 months", 
             time_months < 6 ~ "3-6 months",
             time_months < 12 ~ "6-12 months",
             TRUE ~ "12+ months"
           ))
  
  p2 <- bleeding_events %>%
    ggplot(aes(x = time_phase, fill = treatment_arm)) +
    geom_bar(position = "dodge") +
    labs(title = "Temporal Distribution of First Bleeding Events",
         subtitle = "Showing 6-month plateau pattern from literature",
         x = "Time Phase", y = "Number of Events", fill = "Treatment") +
    theme_minimal() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  # Anticoagulant interaction effects
  anticoag_analysis <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    mutate(anticoag_status = case_when(
      !anticoagulants ~ "No anticoagulants",
      anticoag_type == "Warfarin" ~ "Warfarin",
      doac_type == "Apixaban" ~ "Apixaban", 
      doac_type == "Rivaroxaban" ~ "Rivaroxaban",
      anticoag_type == "DOAC" ~ "Other DOAC",
      TRUE ~ "Other anticoagulant"
    ))
  
  p3 <- anticoag_analysis %>%
    ggplot(aes(x = anticoag_status, y = annual_bleeding_risk * 100)) +
    geom_boxplot(aes(fill = anticoag_status)) +
    geom_hline(yintercept = 2.5, linetype = "dashed", color = "red") +
    annotate("text", x = 1, y = 5, label = "Control rate (2.5%)", color = "red") +
    labs(title = "Bleeding Risk by Anticoagulant Type",
         subtitle = "Showing 2.54× overall increase with drug-specific variations",
         x = "Anticoagulant Status", y = "Annual Bleeding Risk (%)", 
         fill = "Anticoagulant") +
    theme_minimal() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "none")
  
  # Age-dependent bleeding risk
  p4 <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    ggplot(aes(x = age, y = annual_bleeding_risk * 100)) +
    geom_point(alpha = 0.6, color = "steelblue") +
    geom_smooth(method = "gam", se = TRUE, color = "red") +
    geom_vline(xintercept = 70, linetype = "dashed", color = "orange") +
    annotate("text", x = 72, y = max(trial_data$annual_bleeding_risk * 100) * 0.9,
             label = "Age 70\nHR 7.49", color = "orange") +
    labs(title = "Age-Dependent Bleeding Risk", 
         subtitle = "Exponential increase with age (HR 7.49 for ≥70 years)",
         x = "Age (years)", y = "Annual Bleeding Risk (%)") +
    theme_minimal()
  
  # Generate comprehensive safety analyses
  cat("Generating comprehensive safety analyses...\n")
  safety_tables <- generate_safety_tables(trial_data)
  km_plots <- generate_kaplan_meier_plots(trial_data)
  forest_plot <- generate_forest_plots(trial_data)

  # Save enhanced plots using safe file utilities
  results_dir <- get_ml_results_dir()
  ggsave(build_path(results_dir, "enhanced_bleeding_risk_distribution.png"), p1, width = 12, height = 8)
  ggsave(build_path(results_dir, "enhanced_temporal_bleeding_pattern.png"), p2, width = 12, height = 8)
  ggsave(build_path(results_dir, "enhanced_anticoagulant_interactions.png"), p3, width = 12, height = 8)
  ggsave(build_path(results_dir, "enhanced_age_bleeding_relationship.png"), p4, width = 12, height = 8)

  cat("Enhanced trial result plots and safety analyses saved.\n")

  return(list(
    plots = list(p1, p2, p3, p4),
    safety_tables = safety_tables,
    km_plots = km_plots,
    forest_plot = forest_plot
  ))
}

cat("Enhanced virtual safety trial simulator loaded successfully!\n")
cat("Use run_enhanced_virtual_clinical_trial(n_patients) to run comprehensive simulation.\n")
cat("Incorporates ALL evidence: 2.54× anticoagulant risk, 7.49× age hazard, 6-month plateau.\n")
cat("Features multiplicative interactions, temporal evolution, and pathway weighting.\n")