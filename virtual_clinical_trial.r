# =============================================================================
# EVIDENCE-BASED VIRTUAL SAFETY TRIAL SIMULATOR FOR IBRUTINIB
# =============================================================================
# Updated with comprehensive bleeding risk parameters from 14,000+ patient meta-analyses
# Incorporates multiplicative risk interactions, temporal evolution, and pathway weighting

# Load file utilities first
if (file.exists("ml_file_utils.r")) {
  source("ml_file_utils.r")
} else {
  cat("Warning: ml_file_utils.r not found. Using basic file operations.\n")
}

library(dplyr)
library(ggplot2)
library(survival)
library(survminer)
library(gridExtra)
library(splines)
library(mgcv)
set.seed(123)

# Source the updated population generator and enhanced platelet model
source("synthetic_population_generator.r")
source("ibrutinib_comprehensive_model_with_interactions.r")

# Load QSP-derived parameters for bleeding risk calculations
if (file.exists("qsp_derived_parameters.csv")) {
  qsp_params <- read.csv("qsp_derived_parameters.csv", stringsAsFactors = FALSE)
  cat("✓ Using QSP-derived parameters for bleeding risk calculations\n")
} else {
  cat("Warning: QSP parameters file not found. Using fallback literature values.\n")
  # Fallback to literature values if QSP parameters not available
  qsp_params <- data.frame(
    agonist = c("collagen", "adp", "thrombin", "arachidonic_acid", "ristocetin"),
    ic50_nM = c(150, 1800, 2200, 500, 5000),
    max_inhibition_percent = c(70, 6, 10, 55, 5),
    hill_coefficient = c(1.0, 1.0, 1.0, 1.0, 1.0)
  )
}

# =============================================================================
# PART 1: ENHANCED PHARMACOKINETIC MODEL WITH CLINICAL VARIABILITY
# =============================================================================

calculate_clinical_ibrutinib_pk <- function(population, dose_mg) {
  
  # UPDATED: Clinical PK parameters with increased variability
  # Base parameters for 420mg dose with 40% CV
  base_cmax <- ifelse(dose_mg == 420, 150, ifelse(dose_mg == 280, 105, ifelse(dose_mg == 140, 55, 0)))  # ng/mL
  base_auc <- ifelse(dose_mg == 420, 953, ifelse(dose_mg == 280, 667, ifelse(dose_mg == 140, 333, 0)))  # ng*h/mL
  
  n_patients <- nrow(population)
  
  # ENHANCED: Individual variability factors with interaction effects
  
  # Age effect (exponential increase in exposure for elderly)
  # Hazard ratio 7.49 for age >70 translates to exposure changes
  age_factor <- ifelse(population$age >= 70, 1.25, 
                      ifelse(population$age >= 65, 1.15, 1.0)) * 
                (1 + (population$age - 65) * 0.008)
  
  # Weight effect (allometric scaling with BMI interaction)
  weight_factor <- (population$weight / 70)^0.75
  bmi_factor <- ifelse(population$bmi > 30, 1.1, ifelse(population$bmi < 20, 0.9, 1.0))
  
  # Kidney function effect (enhanced for elderly CLL patients)
  renal_factor <- pmax(population$creatinine_clearance / 90, 0.4)
  
  # Liver function effect (disease and age related)
  liver_factor <- pmax(1 - (population$alt - 25) * 0.008, 0.25)
  
  # UPDATED: Genetic metabolizer effect with population-specific distributions
  genetic_factor <- case_when(
    population$metabolizer_phenotype == "Poor" ~ 2.8,      # Increased variability
    population$metabolizer_phenotype == "Intermediate" ~ 1.6,
    population$metabolizer_phenotype == "Normal" ~ 1.0,
    population$metabolizer_phenotype == "Ultrarapid" ~ 0.55
  )
  
  # ENHANCED: Drug interaction effects with dose-dependent relationships
  # CYP3A4 inhibitors: stronger effect at higher doses
  cyp3a4_inhibitor_factor <- ifelse(population$cyp3a4_inhibitors, 
                                   1.8 + (dose_mg/420) * 0.4, 1.0)
  
  # CYP3A4 inducers: more pronounced reduction
  cyp3a4_inducer_factor <- ifelse(population$cyp3a4_inducers, 
                                 0.45 - (dose_mg/420) * 0.1, 1.0)
  
  # PPI interaction (reduced absorption)
  ppi_factor <- ifelse(population$ppi_use, 0.75, 1.0)
  
  # Sex effect (higher exposure in females, especially elderly)
  sex_factor <- ifelse(population$sex == "Female", 
                      1.25 + (population$age - 65) * 0.005, 1.0)
  
  # Disease severity effect (advanced CLL affects metabolism)
  disease_factor <- case_when(
    population$cll_stage %in% c("III", "IV") ~ 1.15,
    population$high_comorbidity_score ~ 1.12,
    TRUE ~ 1.0
  )
  
  # Calculate individual exposure with all factors
  exposure_factor <- age_factor * genetic_factor * cyp3a4_inhibitor_factor * 
                    cyp3a4_inducer_factor * ppi_factor * sex_factor * 
                    disease_factor / (weight_factor * bmi_factor) * 
                    renal_factor * liver_factor
  
  individual_cmax <- base_cmax * exposure_factor
  individual_auc <- base_auc * exposure_factor
  
  # ENHANCED: Random variability with correlated PK parameters
  # Higher CV in clinical practice (50% vs 40% in trials)
  cv_cmax <- 0.50
  cv_auc <- 0.45
  
  # Correlated random effects (r=0.8 between Cmax and AUC)
  sigma_matrix <- matrix(c(cv_cmax^2, cv_cmax*cv_auc*0.8, 
                          cv_cmax*cv_auc*0.8, cv_auc^2), 2, 2)
  random_effects <- exp(MASS::mvrnorm(n_patients, c(0, 0), sigma_matrix))
  
  individual_cmax <- individual_cmax * random_effects[, 1]
  individual_auc <- individual_auc * random_effects[, 2]
  
  # Convert to nM for BTK inhibition calculations (MW = 440.5 g/mol)
  cmax_nm <- individual_cmax * 1000 / 440.5
  auc_nm_h <- individual_auc * 1000 / 440.5
  
  # Calculate steady-state concentrations for multiple dosing
  # Clinical patients often have adherence issues
  adherence_factor <- rbeta(n_patients, 9, 1.5)  # Mean ~85% adherence
  css_avg_nm <- cmax_nm * 0.4 * adherence_factor  # Average steady-state
  
  return(data.frame(
    cmax_ng_ml = round(individual_cmax, 1),
    auc_ng_h_ml = round(individual_auc, 1),
    cmax_nm = round(cmax_nm, 1),
    auc_nm_h = round(auc_nm_h, 1),
    css_avg_nm = round(css_avg_nm, 1),
    adherence_factor = round(adherence_factor, 3),
    exposure_factor = round(exposure_factor, 2)
  ))
}

# =============================================================================
# PART 2: COMPREHENSIVE BLEEDING RISK MODEL WITH ALL INTERACTIONS
# =============================================================================

calculate_comprehensive_bleeding_risk <- function(population, pk_data, treatment_arm) {
  
  n_patients <- nrow(population)
  
  # Initialize bleeding risk components
  risk_components <- data.frame(
    patient_id = population$patient_id,
    treatment_arm = treatment_arm,
    baseline_risk = numeric(n_patients),
    ibrutinib_risk = numeric(n_patients),
    interaction_risk = numeric(n_patients),
    temporal_risk = numeric(n_patients),
    total_risk = numeric(n_patients)
  )
  
  # UPDATED: Baseline bleeding risk from meta-analysis
  # Clinical control rate: 2.5% per year
  baseline_annual_risk <- 0.025
  
  # Age-dependent baseline risk (exponential relationship)
  # HR 7.49 for age ≥70 from SEER-Medicare analysis
  age_hr <- ifelse(population$age >= 75, 7.49,
                  ifelse(population$age >= 70, 4.2,
                        ifelse(population$age >= 65, 2.1, 1.0)))
  
  risk_components$baseline_risk <- baseline_annual_risk * age_hr
  
  # For control patients (no ibrutinib effect)
  control_patients <- treatment_arm == "Control"
  
  if (any(control_patients)) {
    # Control patients have baseline risk plus comorbidity effects
    comorbidity_multiplier <- 1 + 
      as.numeric(population$cardiovascular_disease) * 0.4 +
      as.numeric(population$high_comorbidity_score) * 0.3 +
      as.numeric(population$diabetes) * 0.2 +
      as.numeric(population$anticoagulants) * 0.8  # Still some anticoagulant risk
    
    risk_components$total_risk[control_patients] <- 
      risk_components$baseline_risk[control_patients] * comorbidity_multiplier[control_patients]
  }
  
  # For ibrutinib-treated patients - COMPREHENSIVE RISK MODEL
  treated_patients <- !control_patients
  
  if (any(treated_patients)) {
    
    # 1. IBRUTINIB DIRECT EFFECT
    # Base ibrutinib bleeding risk multiplier from meta-analysis
    # Clinical practice: 19% vs control 2.5% = 7.6× multiplier
    base_ibrutinib_multiplier <- 7.6
    
    # Concentration-dependent effect using average steady-state
    css_nm <- pk_data$css_avg_nm[treated_patients]
    
    # Dose-response relationship: higher exposure = higher bleeding risk
    # Plateau at high concentrations
    concentration_effect <- 1 + (css_nm / (css_nm + 50)) * 1.5  # Emax model
    
    ibrutinib_direct_risk <- baseline_annual_risk * base_ibrutinib_multiplier * concentration_effect
    risk_components$ibrutinib_risk[treated_patients] <- ibrutinib_direct_risk
    
    # 2. MULTIPLICATIVE DRUG INTERACTIONS
    
    # Anticoagulant interactions (MAJOR RISK FACTOR)
    # OR 2.54 from nested case-control study (614 cases, 2,407 controls)
    anticoag_multiplier <- case_when(
      population$anticoag_type[treated_patients] == "Warfarin" ~ 1.99,
      population$doac_type[treated_patients] == "Rivaroxaban" ~ 2.8,  # Higher risk DOAC
      population$doac_type[treated_patients] == "Apixaban" ~ 2.0,     # Lower risk DOAC
      population$anticoag_type[treated_patients] == "DOAC" ~ 2.48,    # Other DOACs
      population$anticoag_type[treated_patients] == "Other" ~ 3.40,   # Parenteral anticoagulants
      TRUE ~ 1.0  # No anticoagulants
    )
    
    # Combined anticoagulant + antiplatelet: HR 19.2
    combined_anticoag_antiplatelet <- as.numeric(population$anticoagulants[treated_patients] & 
                                                 population$antiplatelets[treated_patients])
    anticoag_multiplier[combined_anticoag_antiplatelet == 1] <- 19.2
    
    # Antiplatelet effect (when used alone)
    antiplatelet_multiplier <- ifelse(population$antiplatelets[treated_patients] & 
                                     !population$anticoagulants[treated_patients], 1.8, 1.0)
    
    # 3. PLATELET DYSFUNCTION MARKERS (MULTIPLICATIVE)
    # von Willebrand factor ≤100 IU/dL: HR 2.73
    vwf_multiplier <- ifelse(population$vwf_low[treated_patients], 2.73, 1.0)
    
    # Factor VIII ≤174 IU/dL: HR 3.73
    factor_viii_multiplier <- ifelse(population$factor_viii_low[treated_patients], 3.73, 1.0)
    
    # Epinephrine closure time ≥240 sec: HR 2.74
    epi_closure_multiplier <- ifelse(population$epi_closure_prolonged[treated_patients], 2.74, 1.0)
    
    # Platelet count effect (continuous relationship)
    # OR 0.9 per 10×10⁹/L decrease
    platelet_multiplier <- (population$platelet_count[treated_patients] / 150)^(-0.1)
    
    # 4. COMORBIDITY INTERACTIONS
    
    # Anemia effect: OR 2.34
    # Estimated from hemoglobin levels (age and disease related)
    anemia_prob <- pmin(0.1 + (population$age[treated_patients] - 60) * 0.01 + 
                       as.numeric(population$cll_stage[treated_patients] %in% c("III", "IV")) * 0.2, 0.6)
    anemia_present <- rbinom(sum(treated_patients), 1, anemia_prob)
    anemia_multiplier <- ifelse(anemia_present == 1, 2.34, 1.0)
    
    # Cardiovascular disease interaction
    cvd_multiplier <- ifelse(population$cardiovascular_disease[treated_patients], 1.6, 1.0)
    
    # High comorbidity score interaction
    comorbidity_multiplier <- ifelse(population$high_comorbidity_score[treated_patients], 1.4, 1.0)
    
    # 5. GENETIC INTERACTIONS
    
    # CYP3A4/CYP3A5 polymorphisms affect bleeding through exposure
    metabolizer_effect <- case_when(
      population$metabolizer_phenotype[treated_patients] == "Poor" ~ 1.8,
      population$metabolizer_phenotype[treated_patients] == "Intermediate" ~ 1.3,
      population$metabolizer_phenotype[treated_patients] == "Normal" ~ 1.0,
      population$metabolizer_phenotype[treated_patients] == "Ultrarapid" ~ 0.7
    )
    
    # PLCG2 variants (affect platelet signaling)
    plcg2_multiplier <- ifelse(population$plcg2_variants[treated_patients], 0.8, 1.0)  # Protective
    
    # 6. CALCULATE MULTIPLICATIVE INTERACTION RISK
    interaction_multiplier <- anticoag_multiplier * antiplatelet_multiplier * 
                             vwf_multiplier * factor_viii_multiplier * 
                             epi_closure_multiplier * platelet_multiplier *
                             anemia_multiplier * cvd_multiplier * 
                             comorbidity_multiplier * metabolizer_effect * 
                             plcg2_multiplier
    
    # Cap maximum interaction effect at 50× to prevent unrealistic values
    interaction_multiplier <- pmin(interaction_multiplier, 50)
    
    risk_components$interaction_risk[treated_patients] <- 
      ibrutinib_direct_risk * (interaction_multiplier - 1)
    
    # 7. TEMPORAL RISK EVOLUTION
    # Phase-specific multipliers from Lipsky study
    # Months 0-1: 4×, 1-3: 3.5×, 3-6: 2.5×, 6-12: 1.8×, 12+: 1.5×
    
    # Assume patients are at steady state (after initial months)
    # Use 6-12 month multiplier for primary analysis
    temporal_multiplier <- 1.8
    
    base_treated_risk <- risk_components$baseline_risk[treated_patients] +
                        risk_components$ibrutinib_risk[treated_patients] +
                        risk_components$interaction_risk[treated_patients]
    
    risk_components$temporal_risk[treated_patients] <- 
      base_treated_risk * (temporal_multiplier - 1)
    
    # 8. CALCULATE TOTAL RISK
    risk_components$total_risk[treated_patients] <- 
      risk_components$baseline_risk[treated_patients] +
      risk_components$ibrutinib_risk[treated_patients] +
      risk_components$interaction_risk[treated_patients] +
      risk_components$temporal_risk[treated_patients]
  }
  
  # Ensure risks are reasonable (max 95% annual risk)
  risk_components$total_risk <- pmin(risk_components$total_risk, 0.95)
  
  return(risk_components)
}

# =============================================================================
# PART 3: ENHANCED PLATELET AGGREGATION RESPONSE WITH PATHWAY WEIGHTING
# =============================================================================

simulate_enhanced_platelet_response <- function(population, pk_data, treatment_arm) {
  
  n_patients <- nrow(population)
  
  # Initialize results with enhanced pathway modeling
  results <- data.frame(
    patient_id = population$patient_id,
    treatment_arm = treatment_arm,
    baseline_aggregation = population$baseline_aggregation,
    collagen_inhibition = numeric(n_patients),
    adp_inhibition = numeric(n_patients),
    arachidonic_inhibition = numeric(n_patients),
    thrombin_inhibition = numeric(n_patients),
    epinephrine_inhibition = numeric(n_patients),
    ristocetin_inhibition = numeric(n_patients)
  )
  
  # For control patients
  control_patients <- treatment_arm == "Control"
  
  if (any(control_patients)) {
    # Control patients: small variability around baseline
    results$collagen_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 3)
    results$adp_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 2)
    results$arachidonic_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 2.5)
    results$thrombin_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 2)
    results$epinephrine_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 1.5)
    results$ristocetin_inhibition[control_patients] <- rnorm(sum(control_patients), 0, 1)
  }
  
  # For ibrutinib-treated patients - ENHANCED PATHWAY MODELING
  treated_patients <- !control_patients
  
  if (any(treated_patients)) {
    
    # Get steady-state concentrations
    css_nm <- pk_data$css_avg_nm[treated_patients]
    
    # UPDATED: Evidence-based IC50 values and maximum inhibition
    # From comprehensive QSP model and literature validation
    
    # Extract QSP-derived parameters for each agonist
    collagen_params <- qsp_params[qsp_params$agonist == "collagen", ]
    adp_params <- qsp_params[qsp_params$agonist == "ADP", ]
    arachidonic_params <- qsp_params[qsp_params$agonist == "arachidonic_acid", ]
    thrombin_params <- qsp_params[qsp_params$agonist == "thrombin", ]
    ristocetin_params <- qsp_params[qsp_params$agonist == "ristocetin", ]
    
    # Use QSP-derived parameters for dose-response calculations
    # Primary BTK-dependent pathway: Collagen (GPVI)
    collagen_activity <- dose_response_emax(css_nm, 
                                           collagen_params$ic50_nM, 
                                           collagen_params$hill_coefficient, 
                                           collagen_params$max_inhibition_percent/100)
    collagen_inhibition <- (1 - collagen_activity) * 100
    
    # Secondary pathways with reduced BTK dependence
    # ADP: Using QSP-derived parameters
    adp_activity <- dose_response_emax(css_nm, 
                                      adp_params$ic50_nM, 
                                      adp_params$hill_coefficient, 
                                      adp_params$max_inhibition_percent/100)
    adp_inhibition <- (1 - adp_activity) * 100
    
    # Arachidonic acid (TxA2): Using QSP-derived parameters
    arachidonic_activity <- dose_response_emax(css_nm, 
                                              arachidonic_params$ic50_nM, 
                                              arachidonic_params$hill_coefficient, 
                                              arachidonic_params$max_inhibition_percent/100)
    arachidonic_inhibition <- (1 - arachidonic_activity) * 100
    
    # Thrombin: Using QSP-derived parameters
    thrombin_activity <- dose_response_emax(css_nm, 
                                           thrombin_params$ic50_nM, 
                                           thrombin_params$hill_coefficient, 
                                           thrombin_params$max_inhibition_percent/100)
    thrombin_inhibition <- (1 - thrombin_activity) * 100
    
    # Epinephrine: Use arachidonic acid parameters as proxy (similar TxA2 pathway)
    epinephrine_activity <- dose_response_emax(css_nm, 
                                              arachidonic_params$ic50_nM * 2, 
                                              arachidonic_params$hill_coefficient, 
                                              arachidonic_params$max_inhibition_percent/100 * 0.5)
    epinephrine_inhibition <- (1 - epinephrine_activity) * 100
    
    # Ristocetin (BTK-independent): Using QSP-derived parameters
    ristocetin_activity <- dose_response_emax(css_nm, 
                                             ristocetin_params$ic50_nM, 
                                             ristocetin_params$hill_coefficient, 
                                             ristocetin_params$max_inhibition_percent/100)
    ristocetin_inhibition <- (1 - ristocetin_activity) * 100
    
    # ENHANCED: Individual patient factors affecting response
    
    # Age effect (reduced response in very elderly due to comorbidities)
    age_response_factor <- pmax(1 - (population$age[treated_patients] - 75) * 0.008, 0.6)
    
    # Baseline platelet function
    baseline_factor <- population$baseline_aggregation[treated_patients] / 100
    
    # Disease severity effect
    disease_response_factor <- case_when(
      population$cll_stage[treated_patients] %in% c("III", "IV") ~ 0.85,
      population$high_comorbidity_score[treated_patients] ~ 0.90,
      TRUE ~ 1.0
    )
    
    # Concomitant medication effects
    antiplatelet_factor <- ifelse(population$antiplatelets[treated_patients], 1.4, 1.0)  # Additive
    anticoagulant_factor <- ifelse(population$anticoagulants[treated_patients], 1.15, 1.0)
    
    # Genetic factors
    plcg2_factor <- ifelse(population$plcg2_variants[treated_patients], 0.75, 1.0)
    btk_mutation_factor <- ifelse(population$btk_c481s_mutation[treated_patients], 0.3, 1.0)  # Resistance
    
    # Combined patient factor
    patient_factor <- age_response_factor * baseline_factor * disease_response_factor *
                     antiplatelet_factor * anticoagulant_factor * plcg2_factor * 
                     btk_mutation_factor
    
    # Apply patient factors to inhibition
    collagen_inhibition <- collagen_inhibition * patient_factor
    adp_inhibition <- adp_inhibition * patient_factor
    arachidonic_inhibition <- arachidonic_inhibition * patient_factor
    thrombin_inhibition <- thrombin_inhibition * patient_factor
    epinephrine_inhibition <- epinephrine_inhibition * patient_factor
    ristocetin_inhibition <- ristocetin_inhibition * patient_factor
    
    # Add inter-individual variability (higher in real-world)
    collagen_inhibition <- collagen_inhibition + rnorm(sum(treated_patients), 0, 12)
    adp_inhibition <- adp_inhibition + rnorm(sum(treated_patients), 0, 8)
    arachidonic_inhibition <- arachidonic_inhibition + rnorm(sum(treated_patients), 0, 10)
    thrombin_inhibition <- thrombin_inhibition + rnorm(sum(treated_patients), 0, 7)
    epinephrine_inhibition <- epinephrine_inhibition + rnorm(sum(treated_patients), 0, 6)
    ristocetin_inhibition <- ristocetin_inhibition + rnorm(sum(treated_patients), 0, 3)
    
    # Ensure realistic bounds
    collagen_inhibition <- pmax(pmin(collagen_inhibition, 98), 0)
    adp_inhibition <- pmax(pmin(adp_inhibition, 28), 0)
    arachidonic_inhibition <- pmax(pmin(arachidonic_inhibition, 58), 0)
    thrombin_inhibition <- pmax(pmin(thrombin_inhibition, 45), 0)
    epinephrine_inhibition <- pmax(pmin(epinephrine_inhibition, 25), 0)
    ristocetin_inhibition <- pmax(pmin(ristocetin_inhibition, 5), 0)
    
    # Store results
    results$collagen_inhibition[treated_patients] <- round(collagen_inhibition, 1)
    results$adp_inhibition[treated_patients] <- round(adp_inhibition, 1)
    results$arachidonic_inhibition[treated_patients] <- round(arachidonic_inhibition, 1)
    results$thrombin_inhibition[treated_patients] <- round(thrombin_inhibition, 1)
    results$epinephrine_inhibition[treated_patients] <- round(epinephrine_inhibition, 1)
    results$ristocetin_inhibition[treated_patients] <- round(ristocetin_inhibition, 1)
  }
  
  # ENHANCED: Evidence-based pathway weighting for bleeding risk
  # Based on meta-analysis of bleeding outcomes vs pathway inhibition
  
  # Handle NA values
  results[is.na(results)] <- 0
  
  # Updated pathway weights based on bleeding correlation analysis
  results$bleeding_risk_score <- (
    results$collagen_inhibition * 0.65 +        # Increased weight - primary target
    results$arachidonic_inhibition * 0.18 +     # TxA2 pathway important for hemostasis
    results$thrombin_inhibition * 0.10 +        # Coagulation cascade
    results$adp_inhibition * 0.05 +             # Minimal at therapeutic doses
    results$epinephrine_inhibition * 0.02       # Stress response (minimal)
    # Ristocetin excluded (BTK-independent)
  ) / 100
  
  return(results)
}

# Helper function for dose-response modeling
dose_response_emax <- function(conc, ic50, hill, max_inhib) {
  if (any(conc == 0)) {
    result <- rep(1.0, length(conc))
    result[conc > 0] <- 1 - (max_inhib * conc[conc > 0]^hill) / (ic50^hill + conc[conc > 0]^hill)
    return(result)
  }
  return(1 - (max_inhib * conc^hill) / (ic50^hill + conc^hill))
}

# =============================================================================
# PART 4: TEMPORAL BLEEDING EVOLUTION MODEL
# =============================================================================

simulate_temporal_bleeding_events <- function(population, bleeding_risk, platelet_results) {
  
  n_patients <- nrow(population)
  
  # Initialize outcomes
  outcomes <- data.frame(
    patient_id = population$patient_id,
    treatment_arm = bleeding_risk$treatment_arm,
    annual_bleeding_risk = bleeding_risk$total_risk,
    bleeding_risk_score = platelet_results$bleeding_risk_score
  )
  
  # ENHANCED: Time-dependent bleeding risk from Lipsky study
  # 51% bleed within 6 months, median time 49 days
  
  # Phase-specific hazard rates (per month)
  # Months 0-1: 4× baseline, 1-3: 3.5×, 3-6: 2.5×, 6-12: 1.8×, 12+: 1.5×
  
  time_phases <- data.frame(
    phase = c("0-1", "1-3", "3-6", "6-12", "12+"),
    duration_months = c(1, 2, 3, 6, 12),  # Duration of each phase
    hazard_multiplier = c(4.0, 3.5, 2.5, 1.8, 1.5)
  )
  
  # Convert annual risk to monthly baseline hazard
  monthly_baseline_hazard <- -log(1 - outcomes$annual_bleeding_risk) / 12
  
  # For each patient, simulate bleeding events over time
  max_followup_months <- max(population$treatment_duration_months)
  
  # Initialize event tracking
  outcomes$bleeding_events <- 0
  outcomes$major_bleeding_events <- 0
  outcomes$minor_bleeding_events <- 0
  outcomes$time_to_first_bleeding <- NA
  outcomes$bleeding_event_occurred <- FALSE
  
  for (i in 1:n_patients) {
    patient_followup <- population$treatment_duration_months[i]
    monthly_hazard <- monthly_baseline_hazard[i]
    
    if (!is.na(monthly_hazard) && !is.na(patient_followup) && monthly_hazard > 0 && patient_followup > 0) {
      current_time <- 0
      events <- 0
      first_event_time <- NA
      
      while (current_time < patient_followup) {
        # Determine current phase and hazard multiplier
        if (current_time < 1) {
          multiplier <- 4.0
        } else if (current_time < 3) {
          multiplier <- 3.5
        } else if (current_time < 6) {
          multiplier <- 2.5
        } else if (current_time < 12) {
          multiplier <- 1.8
        } else {
          multiplier <- 1.5
        }
        
        # Current hazard
        current_hazard <- monthly_hazard * multiplier
        
        # Time to next event (exponential distribution)
        time_to_event <- rexp(1, current_hazard)
        
        if (current_time + time_to_event <= patient_followup) {
          events <- events + 1
          if (is.na(first_event_time)) {
            first_event_time <- current_time + time_to_event
          }
          current_time <- current_time + time_to_event
        } else {
          break
        }
      }
      
      outcomes$bleeding_events[i] <- events
      if (!is.na(first_event_time)) {
        outcomes$time_to_first_bleeding[i] <- first_event_time * 30.44  # Convert to days
        outcomes$bleeding_event_occurred[i] <- TRUE
      } else {
        outcomes$time_to_first_bleeding[i] <- patient_followup * 30.44
      }
    } else {
      outcomes$time_to_first_bleeding[i] <- patient_followup * 30.44
    }
  }
  
  # Classify bleeding severity
  # Major bleeding probability based on risk factors
  major_bleeding_prob <- pmin(0.1 + outcomes$bleeding_risk_score * 0.4 + 
                             as.numeric(population$anticoagulants) * 0.3 +
                             as.numeric(population$age >= 70) * 0.2, 0.8)
  
  for (i in 1:n_patients) {
    if (outcomes$bleeding_events[i] > 0) {
      major_events <- rbinom(1, outcomes$bleeding_events[i], major_bleeding_prob[i])
      outcomes$major_bleeding_events[i] <- major_events
      outcomes$minor_bleeding_events[i] <- outcomes$bleeding_events[i] - major_events
    }
  }
  
  return(outcomes)
}

# =============================================================================
# PART 5: MAIN ENHANCED VIRTUAL TRIAL FUNCTION
# =============================================================================

run_enhanced_virtual_clinical_trial <- function(n_patients = 5000, save_results = TRUE) {
  
  cat("=== ENHANCED VIRTUAL SAFETY TRIAL ===\n")
  cat("Based on 14,000+ patient meta-analyses and 2024 bleeding risk data\n")
  cat("Patients:", n_patients, "\n\n")
  
  # Step 1: Generate clinical population
  cat("1. Generating clinical synthetic patient population...\n")
  population <- generate_synthetic_population(n_patients, save_file = FALSE)
  cat("   ✓ Clinical CLL population generated (median age 71)\n\n")
  
  # Step 2: Enhanced PK modeling
  cat("2. Calculating enhanced pharmacokinetics with interactions...\n")
  
  # Separate by treatment arm
  ibr_420_patients <- population$treatment_arm == "Ibrutinib_420mg"
  control_patients <- population$treatment_arm == "Control"
  
  # Initialize PK dataframe
  pk_data <- data.frame(
    cmax_ng_ml = numeric(n_patients),
    auc_ng_h_ml = numeric(n_patients),
    cmax_nm = numeric(n_patients),
    auc_nm_h = numeric(n_patients),
    css_avg_nm = numeric(n_patients),
    adherence_factor = numeric(n_patients),
    exposure_factor = numeric(n_patients)
  )
  
  if (sum(ibr_420_patients) > 0) {
    cat("   Processing", sum(ibr_420_patients), "patients on 420mg dose with variability...\n")
    pk_420 <- calculate_clinical_ibrutinib_pk(population[ibr_420_patients, ], 420)
    pk_data[ibr_420_patients, ] <- pk_420
  }
  
  if (sum(control_patients) > 0) {
    pk_data[control_patients, "adherence_factor"] <- 1.0
    pk_data[control_patients, "exposure_factor"] <- 0.0
  }
  
  cat("   ✓ Enhanced PK with 50% CV and interaction effects completed\n\n")
  
  # Step 3: Comprehensive bleeding risk assessment
  cat("3. Calculating comprehensive bleeding risk with all interactions...\n")
  bleeding_risk <- calculate_comprehensive_bleeding_risk(population, pk_data, population$treatment_arm)
  cat("   ✓ Multiplicative risk model with 2.54× anticoagulant effect completed\n\n")
  
  # Step 4: Enhanced platelet aggregation modeling
  cat("4. Simulating enhanced platelet responses with pathway weighting...\n")
  platelet_results <- simulate_enhanced_platelet_response(population, pk_data, population$treatment_arm)
  cat("   ✓ Evidence-based pathway inhibition with updated IC50 values completed\n\n")
  
  # Step 5: Temporal bleeding event simulation
  cat("5. Simulating temporal bleeding events with 6-month plateau...\n")
  clinical_outcomes <- simulate_temporal_bleeding_events(population, bleeding_risk, platelet_results)
  cat("   ✓ Time-dependent bleeding risk with phase-specific hazards completed\n\n")
  
  # Step 6: Combine all enhanced data
  cat("6. Combining enhanced results...\n")
  
  # Safely combine all dataframes
  trial_data <- population
  
  # Add bleeding risk components
  bleeding_cols <- setdiff(colnames(bleeding_risk), colnames(trial_data))
  if (length(bleeding_cols) > 0) {
    trial_data <- cbind(trial_data, bleeding_risk[, bleeding_cols, drop = FALSE])
  }
  
  # Add platelet results
  platelet_cols <- setdiff(colnames(platelet_results), colnames(trial_data))
  if (length(platelet_cols) > 0) {
    trial_data <- cbind(trial_data, platelet_results[, platelet_cols, drop = FALSE])
  }
  
  # Add clinical outcomes
  outcome_cols <- setdiff(colnames(clinical_outcomes), colnames(trial_data))
  if (length(outcome_cols) > 0) {
    trial_data <- cbind(trial_data, clinical_outcomes[, outcome_cols, drop = FALSE])
  }
  
  # Add PK data
  trial_data <- cbind(trial_data, pk_data)
  
  cat("   ✓ Complete enhanced dataset ready:", nrow(trial_data), "patients\n")
  
  if (save_results) {
    # Save enhanced trial data using safe file utilities
    results_dir <- get_ml_results_dir()
    safe_csv_save(trial_data, build_path(results_dir, "enhanced_virtual_clinical_trial_results.csv"))
    cat("\nEnhanced trial results saved to 'enhanced_virtual_clinical_trial_results.csv'\n")
  }
  
  # Comprehensive summary with real-world benchmarks
  cat("\n=== ENHANCED SAFETY TRIAL SUMMARY ===\n")
  
  # Bleeding outcomes summary
  bleeding_summary <- trial_data %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      bleeding_rate = mean(bleeding_event_occurred, na.rm = TRUE) * 100,
      major_bleeding_rate = mean(major_bleeding_events > 0, na.rm = TRUE) * 100,
      mean_bleeding_risk = mean(annual_bleeding_risk, na.rm = TRUE) * 100,
      mean_events_per_patient = mean(bleeding_events, na.rm = TRUE),
      median_time_to_bleeding = median(time_to_first_bleeding[bleeding_event_occurred], na.rm = TRUE),
      .groups = 'drop'
    )
  
  cat("\nBleeding Outcomes (Clinical Model):\n")
  print(bleeding_summary)
  
  # Compare to literature benchmarks
  ibr_bleeding_rate <- bleeding_summary$bleeding_rate[bleeding_summary$treatment_arm == "Ibrutinib_420mg"]
  control_bleeding_rate <- bleeding_summary$bleeding_rate[bleeding_summary$treatment_arm == "Control"]
  
  cat("\nClinical Benchmarks Comparison:\n")
  cat("  Simulated ibrutinib rate:", round(ibr_bleeding_rate, 1), "% (Target: 19%)\n")
  cat("  Simulated control rate:", round(control_bleeding_rate, 1), "% (Target: 2.5%)\n")
  cat("  Risk ratio:", round(ibr_bleeding_rate / control_bleeding_rate, 1), "× (Target: 7.6×)\n")
  
  # Platelet aggregation summary
  platelet_summary <- trial_data %>%
    group_by(treatment_arm) %>%
    summarise(
      collagen_inhibition = mean(collagen_inhibition, na.rm = TRUE),
      adp_inhibition = mean(adp_inhibition, na.rm = TRUE),
      arachidonic_inhibition = mean(arachidonic_inhibition, na.rm = TRUE),
      thrombin_inhibition = mean(thrombin_inhibition, na.rm = TRUE),
      epinephrine_inhibition = mean(epinephrine_inhibition, na.rm = TRUE),
      ristocetin_inhibition = mean(ristocetin_inhibition, na.rm = TRUE),
      .groups = 'drop'
    )
  
  cat("\nPlatelet Aggregation Inhibition (Enhanced Model):\n")
  platelet_summary_rounded <- platelet_summary
  platelet_summary_rounded[, -1] <- round(platelet_summary[, -1], 1)
  print(platelet_summary_rounded)
  
  # Risk factor analysis
  risk_factor_summary <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    summarise(
      anticoagulant_users = mean(anticoagulants) * 100,
      elderly_patients = mean(age >= 70) * 100,
      high_bleeding_risk = mean(annual_bleeding_risk > 0.15) * 100,
      median_exposure = median(css_avg_nm, na.rm = TRUE),
      adherence_rate = mean(adherence_factor, na.rm = TRUE) * 100,
      .groups = 'drop'
    )
  
  cat("\nRisk Factor Distribution (Ibrutinib Patients):\n")
  cat("  Anticoagulant users:", round(risk_factor_summary$anticoagulant_users, 1), "%\n")
  cat("  Elderly patients (≥70):", round(risk_factor_summary$elderly_patients, 1), "%\n")
  cat("  High bleeding risk (>15%/year):", round(risk_factor_summary$high_bleeding_risk, 1), "%\n")
  cat("  Median steady-state concentration:", round(risk_factor_summary$median_exposure, 0), "nM\n")
  cat("  Mean adherence rate:", round(risk_factor_summary$adherence_rate, 1), "%\n")
  
  return(trial_data)
}

# =============================================================================
# PART 6: ENHANCED TRIAL VISUALIZATION FUNCTIONS
# =============================================================================

plot_enhanced_trial_results <- function(trial_data) {
  
  # Enhanced bleeding risk distribution with risk factor stratification
  p1 <- trial_data %>%
    mutate(risk_group = case_when(
      annual_bleeding_risk < 0.05 ~ "Low (<5%)",
      annual_bleeding_risk < 0.15 ~ "Moderate (5-15%)",
      annual_bleeding_risk < 0.30 ~ "High (15-30%)",
      TRUE ~ "Very High (>30%)"
    )) %>%
    ggplot(aes(x = treatment_arm, fill = risk_group)) +
    geom_bar(position = "fill") +
    labs(title = "Annual Bleeding Risk Distribution",
         subtitle = "Clinical risk stratification with multiplicative interactions",
         x = "Treatment Arm", y = "Proportion", fill = "Annual Bleeding Risk") +
    scale_fill_brewer(palette = "Reds", direction = 1) +
    theme_minimal()
  
  # Temporal bleeding pattern analysis
  bleeding_events <- trial_data %>%
    filter(bleeding_event_occurred) %>%
    mutate(time_months = time_to_first_bleeding / 30.44,
           time_phase = case_when(
             time_months < 1 ~ "0-1 month",
             time_months < 3 ~ "1-3 months", 
             time_months < 6 ~ "3-6 months",
             time_months < 12 ~ "6-12 months",
             TRUE ~ "12+ months"
           ))
  
  p2 <- bleeding_events %>%
    ggplot(aes(x = time_phase, fill = treatment_arm)) +
    geom_bar(position = "dodge") +
    labs(title = "Temporal Distribution of First Bleeding Events",
         subtitle = "Showing 6-month plateau pattern from literature",
         x = "Time Phase", y = "Number of Events", fill = "Treatment") +
    theme_minimal() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  # Anticoagulant interaction effects
  anticoag_analysis <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    mutate(anticoag_status = case_when(
      !anticoagulants ~ "No anticoagulants",
      anticoag_type == "Warfarin" ~ "Warfarin",
      doac_type == "Apixaban" ~ "Apixaban", 
      doac_type == "Rivaroxaban" ~ "Rivaroxaban",
      anticoag_type == "DOAC" ~ "Other DOAC",
      TRUE ~ "Other anticoagulant"
    ))
  
  p3 <- anticoag_analysis %>%
    ggplot(aes(x = anticoag_status, y = annual_bleeding_risk * 100)) +
    geom_boxplot(aes(fill = anticoag_status)) +
    geom_hline(yintercept = 2.5, linetype = "dashed", color = "red") +
    annotate("text", x = 1, y = 5, label = "Control rate (2.5%)", color = "red") +
    labs(title = "Bleeding Risk by Anticoagulant Type",
         subtitle = "Showing 2.54× overall increase with drug-specific variations",
         x = "Anticoagulant Status", y = "Annual Bleeding Risk (%)", 
         fill = "Anticoagulant") +
    theme_minimal() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "none")
  
  # Age-dependent bleeding risk
  p4 <- trial_data %>%
    filter(treatment_arm == "Ibrutinib_420mg") %>%
    ggplot(aes(x = age, y = annual_bleeding_risk * 100)) +
    geom_point(alpha = 0.6, color = "steelblue") +
    geom_smooth(method = "gam", se = TRUE, color = "red") +
    geom_vline(xintercept = 70, linetype = "dashed", color = "orange") +
    annotate("text", x = 72, y = max(trial_data$annual_bleeding_risk * 100) * 0.9,
             label = "Age 70\nHR 7.49", color = "orange") +
    labs(title = "Age-Dependent Bleeding Risk", 
         subtitle = "Exponential increase with age (HR 7.49 for ≥70 years)",
         x = "Age (years)", y = "Annual Bleeding Risk (%)") +
    theme_minimal()
  
  # Save enhanced plots using safe file utilities
  results_dir <- get_ml_results_dir()
  ggsave(build_path(results_dir, "enhanced_bleeding_risk_distribution.png"), p1, width = 12, height = 8)
  ggsave(build_path(results_dir, "enhanced_temporal_bleeding_pattern.png"), p2, width = 12, height = 8)
  ggsave(build_path(results_dir, "enhanced_anticoagulant_interactions.png"), p3, width = 12, height = 8)
  ggsave(build_path(results_dir, "enhanced_age_bleeding_relationship.png"), p4, width = 12, height = 8)
  
  cat("Enhanced trial result plots saved.\n")
  
  return(list(p1, p2, p3, p4))
}

cat("Enhanced virtual safety trial simulator loaded successfully!\n")
cat("Use run_enhanced_virtual_clinical_trial(n_patients) to run comprehensive simulation.\n")
cat("Incorporates ALL evidence: 2.54× anticoagulant risk, 7.49× age hazard, 6-month plateau.\n")
cat("Features multiplicative interactions, temporal evolution, and pathway weighting.\n")