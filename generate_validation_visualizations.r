# =============================================================================
# VALIDATION VISUALIZATIONS - SIMULATION vs LITERATURE
# =============================================================================
# Create comprehensive validation plots comparing simulation with literature

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
  library(gridExtra)
  library(scales)
})

cat("=== GENERATING VALIDATION VISUALIZATIONS ===\n\n")

# Load file utilities and data
source("ml_file_utils.r")
results_dir <- get_ml_results_dir()

# Load comparison data
comparison_file <- build_path(results_dir, "literature_validation_comparison.csv")
comparison_data <- read.csv(comparison_file, stringsAsFactors = FALSE)

# Load full population safety data
safety_file <- build_path(results_dir, "full_population_safety_analysis.csv")
safety_data <- read.csv(safety_file, stringsAsFactors = FALSE)

cat("Loaded comparison data and safety results\n\n")

# 1. Literature vs Simulation Comparison Plot
cat("1. Creating literature vs simulation comparison plots...\n")

# Prepare data for plotting
plot_data <- comparison_data %>%
  filter(Study != "Virtual Clinical Trial") %>%
  select(Study, Any_Bleeding_Rate, Major_Bleeding_Rate, Fatal_Bleeding_Rate, ICH_Rate, GI_Bleeding_Rate) %>%
  mutate(
    Study_Type = "Literature",
    Study_Short = case_when(
      grepl("PCYC", Study) ~ "PCYC-1102",
      grepl("RESONATE-2", Study) ~ "RESONATE-2",
      grepl("RESONATE", Study) ~ "RESONATE",
      grepl("HELIOS", Study) ~ "HELIOS",
      grepl("SEER", Study) ~ "SEER-Medicare",
      grepl("Meta", Study) ~ "Meta-analysis",
      grepl("ELEVATE", Study) ~ "ELEVATE-TN",
      grepl("iLLUMINATE", Study) ~ "iLLUMINATE",
      TRUE ~ Study
    )
  )

# Add simulation data
sim_data <- comparison_data %>%
  filter(Study == "Virtual Clinical Trial") %>%
  select(Study, Any_Bleeding_Rate, Major_Bleeding_Rate, Fatal_Bleeding_Rate, ICH_Rate, GI_Bleeding_Rate) %>%
  mutate(
    Study_Type = "Simulation",
    Study_Short = "Virtual Trial"
  )

combined_plot_data <- rbind(plot_data, sim_data)

# Any bleeding comparison
p1 <- ggplot(combined_plot_data, aes(x = reorder(Study_Short, Any_Bleeding_Rate), 
                                    y = Any_Bleeding_Rate, 
                                    fill = Study_Type)) +
  geom_col(position = "dodge", alpha = 0.8) +
  geom_hline(yintercept = mean(plot_data$Any_Bleeding_Rate), 
             linetype = "dashed", color = "red", size = 1) +
  labs(
    title = "Any Bleeding Rates: Literature vs Virtual Clinical Trial",
    subtitle = "Red dashed line = Literature mean (24.0%)",
    x = "Study",
    y = "Any Bleeding Rate (%)",
    fill = "Source"
  ) +
  theme_minimal() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1),
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5)
  ) +
  scale_fill_manual(values = c("Literature" = "steelblue", "Simulation" = "orange")) +
  ylim(0, max(combined_plot_data$Any_Bleeding_Rate) * 1.1)

# Major bleeding comparison
p2 <- ggplot(combined_plot_data, aes(x = reorder(Study_Short, Major_Bleeding_Rate), 
                                    y = Major_Bleeding_Rate, 
                                    fill = Study_Type)) +
  geom_col(position = "dodge", alpha = 0.8) +
  geom_hline(yintercept = mean(plot_data$Major_Bleeding_Rate), 
             linetype = "dashed", color = "red", size = 1) +
  labs(
    title = "Major Bleeding Rates: Literature vs Virtual Clinical Trial",
    subtitle = "Red dashed line = Literature mean (6.1%)",
    x = "Study",
    y = "Major Bleeding Rate (%)",
    fill = "Source"
  ) +
  theme_minimal() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1),
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5)
  ) +
  scale_fill_manual(values = c("Literature" = "steelblue", "Simulation" = "orange")) +
  ylim(0, max(combined_plot_data$Major_Bleeding_Rate) * 1.1)

# Save comparison plots
ggsave(build_path(results_dir, "validation_any_bleeding_comparison.png"), p1, width = 12, height = 8)
ggsave(build_path(results_dir, "validation_major_bleeding_comparison.png"), p2, width = 12, height = 8)

# 2. Risk Ratio Forest Plot
cat("2. Creating risk ratio forest plot...\n")

# Prepare risk ratio data
rr_data <- comparison_data %>%
  filter(!is.na(Control_Any_Bleeding) & Control_Any_Bleeding > 0) %>%
  mutate(
    Any_RR = Any_Bleeding_Rate / Control_Any_Bleeding,
    Major_RR = Major_Bleeding_Rate / Control_Major_Bleeding,
    Study_Clean = case_when(
      grepl("RESONATE-2", Study) ~ "RESONATE-2",
      grepl("RESONATE", Study) ~ "RESONATE", 
      grepl("HELIOS", Study) ~ "HELIOS",
      grepl("Meta", Study) ~ "Meta-analysis",
      grepl("ELEVATE", Study) ~ "ELEVATE-TN",
      grepl("iLLUMINATE", Study) ~ "iLLUMINATE",
      grepl("Virtual", Study) ~ "Virtual Trial",
      TRUE ~ Study
    )
  ) %>%
  select(Study_Clean, Any_RR, Major_RR)

# Forest plot for any bleeding risk ratios
p3 <- ggplot(rr_data, aes(x = Any_RR, y = reorder(Study_Clean, Any_RR))) +
  geom_point(size = 4, color = "blue") +
  geom_vline(xintercept = 1, linetype = "dashed", color = "red") +
  geom_text(aes(label = sprintf("%.2f", Any_RR)), hjust = -0.3, size = 3) +
  labs(
    title = "Risk Ratios for Any Bleeding (Ibrutinib vs Control)",
    subtitle = "Forest plot comparing studies",
    x = "Risk Ratio",
    y = "Study"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5)
  ) +
  xlim(0, max(rr_data$Any_RR) * 1.2)

ggsave(build_path(results_dir, "validation_risk_ratio_forest_plot.png"), p3, width = 10, height = 6)

# 3. Scatter Plot Correlation Analysis
cat("3. Creating correlation scatter plots...\n")

# Prepare literature summary for correlation
lit_summary <- plot_data %>%
  summarise(
    Lit_Any_Mean = mean(Any_Bleeding_Rate),
    Lit_Major_Mean = mean(Major_Bleeding_Rate),
    Lit_Fatal_Mean = mean(Fatal_Bleeding_Rate),
    Lit_ICH_Mean = mean(ICH_Rate),
    Lit_GI_Mean = mean(GI_Bleeding_Rate)
  )

sim_summary <- sim_data %>%
  select(Any_Bleeding_Rate, Major_Bleeding_Rate, Fatal_Bleeding_Rate, ICH_Rate, GI_Bleeding_Rate)

# Create correlation data
correlation_data <- data.frame(
  Outcome = c("Any Bleeding", "Major Bleeding", "Fatal Bleeding", "ICH", "GI Bleeding"),
  Literature = c(lit_summary$Lit_Any_Mean, lit_summary$Lit_Major_Mean, 
                lit_summary$Lit_Fatal_Mean, lit_summary$Lit_ICH_Mean, lit_summary$Lit_GI_Mean),
  Simulation = c(sim_summary$Any_Bleeding_Rate, sim_summary$Major_Bleeding_Rate,
                sim_summary$Fatal_Bleeding_Rate, sim_summary$ICH_Rate, sim_summary$GI_Bleeding_Rate)
)

# Scatter plot
p4 <- ggplot(correlation_data, aes(x = Literature, y = Simulation)) +
  geom_point(size = 4, alpha = 0.7) +
  geom_abline(intercept = 0, slope = 1, linetype = "dashed", color = "red") +
  geom_text(aes(label = Outcome), hjust = 0.5, vjust = -0.5, size = 3) +
  labs(
    title = "Simulation vs Literature: Bleeding Outcome Correlation",
    subtitle = "Red line = perfect correlation",
    x = "Literature Mean Rate (%)",
    y = "Simulation Rate (%)"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5)
  )

ggsave(build_path(results_dir, "validation_correlation_scatter.png"), p4, width = 10, height = 8)

# 4. Age-Stratified Validation Plot
cat("4. Creating age-stratified validation plot...\n")

# Calculate age-stratified rates from simulation
ibr_patients <- safety_data[safety_data$treatment_arm == "Ibrutinib_420mg", ]

age_sim_data <- data.frame(
  Age_Group = c("≥65 years", "≥70 years", "≥75 years"),
  Sim_Any = c(
    mean(ibr_patients$any_bleeding[ibr_patients$age >= 65]) * 100,
    mean(ibr_patients$any_bleeding[ibr_patients$age >= 70]) * 100,
    mean(ibr_patients$any_bleeding[ibr_patients$age >= 75]) * 100
  ),
  Sim_Major = c(
    mean(ibr_patients$major_bleeding[ibr_patients$age >= 65]) * 100,
    mean(ibr_patients$major_bleeding[ibr_patients$age >= 70]) * 100,
    mean(ibr_patients$major_bleeding[ibr_patients$age >= 75]) * 100
  ),
  Lit_Any = c(32.1, 35.8, 42.3),
  Lit_Major = c(9.2, 11.5, 15.1)
)

# Reshape for plotting
age_plot_data <- age_sim_data %>%
  reshape2::melt(id.vars = "Age_Group", 
                measure.vars = c("Sim_Any", "Lit_Any", "Sim_Major", "Lit_Major"),
                variable.name = "Metric", value.name = "Rate") %>%
  mutate(
    Source = ifelse(grepl("Sim", Metric), "Simulation", "Literature"),
    Outcome = ifelse(grepl("Any", Metric), "Any Bleeding", "Major Bleeding")
  )

p5 <- ggplot(age_plot_data, aes(x = Age_Group, y = Rate, fill = Source)) +
  geom_col(position = "dodge", alpha = 0.8) +
  facet_wrap(~Outcome, scales = "free_y") +
  labs(
    title = "Age-Stratified Bleeding Rates: Simulation vs Literature",
    subtitle = "Comparison with SEER-Medicare real-world data",
    x = "Age Group",
    y = "Bleeding Rate (%)",
    fill = "Source"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5),
    strip.text = element_text(face = "bold")
  ) +
  scale_fill_manual(values = c("Literature" = "steelblue", "Simulation" = "orange"))

ggsave(build_path(results_dir, "validation_age_stratified.png"), p5, width = 12, height = 6)

# 5. Model Calibration Assessment
cat("5. Creating model calibration assessment...\n")

# Calculate calibration metrics
calibration_data <- data.frame(
  Outcome = c("Any Bleeding", "Major Bleeding", "Fatal Bleeding", "ICH", "GI Bleeding"),
  Literature_Mean = c(24.0, 6.1, 0.8, 1.1, 3.7),
  Simulation = c(57.6, 23.9, 3.1, 2.4, 15.3),
  Fold_Difference = c(57.6/24.0, 23.9/6.1, 3.1/0.8, 2.4/1.1, 15.3/3.7)
)

p6 <- ggplot(calibration_data, aes(x = reorder(Outcome, Fold_Difference), y = Fold_Difference)) +
  geom_col(fill = "coral", alpha = 0.8) +
  geom_hline(yintercept = 1, linetype = "dashed", color = "red", size = 1) +
  geom_text(aes(label = sprintf("%.1fx", Fold_Difference)), vjust = -0.3, size = 3) +
  labs(
    title = "Model Calibration: Simulation vs Literature Fold Differences",
    subtitle = "Red line = perfect calibration (1.0x)",
    x = "Bleeding Outcome",
    y = "Fold Difference (Simulation / Literature)"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5),
    axis.text.x = element_text(angle = 45, hjust = 1)
  ) +
  ylim(0, max(calibration_data$Fold_Difference) * 1.1)

ggsave(build_path(results_dir, "validation_model_calibration.png"), p6, width = 10, height = 6)

# Save calibration data
calibration_file <- build_path(results_dir, "model_calibration_assessment.csv")
safe_csv_save(calibration_data, calibration_file)

cat("\n=== VALIDATION VISUALIZATIONS COMPLETED ===\n")
cat("Generated files:\n")
cat("- validation_any_bleeding_comparison.png\n")
cat("- validation_major_bleeding_comparison.png\n")
cat("- validation_risk_ratio_forest_plot.png\n")
cat("- validation_correlation_scatter.png\n")
cat("- validation_age_stratified.png\n")
cat("- validation_model_calibration.png\n")
cat("- model_calibration_assessment.csv\n\n")

cat("✓ Literature validation visualizations complete\n")
cat("✓ Statistical comparisons generated\n")
cat("✓ Model calibration assessment created\n")
