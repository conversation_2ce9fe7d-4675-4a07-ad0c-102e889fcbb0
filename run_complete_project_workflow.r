#!/usr/bin/env Rscript
# =============================================================================
# COMPLETE IBRUTINIB PLATELET QSP PROJECT WORKFLOW
# =============================================================================
# This script runs the entire Ibrutinib Platelet QSP project independently
# using only the 8 core files identified as sufficient for standalone operation
#
# Workflow:
# 1. Generate synthetic patient population
# 2. Run virtual clinical trial with QSP modeling
# 3. Process trial data for machine learning
# 4. Execute ML pipeline with optimization
# 5. Generate comprehensive analysis report
#
# Dependencies: Only the 8 core project files
# =============================================================================

# Clear workspace and set options
cat("Initializing Ibrutinib Platelet QSP Project Workflow...\n")
rm(list = ls())
options(warn = 1)

# Set working directory to project root
if (interactive() && requireNamespace("rstudioapi", quietly = TRUE)) {
  # Use RStudio API if available and in interactive mode
  project_root <- dirname(rstudioapi::getActiveDocumentContext()$path)
  if (!is.null(project_root) && project_root != "") {
    setwd(project_root)
  }
} else {
  # Use script location or current directory
  args <- commandArgs(trailingOnly = FALSE)
  script_path <- sub("--file=", "", args[grep("--file=", args)])
  if (length(script_path) > 0) {
    project_root <- dirname(script_path)
    setwd(project_root)
  }
}

# =============================================================================
# STEP 1: INSTALL REQUIRED PACKAGES
# =============================================================================

install_required_packages <- function() {
  cat("\n=== STEP 1: Installing Required Packages ===\n")
  
  # Set CRAN mirror if not already set
  if (length(getOption("repos")) == 0 || getOption("repos")["CRAN"] == "@CRAN@") {
    options(repos = c(CRAN = "https://cran.rstudio.com/"))
  }
  
  required_packages <- c(
    "randomForest", "xgboost", "nnet", "caret", "caretEnsemble", "pROC", "dplyr", "ggplot2", "corrplot",
    "ROSE", "parallel", "doParallel", "Boruta", "e1071", "glmnet", "h2o", "smotefamily"
  )
  
  missing_packages <- required_packages[!sapply(required_packages, requireNamespace, quietly = TRUE)]
  
  if (length(missing_packages) > 0) {
    cat("Installing missing packages:", paste(missing_packages, collapse = ", "), "\n")
    install.packages(missing_packages, dependencies = TRUE)
  } else {
    cat("All required packages are already installed.\n")
  }
  
  # Load all packages
  lapply(required_packages, library, character.only = TRUE)
  cat("Package setup complete.\n")
}

# =============================================================================
# STEP 2: SOURCE ALL CORE FILES
# =============================================================================

source_core_files <- function() {
  cat("\n=== STEP 2: Loading Core Project Files ===\n")
  
  core_files <- c(
    "synthetic_population_generator.r",
    "ibrutinib_comprehensive_model_with_interactions.r",
    "virtual_clinical_trial.r",
    "ml_response_prediction.r",
    "ml_optimization.r",
    "ml_ensemble.r",
    "ml_integration.r",
    "ml_demo.r"
  )
  
  missing_files <- character()
  for (file in core_files) {
    if (file.exists(file)) {
      cat("Loading:", file, "\n")
      source(file)
    } else {
      missing_files <- c(missing_files, file)
    }
  }
  
  if (length(missing_files) > 0) {
    stop("Missing core files: ", paste(missing_files, collapse = ", "))
  }
  
  cat("All core files loaded successfully.\n")
}

# =============================================================================
# STEP 3: GENERATE SYNTHETIC POPULATION
# =============================================================================

generate_synthetic_population <- function() {
  cat("\n=== STEP 3: Generating Synthetic Patient Population ===\n")
  
  # Set seed for reproducibility
  set.seed(12345)
  
  # Generate population using the synthetic population generator
  population <- generate_synthetic_population(
    n_patients = 5000,
    save_file = TRUE
  )
  
  cat("Generated", nrow(population), "synthetic patients\n")
  cat("Population characteristics:\n")
  cat("- Mean age:", round(mean(population$age), 1), "years\n")
  cat("- Male proportion:", round(sum(population$sex == "Male")/nrow(population)*100, 1), "%\n")
  cat("- Disease prevalence:", round(sum(population$disease == "CLL")/nrow(population)*100, 1), "%\n")
  
  return(population)
}

# =============================================================================
# STEP 4: RUN VIRTUAL CLINICAL TRIAL
# =============================================================================

run_virtual_trial <- function(population) {
  cat("\n=== STEP 4: Running Virtual Clinical Trial ===\n")
  
  # Set seed for reproducibility
  set.seed(54321)
  
  # Run clinical trial with QSP modeling
  trial_results <- run_enhanced_virtual_clinical_trial(
    n_patients = nrow(population)
  )
  
  cat("Trial completed with", nrow(trial_results), "patient records\n")
  cat("Trial summary:\n")
  cat("- Treatment arms:", length(unique(trial_results$treatment_arm)), "\n")
  cat("- Bleeding events:", sum(trial_results$bleeding_event, na.rm = TRUE), "\n")
  cat("- Major bleeding:", sum(trial_results$major_bleeding, na.rm = TRUE), "\n")
  
  return(trial_results)
}

# =============================================================================
# STEP 5: MACHINE LEARNING ANALYSIS
# =============================================================================

run_ml_analysis <- function(trial_results) {
  cat("\n=== STEP 5: Running Machine Learning Analysis ===\n")
  
  # Process data for ML
  cat("Processing trial data for ML...\n")
  ml_data <- preprocess_trial_data(trial_results)
  
  # Run ML pipeline
  cat("Running ML pipeline...\n")
  ml_results <- run_ml_pipeline(
    trial_data = trial_results,
    targets = c("major_bleeding_event"),
    parallel_cores = detectCores() - 1
  )
  
  cat("ML analysis complete\n")
  cat("Results summary:\n")
  
  # Handle NULL results safely - extract from proper structure
  best_model <- if (!is.null(ml_results$results) && length(ml_results$results) > 0) {
    first_target <- names(ml_results$results)[1]
    ml_results$results[[first_target]]$best_model
  } else "None"
  
  best_auc <- if (!is.null(ml_results$results) && length(ml_results$results) > 0) {
    first_target <- names(ml_results$results)[1]
    if (!is.null(ml_results$results[[first_target]]$best_performance)) {
      round(as.numeric(ml_results$results[[first_target]]$best_performance$AUC), 3)
    } else "N/A"
  } else "N/A"
  
  best_accuracy <- if (!is.null(ml_results$results) && length(ml_results$results) > 0) {
    first_target <- names(ml_results$results)[1]
    if (!is.null(ml_results$results[[first_target]]$best_performance)) {
      round(as.numeric(ml_results$results[[first_target]]$best_performance$Accuracy), 3)
    } else "N/A"
  } else "N/A"
  
  cat("- Best model:", best_model, "\n")
  cat("- AUC:", best_auc, "\n")
  cat("- Accuracy:", best_accuracy, "\n")
  
  return(list(ml_data = ml_data, ml_results = ml_results))
}

# =============================================================================
# STEP 6: GENERATE COMPREHENSIVE REPORT
# =============================================================================

generate_project_report <- function(population, trial_results, ml_analysis) {
  cat("\n=== STEP 6: Generating Project Report ===\n")
  
  # Create summary statistics
  report <- list(
    project_info = list(
      name = "Ibrutinib Platelet QSP Project",
      version = "1.0",
      date = Sys.Date(),
      files_used = 8
    ),
    population_summary = list(
      total_patients = nrow(population),
      demographics = summary(population[, c("age", "weight", "bmi")]),
      disease_distribution = table(population$disease),
      comorbidities = sum(population$hypertension, na.rm = TRUE)
    ),
    trial_summary = list(
      total_records = nrow(trial_results),
      bleeding_events = sum(trial_results$bleeding_event, na.rm = TRUE),
      major_bleeding = sum(trial_results$major_bleeding, na.rm = TRUE),
      pathway_inhibition = list(
        collagen = mean(trial_results$collagen_inhibition, na.rm = TRUE),
        adp = mean(trial_results$adp_inhibition, na.rm = TRUE),
        arachidonic = mean(trial_results$arachidonic_inhibition, na.rm = TRUE)
      )
    ),
    ml_performance = list(
      best_model = if(!is.null(ml_analysis$ml_results$results) && length(ml_analysis$ml_results$results) > 0) {
        first_target <- names(ml_analysis$ml_results$results)[1]
        ml_analysis$ml_results$results[[first_target]]$best_model
      } else "None",
      auc = if(!is.null(ml_analysis$ml_results$results) && length(ml_analysis$ml_results$results) > 0) {
        first_target <- names(ml_analysis$ml_results$results)[1]
        if(!is.null(ml_analysis$ml_results$results[[first_target]]$best_performance)) {
          as.numeric(ml_analysis$ml_results$results[[first_target]]$best_performance$AUC)
        } else NA
      } else NA,
      accuracy = if(!is.null(ml_analysis$ml_results$results) && length(ml_analysis$ml_results$results) > 0) {
        first_target <- names(ml_analysis$ml_results$results)[1]
        if(!is.null(ml_analysis$ml_results$results[[first_target]]$best_performance)) {
          as.numeric(ml_analysis$ml_results$results[[first_target]]$best_performance$Accuracy)
        } else NA
      } else NA,
      sensitivity = if(!is.null(ml_analysis$ml_results$results) && length(ml_analysis$ml_results$results) > 0) {
        first_target <- names(ml_analysis$ml_results$results)[1]
        if(!is.null(ml_analysis$ml_results$results[[first_target]]$best_performance)) {
          as.numeric(ml_analysis$ml_results$results[[first_target]]$best_performance$Sensitivity)
        } else NA
      } else NA,
      specificity = if(!is.null(ml_analysis$ml_results$results) && length(ml_analysis$ml_results$results) > 0) {
        first_target <- names(ml_analysis$ml_results$results)[1]
        if(!is.null(ml_analysis$ml_results$results[[first_target]]$best_performance)) {
          as.numeric(ml_analysis$ml_results$results[[first_target]]$best_performance$Specificity)
        } else NA
      } else NA
    )
  )
  
  # Save report to file
  saveRDS(report, file = "project_summary_report.rds")
  
  # Print summary
  cat(paste0("\n", strrep("=", 60), "\n"))
  cat("PROJECT EXECUTION COMPLETE\n")
  cat(paste0(strrep("=", 60), "\n"))
  cat("Project: Ibrutinib Platelet QSP Analysis\n")
  cat("Files used: 8 core files (independent operation)\n")
  cat("Patients analyzed:", report$population_summary$total_patients, "\n")
  cat("Bleeding events predicted:", report$trial_summary$bleeding_events, "\n")
  auc_value <- if(is.numeric(report$ml_performance$auc)) round(report$ml_performance$auc, 3) else "N/A"
  cat("ML model AUC:", auc_value, "\n")
  cat("Report saved to: project_summary_report.rds\n")
  cat(paste0(strrep("=", 60), "\n"))
  
  return(report)
}

# =============================================================================
# MAIN EXECUTION FUNCTION
# =============================================================================

run_complete_project <- function() {
  cat("Starting complete Ibrutinib Platelet QSP project workflow...\n")
  
  # Track execution time
  start_time <- Sys.time()
  
  tryCatch({
    # Step 1: Install packages
    install_required_packages()
    
    # Step 2: Load core files
    source_core_files()
    
    # Step 3: Generate population
    population <- generate_synthetic_population()
    
    # Step 4: Run clinical trial
    trial_results <- run_virtual_trial(population)
    
    # Step 5: Run ML analysis
    ml_analysis <- run_ml_analysis(trial_results)
    
    # Step 6: Generate report
    report <- generate_project_report(population, trial_results, ml_analysis)
    
    # Calculate total execution time
    end_time <- Sys.time()
    execution_time <- as.numeric(difftime(end_time, start_time, units = "mins"))
    
    cat(paste0("\n", strrep("*", 60), "\n"))
    cat("WORKFLOW EXECUTION SUMMARY:\n")
    cat("Total execution time:", round(execution_time, 1), "minutes\n")
    cat("All 8 core files executed successfully\n")
    cat("Project is fully independent and self-contained\n")
    cat(paste0(strrep("*", 60), "\n"))
    
    return(list(
      status = "success",
      population = population,
      trial_results = trial_results,
      ml_analysis = ml_analysis,
      report = report,
      execution_time = execution_time
    ))
    
  }, error = function(e) {
    cat("\nERROR: Workflow execution failed\n")
    cat("Error message:", e$message, "\n")
    cat("Error call:", deparse(e$call), "\n")
    cat("Traceback:\n")
    traceback()
    cat("Please check the error details above\n")
    return(list(status = "error", error = e$message, call = e$call))
  })
}

# =============================================================================
# EXECUTE PROJECT
# =============================================================================

if (!interactive()) {
  # Run complete project when executed as script
  results <- run_complete_project()
  
  # Save final results
  if (results$status == "success") {
    saveRDS(results, file = "complete_project_results.rds")
    cat("\nComplete project results saved to: complete_project_results.rds\n")
  }
}

# For interactive use, provide function
# run_complete_project()  # Uncomment to run interactively