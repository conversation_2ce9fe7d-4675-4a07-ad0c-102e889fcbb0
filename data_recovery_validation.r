# =============================================================================
# DATA RECOVERY AND VALIDATION MODULE
# =============================================================================
# This module provides comprehensive data recovery and validation functions
# for the secure data storage system, including automated recovery workflows,
# data validation checks, and emergency recovery procedures.
#
# Features:
# - Automated data recovery from backups
# - Data validation and quality checks
# - Emergency recovery procedures
# - Data consistency verification
# - Recovery workflow automation
#
# Author: Data Recovery System
# Date: 2024
# =============================================================================

# Load required libraries and modules
source("secure_data_storage.r")
source("data_integrity_manager.r")

if (!require(dplyr, quietly = TRUE)) {
  install.packages("dplyr")
  library(dplyr)
}

if (!require(lubridate, quietly = TRUE)) {
  install.packages("lubridate")
  library(lubridate)
}

#' Comprehensive Data Validation
#' 
#' Performs comprehensive validation checks on platelet aggregation data
#' 
#' @param data Data frame to validate
#' @param data_type Type of data ("inhibition", "aggregation", "pk", "general")
#' @param verbose Whether to print detailed validation results (default: TRUE)
#' @return List with validation results and recommendations
comprehensive_data_validation <- function(data, data_type = "inhibition", verbose = TRUE) {
  
  if (verbose) cat("\n=== COMPREHENSIVE DATA VALIDATION ===\n")
  
  validation_results <- list(
    overall_status = "PASS",
    checks_performed = 0,
    checks_passed = 0,
    checks_failed = 0,
    warnings = character(0),
    errors = character(0),
    recommendations = character(0),
    data_quality_score = 0
  )
  
  # Basic structure validation
  validation_results$checks_performed <- validation_results$checks_performed + 1
  if (is.data.frame(data) && nrow(data) > 0 && ncol(data) > 0) {
    validation_results$checks_passed <- validation_results$checks_passed + 1
    if (verbose) cat("✓ Basic structure validation passed\n")
  } else {
    validation_results$checks_failed <- validation_results$checks_failed + 1
    validation_results$errors <- c(validation_results$errors, "Invalid data structure")
    validation_results$overall_status <- "FAIL"
    if (verbose) cat("✗ Basic structure validation failed\n")
    return(validation_results)
  }
  
  # Data type specific validations
  if (data_type == "inhibition") {
    
    # Required columns for inhibition data
    required_cols <- c("agonist_name", "dose_mg", "percent_inhibition")
    validation_results$checks_performed <- validation_results$checks_performed + 1
    
    missing_cols <- setdiff(required_cols, names(data))
    if (length(missing_cols) == 0) {
      validation_results$checks_passed <- validation_results$checks_passed + 1
      if (verbose) cat("✓ Required columns present\n")
    } else {
      validation_results$checks_failed <- validation_results$checks_failed + 1
      validation_results$errors <- c(validation_results$errors, 
                                    sprintf("Missing columns: %s", paste(missing_cols, collapse = ", ")))
      validation_results$overall_status <- "FAIL"
      if (verbose) cat(sprintf("✗ Missing columns: %s\n", paste(missing_cols, collapse = ", ")))
    }
    
    # Validate dose ranges
    if ("dose_mg" %in% names(data)) {
      validation_results$checks_performed <- validation_results$checks_performed + 1
      
      dose_range_valid <- all(data$dose_mg >= 0 & data$dose_mg <= 1000, na.rm = TRUE)
      if (dose_range_valid) {
        validation_results$checks_passed <- validation_results$checks_passed + 1
        if (verbose) cat("✓ Dose ranges are valid (0-1000 mg)\n")
      } else {
        validation_results$checks_failed <- validation_results$checks_failed + 1
        validation_results$errors <- c(validation_results$errors, "Invalid dose ranges detected")
        validation_results$overall_status <- "FAIL"
        if (verbose) cat("✗ Invalid dose ranges detected\n")
      }
    }
    
    # Validate inhibition percentages
    if ("percent_inhibition" %in% names(data)) {
      validation_results$checks_performed <- validation_results$checks_performed + 1
      
      inhibition_range_valid <- all(data$percent_inhibition >= 0 & data$percent_inhibition <= 100, na.rm = TRUE)
      if (inhibition_range_valid) {
        validation_results$checks_passed <- validation_results$checks_passed + 1
        if (verbose) cat("✓ Inhibition percentages are valid (0-100%)\n")
      } else {
        validation_results$checks_failed <- validation_results$checks_failed + 1
        validation_results$warnings <- c(validation_results$warnings, "Inhibition percentages outside 0-100% range")
        if (verbose) cat("⚠ Warning: Inhibition percentages outside expected range\n")
      }
    }
    
    # Validate agonist names
    if ("agonist_name" %in% names(data)) {
      validation_results$checks_performed <- validation_results$checks_performed + 1
      
      expected_agonists <- c("ADP", "Arachidonic Acid", "Collagen", "Thrombin", "Epinephrine", "Ristocetin")
      unique_agonists <- unique(data$agonist_name)
      unexpected_agonists <- setdiff(unique_agonists, expected_agonists)
      
      if (length(unexpected_agonists) == 0) {
        validation_results$checks_passed <- validation_results$checks_passed + 1
        if (verbose) cat("✓ All agonist names are recognized\n")
      } else {
        validation_results$checks_failed <- validation_results$checks_failed + 1
        validation_results$warnings <- c(validation_results$warnings, 
                                        sprintf("Unexpected agonists: %s", paste(unexpected_agonists, collapse = ", ")))
        if (verbose) cat(sprintf("⚠ Warning: Unexpected agonists: %s\n", paste(unexpected_agonists, collapse = ", ")))
      }
    }
  }
  
  # General data quality checks
  
  # Check for missing values
  validation_results$checks_performed <- validation_results$checks_performed + 1
  missing_count <- sum(is.na(data))
  missing_percentage <- (missing_count / (nrow(data) * ncol(data))) * 100
  
  if (missing_percentage < 5) {
    validation_results$checks_passed <- validation_results$checks_passed + 1
    if (verbose) cat(sprintf("✓ Missing values: %.1f%% (acceptable)\n", missing_percentage))
  } else if (missing_percentage < 15) {
    validation_results$warnings <- c(validation_results$warnings, 
                                   sprintf("High missing value percentage: %.1f%%", missing_percentage))
    if (verbose) cat(sprintf("⚠ Warning: Missing values: %.1f%%\n", missing_percentage))
  } else {
    validation_results$checks_failed <- validation_results$checks_failed + 1
    validation_results$errors <- c(validation_results$errors, 
                                  sprintf("Excessive missing values: %.1f%%", missing_percentage))
    validation_results$overall_status <- "FAIL"
    if (verbose) cat(sprintf("✗ Excessive missing values: %.1f%%\n", missing_percentage))
  }
  
  # Check for duplicate rows
  validation_results$checks_performed <- validation_results$checks_performed + 1
  duplicate_count <- sum(duplicated(data))
  
  if (duplicate_count == 0) {
    validation_results$checks_passed <- validation_results$checks_passed + 1
    if (verbose) cat("✓ No duplicate rows detected\n")
  } else {
    validation_results$warnings <- c(validation_results$warnings, 
                                   sprintf("%d duplicate rows detected", duplicate_count))
    if (verbose) cat(sprintf("⚠ Warning: %d duplicate rows detected\n", duplicate_count))
  }
  
  # Calculate data quality score
  validation_results$data_quality_score <- (validation_results$checks_passed / validation_results$checks_performed) * 100
  
  # Generate recommendations
  if (validation_results$data_quality_score < 70) {
    validation_results$recommendations <- c(validation_results$recommendations, 
                                          "Data quality is poor - consider data cleaning")
  }
  
  if (missing_percentage > 10) {
    validation_results$recommendations <- c(validation_results$recommendations, 
                                          "High missing values - investigate data collection")
  }
  
  if (duplicate_count > 0) {
    validation_results$recommendations <- c(validation_results$recommendations, 
                                          "Remove duplicate rows to improve data quality")
  }
  
  if (verbose) {
    cat(sprintf("\n=== VALIDATION SUMMARY ===\n"))
    cat(sprintf("Overall Status: %s\n", validation_results$overall_status))
    cat(sprintf("Data Quality Score: %.1f%%\n", validation_results$data_quality_score))
    cat(sprintf("Checks Performed: %d\n", validation_results$checks_performed))
    cat(sprintf("Checks Passed: %d\n", validation_results$checks_passed))
    cat(sprintf("Checks Failed: %d\n", validation_results$checks_failed))
    
    if (length(validation_results$recommendations) > 0) {
      cat("\nRecommendations:\n")
      for (rec in validation_results$recommendations) {
        cat(sprintf("  • %s\n", rec))
      }
    }
  }
  
  return(validation_results)
}

#' Emergency Data Recovery
#' 
#' Performs emergency recovery procedures when primary data is corrupted
#' 
#' @param file_path Path to the corrupted file
#' @param recovery_strategy Strategy to use ("backup", "regenerate", "manual")
#' @param verbose Whether to print recovery progress (default: TRUE)
#' @return List with recovery results
emergency_data_recovery <- function(file_path, recovery_strategy = "backup", verbose = TRUE) {
  
  if (verbose) cat("\n=== EMERGENCY DATA RECOVERY ===\n")
  if (verbose) cat(sprintf("Target file: %s\n", basename(file_path)))
  if (verbose) cat(sprintf("Recovery strategy: %s\n", recovery_strategy))
  
  recovery_result <- list(
    success = FALSE,
    strategy_used = recovery_strategy,
    recovered_file = NULL,
    backup_source = NULL,
    recovery_time = Sys.time(),
    data_validation = NULL,
    recommendations = character(0)
  )
  
  if (recovery_strategy == "backup") {
    
    # Look for backup files
    file_base <- tools::file_path_sans_ext(file_path)
    file_ext <- tools::file_ext(file_path)
    backup_pattern <- sprintf("%s_backup_*\\.%s$", basename(file_base), file_ext)
    backup_dir <- dirname(file_path)
    
    backup_files <- list.files(backup_dir, pattern = backup_pattern, full.names = TRUE)
    
    if (length(backup_files) == 0) {
      if (verbose) cat("✗ No backup files found\n")
      recovery_result$recommendations <- c(recovery_result$recommendations, 
                                         "No backups available - consider regenerating data")
      return(recovery_result)
    }
    
    # Sort backups by modification time (newest first)
    backup_files <- backup_files[order(file.mtime(backup_files), decreasing = TRUE)]
    
    if (verbose) cat(sprintf("Found %d backup files\n", length(backup_files)))
    
    # Try each backup until we find a good one
    for (backup_file in backup_files) {
      if (verbose) cat(sprintf("Testing backup: %s\n", basename(backup_file)))
      
      # Load and validate backup
      backup_test <- tryCatch({
        if (tools::file_ext(backup_file) == "csv") {
          test_data <- read.csv(backup_file)
        } else if (tools::file_ext(backup_file) == "rds") {
          test_data <- readRDS(backup_file)
        } else {
          stop("Unsupported file format")
        }
        
        # Validate the backup data
        validation <- comprehensive_data_validation(test_data, verbose = FALSE)
        
        list(success = TRUE, data = test_data, validation = validation)
      }, error = function(e) {
        list(success = FALSE, error = e$message)
      })
      
      if (backup_test$success && backup_test$validation$overall_status == "PASS") {
        # This backup is good, restore it
        tryCatch({
          file.copy(backup_file, file_path, overwrite = TRUE)
          
          recovery_result$success <- TRUE
          recovery_result$recovered_file <- file_path
          recovery_result$backup_source <- backup_file
          recovery_result$data_validation <- backup_test$validation
          
          if (verbose) cat(sprintf("✓ Successfully recovered from %s\n", basename(backup_file)))
          if (verbose) cat(sprintf("✓ Data quality score: %.1f%%\n", backup_test$validation$data_quality_score))
          
          break
        }, error = function(e) {
          if (verbose) cat(sprintf("✗ Failed to restore backup: %s\n", e$message))
        })
      } else {
        if (verbose) cat("✗ Backup validation failed\n")
      }
    }
    
    if (!recovery_result$success) {
      recovery_result$recommendations <- c(recovery_result$recommendations, 
                                         "All backups failed validation - consider manual recovery")
    }
    
  } else if (recovery_strategy == "regenerate") {
    
    if (verbose) cat("Regeneration strategy not implemented in this version\n")
    recovery_result$recommendations <- c(recovery_result$recommendations, 
                                       "Regeneration requires running the full simulation pipeline")
    
  } else if (recovery_strategy == "manual") {
    
    if (verbose) cat("Manual recovery strategy selected\n")
    recovery_result$recommendations <- c(recovery_result$recommendations, 
                                       "Manual recovery requires user intervention",
                                       "Check data sources and regenerate if necessary")
  }
  
  if (verbose) {
    cat(sprintf("\n=== RECOVERY SUMMARY ===\n"))
    cat(sprintf("Recovery Status: %s\n", ifelse(recovery_result$success, "SUCCESS", "FAILED")))
    if (recovery_result$success) {
      cat(sprintf("Recovered from: %s\n", basename(recovery_result$backup_source)))
      cat(sprintf("Data Quality: %.1f%%\n", recovery_result$data_validation$data_quality_score))
    }
    
    if (length(recovery_result$recommendations) > 0) {
      cat("\nRecommendations:\n")
      for (rec in recovery_result$recommendations) {
        cat(sprintf("  • %s\n", rec))
      }
    }
  }
  
  return(recovery_result)
}

#' Automated Recovery Workflow
#' 
#' Comprehensive automated recovery workflow for multiple files
#' 
#' @param manifest_path Path to the integrity manifest
#' @param auto_recover Whether to automatically attempt recovery (default: TRUE)
#' @param verbose Whether to print detailed progress (default: TRUE)
#' @return List with workflow results
automated_recovery_workflow <- function(manifest_path = "comprehensive_inhibition_manifest.json", 
                                       auto_recover = TRUE, verbose = TRUE) {
  
  if (verbose) cat("\n=== AUTOMATED RECOVERY WORKFLOW ===\n")
  
  workflow_result <- list(
    files_checked = 0,
    files_corrupted = 0,
    files_recovered = 0,
    files_failed = 0,
    recovery_details = list(),
    overall_success = FALSE
  )
  
  # Check if manifest exists
  if (!file.exists(manifest_path)) {
    if (verbose) cat("✗ Integrity manifest not found\n")
    return(workflow_result)
  }
  
  # Verify data integrity
  verification <- verify_data_integrity(manifest_path, verbose = verbose)
  
  workflow_result$files_checked <- verification$total_files
  workflow_result$files_corrupted <- verification$corrupted + verification$missing
  
  if (verification$success) {
    if (verbose) cat("✓ All files passed integrity verification\n")
    workflow_result$overall_success <- TRUE
    return(workflow_result)
  }
  
  # Identify corrupted files
  corrupted_files <- character(0)
  for (file_path in names(verification$details)) {
    if (verification$details[[file_path]]$status %in% c("corrupted", "missing")) {
      corrupted_files <- c(corrupted_files, file_path)
    }
  }
  
  if (length(corrupted_files) == 0) {
    if (verbose) cat("No corrupted files detected\n")
    workflow_result$overall_success <- TRUE
    return(workflow_result)
  }
  
  if (verbose) cat(sprintf("Found %d corrupted/missing files\n", length(corrupted_files)))
  
  # Attempt recovery for each corrupted file
  if (auto_recover) {
    for (corrupted_file in corrupted_files) {
      if (verbose) cat(sprintf("\nRecovering: %s\n", basename(corrupted_file)))
      
      recovery <- emergency_data_recovery(corrupted_file, "backup", verbose = verbose)
      workflow_result$recovery_details[[corrupted_file]] <- recovery
      
      if (recovery$success) {
        workflow_result$files_recovered <- workflow_result$files_recovered + 1
      } else {
        workflow_result$files_failed <- workflow_result$files_failed + 1
      }
    }
  } else {
    if (verbose) cat("Auto-recovery disabled - manual intervention required\n")
    workflow_result$files_failed <- length(corrupted_files)
  }
  
  # Final verification
  if (workflow_result$files_recovered > 0) {
    if (verbose) cat("\n=== FINAL VERIFICATION ===\n")
    final_verification <- verify_data_integrity(manifest_path, verbose = verbose)
    workflow_result$overall_success <- final_verification$success
  }
  
  if (verbose) {
    cat(sprintf("\n=== WORKFLOW SUMMARY ===\n"))
    cat(sprintf("Files checked: %d\n", workflow_result$files_checked))
    cat(sprintf("Files corrupted: %d\n", workflow_result$files_corrupted))
    cat(sprintf("Files recovered: %d\n", workflow_result$files_recovered))
    cat(sprintf("Files failed: %d\n", workflow_result$files_failed))
    cat(sprintf("Overall success: %s\n", ifelse(workflow_result$overall_success, "YES", "NO")))
  }
  
  return(workflow_result)
}

#' Data Consistency Check
#' 
#' Checks consistency between different data formats (CSV vs RDS)
#' 
#' @param csv_file Path to CSV file
#' @param rds_file Path to RDS file
#' @param verbose Whether to print detailed results (default: TRUE)
#' @return List with consistency check results
data_consistency_check <- function(csv_file, rds_file, verbose = TRUE) {
  
  if (verbose) cat("\n=== DATA CONSISTENCY CHECK ===\n")
  
  consistency_result <- list(
    files_exist = FALSE,
    data_identical = FALSE,
    structure_match = FALSE,
    content_match = FALSE,
    differences = character(0),
    recommendations = character(0)
  )
  
  # Check if both files exist
  if (!file.exists(csv_file) || !file.exists(rds_file)) {
    missing_files <- c(
      if (!file.exists(csv_file)) csv_file,
      if (!file.exists(rds_file)) rds_file
    )
    if (verbose) cat(sprintf("✗ Missing files: %s\n", paste(basename(missing_files), collapse = ", ")))
    return(consistency_result)
  }
  
  consistency_result$files_exist <- TRUE
  
  # Load both datasets
  tryCatch({
    csv_data <- read.csv(csv_file)
    rds_data <- readRDS(rds_file)
    
    # Check structure
    if (identical(dim(csv_data), dim(rds_data)) && identical(names(csv_data), names(rds_data))) {
      consistency_result$structure_match <- TRUE
      if (verbose) cat("✓ Data structures match\n")
    } else {
      consistency_result$differences <- c(consistency_result$differences, "Structure mismatch")
      if (verbose) cat("✗ Data structures do not match\n")
    }
    
    # Check content (allowing for minor floating point differences)
    if (consistency_result$structure_match) {
      # Convert both to same format for comparison
      csv_data_sorted <- csv_data[order(csv_data[[1]]), ]
      rds_data_sorted <- rds_data[order(rds_data[[1]]), ]
      
      # Check if data is identical
      if (identical(csv_data_sorted, rds_data_sorted)) {
        consistency_result$content_match <- TRUE
        consistency_result$data_identical <- TRUE
        if (verbose) cat("✓ Data content is identical\n")
      } else {
        # Check for minor differences (e.g., floating point precision)
        numeric_cols <- sapply(csv_data, is.numeric)
        
        if (any(numeric_cols)) {
          max_diff <- 0
          for (col in names(csv_data)[numeric_cols]) {
            col_diff <- max(abs(csv_data_sorted[[col]] - rds_data_sorted[[col]]), na.rm = TRUE)
            max_diff <- max(max_diff, col_diff)
          }
          
          if (max_diff < 1e-10) {
            consistency_result$content_match <- TRUE
            if (verbose) cat(sprintf("✓ Data content matches (max diff: %.2e)\n", max_diff))
          } else {
            consistency_result$differences <- c(consistency_result$differences, 
                                              sprintf("Content differs (max diff: %.2e)", max_diff))
            if (verbose) cat(sprintf("✗ Data content differs (max diff: %.2e)\n", max_diff))
          }
        } else {
          consistency_result$differences <- c(consistency_result$differences, "Content mismatch")
          if (verbose) cat("✗ Data content does not match\n")
        }
      }
    }
    
  }, error = function(e) {
    consistency_result$differences <- c(consistency_result$differences, 
                                      sprintf("Error loading files: %s", e$message))
    if (verbose) cat(sprintf("✗ Error loading files: %s\n", e$message))
  })
  
  # Generate recommendations
  if (!consistency_result$content_match) {
    consistency_result$recommendations <- c(consistency_result$recommendations, 
                                          "Data inconsistency detected - investigate data sources")
  }
  
  if (length(consistency_result$differences) > 0) {
    consistency_result$recommendations <- c(consistency_result$recommendations, 
                                          "Consider regenerating data files to ensure consistency")
  }
  
  if (verbose) {
    cat(sprintf("\n=== CONSISTENCY SUMMARY ===\n"))
    cat(sprintf("Files exist: %s\n", ifelse(consistency_result$files_exist, "YES", "NO")))
    cat(sprintf("Structure match: %s\n", ifelse(consistency_result$structure_match, "YES", "NO")))
    cat(sprintf("Content match: %s\n", ifelse(consistency_result$content_match, "YES", "NO")))
    
    if (length(consistency_result$differences) > 0) {
      cat("\nDifferences detected:\n")
      for (diff in consistency_result$differences) {
        cat(sprintf("  • %s\n", diff))
      }
    }
    
    if (length(consistency_result$recommendations) > 0) {
      cat("\nRecommendations:\n")
      for (rec in consistency_result$recommendations) {
        cat(sprintf("  • %s\n", rec))
      }
    }
  }
  
  return(consistency_result)
}

cat("\nData recovery and validation module loaded successfully!\n")
cat("Recovery and validation functions available:\n")
cat("  - comprehensive_data_validation(): Validate data quality and structure\n")
cat("  - emergency_data_recovery(): Recover corrupted files from backups\n")
cat("  - automated_recovery_workflow(): Full automated recovery process\n")
cat("  - data_consistency_check(): Verify consistency between file formats\n")
cat("\nValidation features:\n")
cat("  ✓ Comprehensive data quality scoring\n")
cat("  ✓ Domain-specific validation rules\n")
cat("  ✓ Automated backup recovery\n")
cat("  ✓ Data consistency verification\n")
cat("  ✓ Emergency recovery procedures\n")