# =============================================================================
# ML FILE PATH UTILITIES
# =============================================================================
# This module provides cross-platform file path utilities for ML scripts
# to ensure proper directory creation and file handling across Windows/Unix systems.
#
# Author: ML Pipeline
# Date: 2024
# =============================================================================

#' Create Directory with Cross-Platform Support
#' 
#' Creates a directory if it doesn't exist, with proper error handling
#' 
#' @param dir_path Directory path to create
#' @param recursive Whether to create parent directories (default: TRUE)
#' @param verbose Whether to print status messages (default: TRUE)
#' @return TRUE if successful, FALSE otherwise
create_directory <- function(dir_path, recursive = TRUE, verbose = TRUE) {
  
  if (is.null(dir_path) || dir_path == "") {
    if (verbose) cat("Error: Invalid directory path\n")
    return(FALSE)
  }
  
  # Normalize path separators for current OS
  dir_path <- normalizePath(dir_path, mustWork = FALSE)
  
  tryCatch({
    if (!dir.exists(dir_path)) {
      dir.create(dir_path, recursive = recursive, showWarnings = FALSE)
      if (verbose) cat(sprintf("Created directory: %s\n", dir_path))
    } else {
      if (verbose) cat(sprintf("Directory already exists: %s\n", dir_path))
    }
    return(TRUE)
  }, error = function(e) {
    if (verbose) cat(sprintf("Error creating directory %s: %s\n", dir_path, e$message))
    return(FALSE)
  })
}

#' Build Cross-Platform File Path
#' 
#' Constructs file paths using appropriate separators for the current OS
#' 
#' @param ... Path components to join
#' @return Normalized file path
build_path <- function(...) {
  path_components <- list(...)
  path_components <- path_components[!sapply(path_components, is.null)]
  path_components <- path_components[path_components != ""]
  
  if (length(path_components) == 0) {
    return("")
  }
  
  # Join path components
  full_path <- do.call(file.path, path_components)
  
  # Normalize for current OS
  return(normalizePath(full_path, mustWork = FALSE))
}

#' Safe File Save with Directory Creation
#' 
#' Saves data to file, creating directories as needed
#' 
#' @param data Data to save
#' @param file_path Full file path
#' @param save_function Function to use for saving (default: saveRDS)
#' @param verbose Whether to print status messages (default: TRUE)
#' @return TRUE if successful, FALSE otherwise
safe_file_save <- function(data, file_path, save_function = saveRDS, verbose = TRUE) {
  
  if (is.null(file_path) || file_path == "") {
    if (verbose) cat("Error: Invalid file path\n")
    return(FALSE)
  }
  
  # Normalize file path
  file_path <- normalizePath(file_path, mustWork = FALSE)
  
  # Create directory if needed
  dir_path <- dirname(file_path)
  if (!create_directory(dir_path, verbose = verbose)) {
    return(FALSE)
  }
  
  # Save file
  tryCatch({
    save_function(data, file_path)
    if (verbose) cat(sprintf("Saved file: %s\n", file_path))
    return(TRUE)
  }, error = function(e) {
    if (verbose) cat(sprintf("Error saving file %s: %s\n", file_path, e$message))
    return(FALSE)
  })
}

#' Safe File Load with Error Handling
#' 
#' Loads data from file with proper error handling
#' 
#' @param file_path Full file path
#' @param load_function Function to use for loading (default: readRDS)
#' @param verbose Whether to print status messages (default: TRUE)
#' @return Loaded data or NULL if failed
safe_file_load <- function(file_path, load_function = readRDS, verbose = TRUE) {
  
  if (is.null(file_path) || file_path == "") {
    if (verbose) cat("Error: Invalid file path\n")
    return(NULL)
  }
  
  # Normalize file path
  file_path <- normalizePath(file_path, mustWork = FALSE)
  
  if (!file.exists(file_path)) {
    if (verbose) cat(sprintf("File not found: %s\n", file_path))
    return(NULL)
  }
  
  tryCatch({
    data <- load_function(file_path)
    if (verbose) cat(sprintf("Loaded file: %s\n", file_path))
    return(data)
  }, error = function(e) {
    if (verbose) cat(sprintf("Error loading file %s: %s\n", file_path, e$message))
    return(NULL)
  })
}

#' Safe CSV Save with Directory Creation
#' 
#' Saves CSV file with proper directory creation
#' 
#' @param data Data frame to save
#' @param file_path Full file path
#' @param row_names Whether to include row names (default: FALSE)
#' @param verbose Whether to print status messages (default: TRUE)
#' @return TRUE if successful, FALSE otherwise
safe_csv_save <- function(data, file_path, row_names = FALSE, verbose = TRUE) {
  return(safe_file_save(data, file_path, 
                       function(d, f) write.csv(d, f, row.names = row_names), 
                       verbose))
}

#' Safe CSV Load with Error Handling
#' 
#' Loads CSV file with proper error handling
#' 
#' @param file_path Full file path
#' @param verbose Whether to print status messages (default: TRUE)
#' @return Data frame or NULL if failed
safe_csv_load <- function(file_path, verbose = TRUE) {
  return(safe_file_load(file_path, read.csv, verbose))
}

#' Safe Plot Save with Directory Creation
#' 
#' Saves ggplot with proper directory creation
#' 
#' @param plot ggplot object
#' @param file_path Full file path
#' @param width Plot width (default: 12)
#' @param height Plot height (default: 8)
#' @param dpi Resolution (default: 300)
#' @param verbose Whether to print status messages (default: TRUE)
#' @return TRUE if successful, FALSE otherwise
safe_plot_save <- function(plot, file_path, width = 12, height = 8, dpi = 300, verbose = TRUE) {
  
  if (!require(ggplot2, quietly = TRUE)) {
    if (verbose) cat("Error: ggplot2 package required for plot saving\n")
    return(FALSE)
  }
  
  return(safe_file_save(plot, file_path, 
                       function(p, f) ggsave(f, p, width = width, height = height, dpi = dpi), 
                       verbose))
}

#' Get ML Results Directory
#' 
#' Returns the standard ML results directory path
#' 
#' @param create_if_missing Whether to create directory if it doesn't exist (default: TRUE)
#' @return Directory path
get_ml_results_dir <- function(create_if_missing = TRUE) {
  results_dir <- build_path(getwd(), "ml_results")
  
  if (create_if_missing) {
    create_directory(results_dir, verbose = FALSE)
  }
  
  return(results_dir)
}

#' Get ML Plots Directory
#' 
#' Returns the standard ML plots directory path
#' 
#' @param create_if_missing Whether to create directory if it doesn't exist (default: TRUE)
#' @return Directory path
get_ml_plots_dir <- function(create_if_missing = TRUE) {
  plots_dir <- build_path(getwd(), "ml_plots")
  
  if (create_if_missing) {
    create_directory(plots_dir, verbose = FALSE)
  }
  
  return(plots_dir)
}

#' Clean Up Old Files
#' 
#' Removes old ML result files to prevent conflicts
#' 
#' @param file_patterns Vector of file patterns to remove
#' @param verbose Whether to print status messages (default: TRUE)
#' @return Number of files removed
cleanup_old_files <- function(file_patterns = c("ml_*.rds", "ml_*.csv", "*.png"), verbose = TRUE) {
  
  removed_count <- 0
  
  for (pattern in file_patterns) {
    files_to_remove <- list.files(pattern = glob2rx(pattern), full.names = TRUE)
    
    for (file_path in files_to_remove) {
      tryCatch({
        file.remove(file_path)
        removed_count <- removed_count + 1
        if (verbose) cat(sprintf("Removed: %s\n", file_path))
      }, error = function(e) {
        if (verbose) cat(sprintf("Could not remove %s: %s\n", file_path, e$message))
      })
    }
  }
  
  if (verbose) cat(sprintf("Cleanup complete. Removed %d files.\n", removed_count))
  return(removed_count)
}

cat("ML file path utilities loaded successfully!\n")
cat("Available functions:\n")
cat("  - create_directory(): Cross-platform directory creation\n")
cat("  - build_path(): Cross-platform path construction\n")
cat("  - safe_file_save(): Safe file saving with directory creation\n")
cat("  - safe_file_load(): Safe file loading with error handling\n")
cat("  - safe_csv_save(): Safe CSV saving\n")
cat("  - safe_csv_load(): Safe CSV loading\n")
cat("  - safe_plot_save(): Safe plot saving\n")
cat("  - get_ml_results_dir(): Get ML results directory\n")
cat("  - get_ml_plots_dir(): Get ML plots directory\n")
cat("  - cleanup_old_files(): Clean up old result files\n")