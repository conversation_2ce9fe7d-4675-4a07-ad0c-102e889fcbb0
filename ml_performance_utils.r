# =============================================================================
# STANDARDIZED PERFORMANCE METRICS UTILITIES
# =============================================================================
# This module provides standardized performance metrics calculation functions
# to ensure consistency across all ML scripts in the project.
#
# Author: ML Pipeline
# Date: 2024
# =============================================================================

# Load required libraries
if (!require(pROC, quietly = TRUE)) {
  install.packages("pROC")
  library(pROC)
}

if (!require(caret, quietly = TRUE)) {
  install.packages("caret")
  library(caret)
}

#' Calculate Standardized Performance Metrics
#' 
#' This function provides consistent performance metrics calculation across all ML scripts.
#' It handles edge cases and ensures standardized output format.
#' 
#' @param y_true Vector of true binary labels (0/1)
#' @param y_pred Vector of predicted probabilities or binary predictions
#' @param threshold Threshold for converting probabilities to binary predictions (default: 0.5)
#' @param task_type Type of task: "classification" or "regression" (default: "classification")
#' @return List containing standardized performance metrics
calculate_standardized_performance <- function(y_true, y_pred, threshold = 0.5, task_type = "classification") {
  
  # Input validation
  if (length(y_true) != length(y_pred)) {
    stop("Length of y_true and y_pred must be equal")
  }
  
  if (length(y_true) == 0) {
    return(list(
      auc = NA,
      accuracy = NA,
      sensitivity = NA,
      specificity = NA,
      precision = NA,
      recall = NA,
      f1_score = NA,
      note = "Empty input vectors"
    ))
  }
  
  # Handle regression tasks
  if (task_type == "regression") {
    # Remove NA values
    valid_idx <- !is.na(y_true) & !is.na(y_pred) & is.finite(y_true) & is.finite(y_pred)
    y_true_clean <- y_true[valid_idx]
    y_pred_clean <- y_pred[valid_idx]
    
    if (length(y_true_clean) == 0) {
      return(list(
        rmse = NA,
        mae = NA,
        r2 = NA,
        note = "No valid data points for regression"
      ))
    }
    
    # Calculate regression metrics
    rmse <- sqrt(mean((y_true_clean - y_pred_clean)^2))
    mae <- mean(abs(y_true_clean - y_pred_clean))
    
    # R-squared
    ss_res <- sum((y_true_clean - y_pred_clean)^2)
    ss_tot <- sum((y_true_clean - mean(y_true_clean))^2)
    r2 <- ifelse(ss_tot > 0, 1 - (ss_res / ss_tot), NA)
    
    return(list(
      rmse = rmse,
      mae = mae,
      r2 = r2,
      n_samples = length(y_true_clean)
    ))
  }
  
  # Classification tasks
  # Remove NA values
  valid_idx <- !is.na(y_true) & !is.na(y_pred)
  y_true_clean <- y_true[valid_idx]
  y_pred_clean <- y_pred[valid_idx]
  
  if (length(y_true_clean) == 0) {
    return(list(
      auc = NA,
      accuracy = NA,
      sensitivity = NA,
      specificity = NA,
      precision = NA,
      recall = NA,
      f1_score = NA,
      note = "No valid data points"
    ))
  }
  
  # Handle edge case: only one class in true labels
  if (length(unique(y_true_clean)) == 1) {
    return(list(
      auc = NA,
      accuracy = mean((y_pred_clean > threshold) == y_true_clean),
      sensitivity = NA,
      specificity = NA,
      precision = NA,
      recall = NA,
      f1_score = NA,
      note = "Only one class in true labels"
    ))
  }
  
  # Calculate AUC-ROC
  auc_score <- tryCatch({
    roc_obj <- roc(y_true_clean, y_pred_clean, quiet = TRUE)
    as.numeric(roc_obj$auc)
  }, error = function(e) {
    NA
  })
  
  # Convert predictions to binary
  y_pred_binary <- as.numeric(y_pred_clean > threshold)
  
  # Calculate confusion matrix elements
  tp <- sum(y_true_clean == 1 & y_pred_binary == 1)
  tn <- sum(y_true_clean == 0 & y_pred_binary == 0)
  fp <- sum(y_true_clean == 0 & y_pred_binary == 1)
  fn <- sum(y_true_clean == 1 & y_pred_binary == 0)
  
  # Calculate metrics with proper handling of edge cases
  accuracy <- (tp + tn) / (tp + tn + fp + fn)
  sensitivity <- ifelse(tp + fn > 0, tp / (tp + fn), NA)
  specificity <- ifelse(tn + fp > 0, tn / (tn + fp), NA)
  precision <- ifelse(tp + fp > 0, tp / (tp + fp), NA)
  recall <- sensitivity
  
  # F1 score
  f1_score <- ifelse(!is.na(precision) & !is.na(recall) & (precision + recall) > 0, 
                    2 * precision * recall / (precision + recall), NA)
  
  # Create confusion matrix
  confusion_matrix <- matrix(c(tn, fp, fn, tp), nrow = 2, 
                           dimnames = list(c("Pred_0", "Pred_1"), c("True_0", "True_1")))
  
  return(list(
    auc = auc_score,
    accuracy = accuracy,
    sensitivity = sensitivity,
    specificity = specificity,
    precision = precision,
    recall = recall,
    f1_score = f1_score,
    confusion_matrix = confusion_matrix,
    n_samples = length(y_true_clean),
    n_positive = sum(y_true_clean == 1),
    n_negative = sum(y_true_clean == 0)
  ))
}

#' Calculate Performance Metrics with Caret (Alternative Implementation)
#' 
#' Uses caret package functions for additional validation
#' 
#' @param y_true Vector of true binary labels (0/1)
#' @param y_pred Vector of predicted probabilities
#' @param threshold Threshold for converting probabilities to binary predictions
#' @return List containing performance metrics calculated using caret
calculate_caret_performance <- function(y_true, y_pred, threshold = 0.5) {
  
  # Input validation
  if (length(y_true) != length(y_pred) || length(y_true) == 0) {
    return(list(note = "Invalid input"))
  }
  
  # Remove NA values
  valid_idx <- !is.na(y_true) & !is.na(y_pred)
  y_true_clean <- y_true[valid_idx]
  y_pred_clean <- y_pred[valid_idx]
  
  if (length(y_true_clean) == 0 || length(unique(y_true_clean)) == 1) {
    return(list(note = "Insufficient valid data or single class"))
  }
  
  tryCatch({
    # Convert to factors for caret
    y_true_factor <- factor(y_true_clean, levels = c(0, 1))
    y_pred_factor <- factor(as.numeric(y_pred_clean > threshold), levels = c(0, 1))
    
    # Calculate metrics using caret
    cm <- confusionMatrix(y_pred_factor, y_true_factor, positive = "1")
    
    return(list(
      accuracy = as.numeric(cm$overall["Accuracy"]),
      sensitivity = as.numeric(cm$byClass["Sensitivity"]),
      specificity = as.numeric(cm$byClass["Specificity"]),
      precision = as.numeric(cm$byClass["Pos Pred Value"]),
      recall = as.numeric(cm$byClass["Sensitivity"]),
      f1_score = as.numeric(cm$byClass["F1"]),
      balanced_accuracy = as.numeric(cm$byClass["Balanced Accuracy"])
    ))
  }, error = function(e) {
    return(list(note = paste("Caret calculation failed:", e$message)))
  })
}

#' Format Performance Metrics for Display
#' 
#' Formats performance metrics in a consistent, readable format
#' 
#' @param performance_list List of performance metrics
#' @param digits Number of decimal places (default: 4)
#' @return Formatted string representation
format_performance_metrics <- function(performance_list, digits = 4) {
  
  if (is.null(performance_list) || length(performance_list) == 0) {
    return("No performance metrics available")
  }
  
  # Handle regression metrics
  if ("rmse" %in% names(performance_list)) {
    return(sprintf(
      "RMSE: %s, MAE: %s, R²: %s (n=%d)",
      ifelse(is.na(performance_list$rmse), "NA", round(performance_list$rmse, digits)),
      ifelse(is.na(performance_list$mae), "NA", round(performance_list$mae, digits)),
      ifelse(is.na(performance_list$r2), "NA", round(performance_list$r2, digits)),
      ifelse(is.null(performance_list$n_samples), 0, performance_list$n_samples)
    ))
  }
  
  # Handle classification metrics
  metrics_str <- sprintf(
    "AUC: %s, Accuracy: %s, Sensitivity: %s, Specificity: %s",
    ifelse(is.na(performance_list$auc), "NA", round(performance_list$auc, digits)),
    ifelse(is.na(performance_list$accuracy), "NA", round(performance_list$accuracy, digits)),
    ifelse(is.na(performance_list$sensitivity), "NA", round(performance_list$sensitivity, digits)),
    ifelse(is.na(performance_list$specificity), "NA", round(performance_list$specificity, digits))
  )
  
  # Add sample size information if available
  if (!is.null(performance_list$n_samples)) {
    metrics_str <- paste0(metrics_str, sprintf(" (n=%d)", performance_list$n_samples))
  }
  
  # Add note if present
  if (!is.null(performance_list$note)) {
    metrics_str <- paste0(metrics_str, " [Note: ", performance_list$note, "]")
  }
  
  return(metrics_str)
}

#' Compare Multiple Model Performances
#' 
#' Creates a comparison table of multiple model performances
#' 
#' @param performance_list Named list of performance metrics for different models
#' @param sort_by Metric to sort by (default: "auc")
#' @return Data frame with model comparison
compare_model_performances <- function(performance_list, sort_by = "auc") {
  
  if (length(performance_list) == 0) {
    return(data.frame(
      Model = character(0),
      AUC = numeric(0),
      Accuracy = numeric(0),
      Sensitivity = numeric(0),
      Specificity = numeric(0)
    ))
  }
  
  comparison_df <- data.frame(
    Model = character(),
    AUC = numeric(),
    Accuracy = numeric(),
    Sensitivity = numeric(),
    Specificity = numeric(),
    stringsAsFactors = FALSE
  )
  
  for (model_name in names(performance_list)) {
    perf <- performance_list[[model_name]]
    
    if (!is.null(perf) && is.list(perf)) {
      comparison_df <- rbind(comparison_df, data.frame(
        Model = model_name,
        AUC = ifelse(is.null(perf$auc) || is.na(perf$auc), 0, perf$auc),
        Accuracy = ifelse(is.null(perf$accuracy) || is.na(perf$accuracy), 0, perf$accuracy),
        Sensitivity = ifelse(is.null(perf$sensitivity) || is.na(perf$sensitivity), 0, perf$sensitivity),
        Specificity = ifelse(is.null(perf$specificity) || is.na(perf$specificity), 0, perf$specificity),
        stringsAsFactors = FALSE
      ))
    }
  }
  
  # Sort by specified metric
  if (sort_by %in% colnames(comparison_df) && nrow(comparison_df) > 0) {
    comparison_df <- comparison_df[order(comparison_df[[sort_by]], decreasing = TRUE), ]
  }
  
  return(comparison_df)
}

cat("Standardized ML performance utilities loaded successfully!\n")
cat("Available functions:\n")
cat("  - calculate_standardized_performance(): Main standardized metrics function\n")
cat("  - calculate_caret_performance(): Alternative using caret package\n")
cat("  - format_performance_metrics(): Format metrics for display\n")
cat("  - compare_model_performances(): Compare multiple model performances\n")