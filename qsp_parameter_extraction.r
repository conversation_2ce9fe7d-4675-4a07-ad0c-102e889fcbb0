# =============================================================================
# QSP PARAMETER EXTRACTION FROM SIMULATION RESULTS
# =============================================================================
# This script extracts IC50 and maximum inhibition parameters from QSP PK-PD
# simulation results rather than using literature-derived hardcoded values
# =============================================================================

# Required packages
library(deSolve)
library(dplyr)
library(ggplot2)
library(minpack.lm)  # For nonlinear least squares fitting
library(broom)       # For tidy model outputs

if (!require("minpack.lm")) install.packages("minpack.lm")
if (!require("broom")) install.packages("broom")

# =============================================================================
# MECHANISTIC QSP MODEL FOR PARAMETER EXTRACTION
# =============================================================================

# Simplified mechanistic model focused on BTK/TEC kinase dynamics
# This model simulates the actual biochemical processes without hardcoded IC50s
mechanistic_btk_model <- function(time, state, parameters, ibrutinib_conc, agonist_type) {
  with(as.list(c(state, parameters)), {
    
    # Get current ibrutinib concentration
    t_idx <- max(1, which.min(abs(time - ibrutinib_conc$time)))
    C_ibr <- ibrutinib_conc$Cp_nM[t_idx]
    
    # Mechanistic BTK/TEC binding kinetics (mass action)
    # BTK binding: Ibr + BTK <-> Ibr-BTK (irreversible covalent binding)
    # TEC binding: Ibr + TEC <-> Ibr-TEC (reversible binding)
    
    # BTK kinase dynamics (covalent irreversible binding)
    k_btk_bind <- 0.1    # Binding rate constant (nM^-1 h^-1)
    BTK_total <- 100     # Total BTK concentration (nM)
    dBTK_free <- -k_btk_bind * C_ibr * BTK_free
    
    # TEC kinase dynamics (reversible binding)
    k_tec_on <- 0.05     # Association rate constant (nM^-1 h^-1)
    k_tec_off <- 5.0     # Dissociation rate constant (h^-1)
    TEC_total <- 80      # Total TEC concentration (nM)
    TEC_bound <- TEC_total - TEC_free
    dTEC_free <- k_tec_off * TEC_bound - k_tec_on * C_ibr * TEC_free
    
    # Pathway-specific kinase requirements
    # Based on mechanistic understanding of platelet signaling
    
    if (agonist_type == "collagen") {
      # GPVI pathway - highly BTK dependent, moderately TEC dependent
      btk_requirement <- 0.8
      tec_requirement <- 0.3
      pathway_stimulus <- collagen_stimulus
    } else if (agonist_type == "ADP") {
      # P2Y12 pathway - minimally BTK dependent
      btk_requirement <- 0.1
      tec_requirement <- 0.05
      pathway_stimulus <- adp_stimulus
    } else if (agonist_type == "thrombin") {
      # PAR pathway - minimally BTK/TEC dependent
      btk_requirement <- 0.15
      tec_requirement <- 0.08
      pathway_stimulus <- thrombin_stimulus
    } else if (agonist_type == "arachidonic_acid") {
      # TxA2 pathway - moderately BTK dependent
      btk_requirement <- 0.4
      tec_requirement <- 0.2
      pathway_stimulus <- txa2_stimulus
    } else if (agonist_type == "ristocetin") {
      # VWF/GPIb pathway - BTK independent
      btk_requirement <- 0.02
      tec_requirement <- 0.01
      pathway_stimulus <- ristocetin_stimulus
    } else {
      btk_requirement <- 0.3
      tec_requirement <- 0.15
      pathway_stimulus <- 1.0
    }
    
    # Calculate pathway activity based on available kinases
    btk_activity <- BTK_free / BTK_total
    tec_activity <- TEC_free / TEC_total
    
    # Pathway activation depends on kinase availability
    pathway_activity <- pathway_stimulus * 
                       (btk_activity * btk_requirement + (1 - btk_requirement)) *
                       (tec_activity * tec_requirement + (1 - tec_requirement))
    
    # Platelet aggregation response
    k_agg <- 2.0
    k_deagg <- 0.5
    dAgg <- k_agg * pathway_activity - k_deagg * Agg
    
    # Return derivatives
    list(c(dBTK_free, dTEC_free, dAgg))
  })
}

# =============================================================================
# DOSE-RESPONSE CURVE GENERATION FROM QSP MODEL
# =============================================================================

# Generate dose-response data using mechanistic QSP model
generate_qsp_dose_response <- function(agonist_type, concentration_range = seq(0, 1000, by = 50)) {
  
  cat(sprintf("Generating QSP dose-response for %s...\n", agonist_type))
  
  dose_response_data <- data.frame()
  
  for (conc in concentration_range) {
    # Create PK profile for this concentration
    time_points <- seq(0, 24, by = 0.5)
    pk_data <- data.frame(
      time = time_points,
      Cp_nM = rep(conc, length(time_points))
    )
    
    # Initial conditions
    initial_state <- c(
      BTK_free = 100,  # Total BTK
      TEC_free = 80,   # Total TEC
      Agg = 0.1        # Baseline aggregation
    )
    
    # Model parameters
    params <- list(
      collagen_stimulus = ifelse(agonist_type == "collagen", 1.0, 0),
      adp_stimulus = ifelse(agonist_type == "ADP", 1.0, 0),
      thrombin_stimulus = ifelse(agonist_type == "thrombin", 1.0, 0),
      txa2_stimulus = ifelse(agonist_type == "arachidonic_acid", 1.0, 0),
      ristocetin_stimulus = ifelse(agonist_type == "ristocetin", 1.0, 0)
    )
    
    # Solve ODE
    result <- ode(
      y = initial_state,
      times = time_points,
      func = mechanistic_btk_model,
      parms = params,
      ibrutinib_conc = pk_data,
      agonist_type = agonist_type,
      method = "lsoda"
    )
    
    # Extract steady-state aggregation (last time point)
    final_agg <- result[nrow(result), "Agg"]
    
    # Calculate inhibition relative to control (0 nM)
    if (conc == 0) {
      control_response <- final_agg
    }
    
    dose_response_data <- rbind(dose_response_data, data.frame(
      concentration = conc,
      aggregation = final_agg,
      agonist = agonist_type
    ))
  }
  
  # Calculate percent inhibition
  control_response <- dose_response_data$aggregation[dose_response_data$concentration == 0]
  dose_response_data$percent_inhibition <- 
    100 * (1 - dose_response_data$aggregation / control_response)
  
  return(dose_response_data)
}

# =============================================================================
# IC50 EXTRACTION FROM DOSE-RESPONSE CURVES
# =============================================================================

# Fit Hill equation to extract IC50 and maximum inhibition
fit_hill_equation <- function(dose_response_data) {
  
  # Hill equation: Inhibition = (Max_Inhib * C^n) / (IC50^n + C^n)
  hill_model <- function(concentration, max_inhib, ic50, hill_coef) {
    (max_inhib * concentration^hill_coef) / (ic50^hill_coef + concentration^hill_coef)
  }
  
  # Fit nonlinear model
  tryCatch({
    fit <- nlsLM(
      percent_inhibition ~ hill_model(concentration, max_inhib, ic50, hill_coef),
      data = dose_response_data,
      start = list(max_inhib = 50, ic50 = 200, hill_coef = 1.0),
      lower = c(0, 1, 0.1),
      upper = c(100, 2000, 5.0),
      control = nls.lm.control(maxiter = 1000)
    )
    
    # Extract parameters
    params <- coef(fit)
    
    # Calculate goodness of fit
    r_squared <- 1 - sum(residuals(fit)^2) / sum((dose_response_data$percent_inhibition - mean(dose_response_data$percent_inhibition))^2)
    
    return(list(
      ic50 = params["ic50"],
      max_inhibition = params["max_inhib"],
      hill_coefficient = params["hill_coef"],
      r_squared = r_squared,
      fit_object = fit
    ))
    
  }, error = function(e) {
    cat(sprintf("Fitting failed: %s\n", e$message))
    return(list(
      ic50 = NA,
      max_inhibition = NA,
      hill_coefficient = NA,
      r_squared = NA,
      fit_object = NULL
    ))
  })
}

# =============================================================================
# EXTRACT PARAMETERS FOR ALL AGONISTS
# =============================================================================

# Main function to extract QSP-derived parameters
extract_qsp_parameters <- function() {
  
  cat("=== EXTRACTING IC50 PARAMETERS FROM QSP SIMULATIONS ===\n")
  
  agonists <- c("collagen", "ADP", "thrombin", "arachidonic_acid", "ristocetin")
  qsp_parameters <- data.frame()
  
  for (agonist in agonists) {
    cat(sprintf("\nProcessing %s...\n", agonist))
    
    # Generate dose-response curve
    dose_response <- generate_qsp_dose_response(agonist)
    
    # Fit Hill equation
    fit_results <- fit_hill_equation(dose_response)
    
    # Store results
    qsp_parameters <- rbind(qsp_parameters, data.frame(
      agonist = agonist,
      ic50_nM = fit_results$ic50,
      max_inhibition_percent = fit_results$max_inhibition,
      hill_coefficient = fit_results$hill_coefficient,
      r_squared = fit_results$r_squared,
      source = "QSP_simulation",
      stringsAsFactors = FALSE
    ))
    
    cat(sprintf("  IC50: %.1f nM, Max Inhibition: %.1f%%, R²: %.3f\n", 
                fit_results$ic50, fit_results$max_inhibition, fit_results$r_squared))
  }
  
  return(qsp_parameters)
}

# =============================================================================
# COMPARISON WITH LITERATURE VALUES
# =============================================================================

# Compare QSP-derived parameters with literature values
compare_with_literature <- function(qsp_params) {
  
  # Literature values (current hardcoded values)
  literature_params <- data.frame(
    agonist = c("collagen", "ADP", "thrombin", "arachidonic_acid", "ristocetin"),
    ic50_nM = c(90, 1800, 2200, 500, 5000),
    max_inhibition_percent = c(98, 6, 10, 55, 5),
    source = "Literature",
    stringsAsFactors = FALSE
  )
  
  # Combine datasets
  comparison <- rbind(
    qsp_params[, c("agonist", "ic50_nM", "max_inhibition_percent", "source")],
    literature_params
  )
  
  cat("\n=== COMPARISON: QSP vs Literature Parameters ===\n")
  print(comparison)
  
  return(comparison)
}

# =============================================================================
# MAIN EXECUTION FUNCTION
# =============================================================================

# Run the complete parameter extraction workflow
run_qsp_parameter_extraction <- function() {
  
  cat("Starting QSP parameter extraction workflow...\n")
  
  # Extract parameters from QSP simulations
  qsp_params <- extract_qsp_parameters()
  
  # Compare with literature
  comparison <- compare_with_literature(qsp_params)
  
  # Save results
  write.csv(qsp_params, "qsp_derived_parameters.csv", row.names = FALSE)
  write.csv(comparison, "qsp_literature_comparison.csv", row.names = FALSE)
  
  cat("\nQSP parameter extraction completed!\n")
  cat("Results saved to: qsp_derived_parameters.csv\n")
  cat("Comparison saved to: qsp_literature_comparison.csv\n")
  
  return(list(
    qsp_parameters = qsp_params,
    comparison = comparison
  ))
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Load QSP-derived parameters for use in other scripts
load_qsp_parameters <- function(file_path = "qsp_derived_parameters.csv") {
  if (file.exists(file_path)) {
    return(read.csv(file_path, stringsAsFactors = FALSE))
  } else {
    cat("QSP parameters file not found. Run run_qsp_parameter_extraction() first.\n")
    return(NULL)
  }
}

# Convert QSP parameters to format used by existing model
format_qsp_parameters_for_model <- function(qsp_params) {
  
  formatted_params <- list()
  
  for (i in 1:nrow(qsp_params)) {
    agonist <- qsp_params$agonist[i]
    
    # Map agonist names to model format
    model_name <- switch(agonist,
      "collagen" = "Collagen",
      "ADP" = "ADP", 
      "thrombin" = "Thrombin",
      "arachidonic_acid" = "TxA2",
      "ristocetin" = "Ristocetin"
    )
    
    formatted_params[[model_name]] <- list(
      IC50 = qsp_params$ic50_nM[i],
      max_inhibition = qsp_params$max_inhibition_percent[i] / 100,  # Convert to fraction
      hill_coefficient = qsp_params$hill_coefficient[i]
    )
  }
  
  return(formatted_params)
}

cat("QSP parameter extraction functions loaded successfully!\n")
cat("Run run_qsp_parameter_extraction() to extract parameters from simulations.\n")