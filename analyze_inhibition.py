import pandas as pd
import numpy as np

# Read the enhanced virtual clinical trial results
df = pd.read_csv('enhanced_virtual_clinical_trial_results.csv')

print('=== CLINICAL SIGNIFICANCE ANALYSIS ===')
print()

# Categorize patients by overall inhibition level
ibrutinib_patients = df[df['treatment_arm'] == 'Ibrutinib_420mg']

# Define clinically significant inhibition thresholds
collagen_threshold = 20    
arachidonic_threshold = 10 

print('Patients with Clinically Significant Inhibition (Ibrutinib 420mg):')
print()

# Collagen inhibition analysis
collagen_sig = ibrutinib_patients[ibrutinib_patients['collagen_inhibition'] > collagen_threshold]
print(f'Collagen inhibition >20%: {len(collagen_sig)}/{len(ibrutinib_patients)} patients ({len(collagen_sig)/len(ibrutinib_patients)*100:.1f}%)')
print(f'  Mean inhibition in these patients: {collagen_sig["collagen_inhibition"].mean():.1f}%')

# Arachidonic acid inhibition analysis
arachidonic_sig = ibrutinib_patients[ibrutinib_patients['arachidonic_inhibition'] > arachidonic_threshold]
print(f'Arachidonic acid inhibition >10%: {len(arachidonic_sig)}/{len(ibrutinib_patients)} patients ({len(arachidonic_sig)/len(ibrutinib_patients)*100:.1f}%)')
print(f'  Mean inhibition in these patients: {arachidonic_sig["arachidonic_inhibition"].mean():.1f}%')

print()
print('=== KEY FINDINGS SUMMARY ===')
print()
print('1. OVERALL PLATELET INHIBITION PATTERN:')
print(f'   • Collagen pathway most affected (mean: {ibrutinib_patients["collagen_inhibition"].mean():.1f}%)')
print(f'   • Arachidonic acid pathway moderately affected (mean: {ibrutinib_patients["arachidonic_inhibition"].mean():.1f}%)')
print(f'   • Other pathways minimally affected')
print()

print('2. CLINICAL IMPACT:')
clinically_significant = len(ibrutinib_patients[ibrutinib_patients['collagen_inhibition'] > 20])
print(f'   • {clinically_significant}/{len(ibrutinib_patients)} patients ({clinically_significant/len(ibrutinib_patients)*100:.1f}%) have clinically significant collagen inhibition')

# Calculate overall bleeding risk
bleeding_patients = len(ibrutinib_patients[ibrutinib_patients['bleeding_events'] > 0])
print(f'   • {bleeding_patients}/{len(ibrutinib_patients)} patients ({bleeding_patients/len(ibrutinib_patients)*100:.1f}%) experienced bleeding events')

# Show high vs low inhibition groups
high_inhibition = ibrutinib_patients[ibrutinib_patients['collagen_inhibition'] > 30]
low_inhibition = ibrutinib_patients[ibrutinib_patients['collagen_inhibition'] <= 10]

print()
print('3. COMPARISON BY INHIBITION LEVEL:')
print(f'   High inhibition (>30% collagen): {len(high_inhibition)} patients')
print(f'   Low inhibition (≤10% collagen): {len(low_inhibition)} patients')

if len(high_inhibition) > 0:
    high_bleeding_rate = (high_inhibition['bleeding_events'] > 0).mean() * 100
    print(f'   High group bleeding rate: {(high_inhibition["bleeding_events"] > 0).sum()}/{len(high_inhibition)} ({high_bleeding_rate:.1f}%)')
    
if len(low_inhibition) > 0:
    low_bleeding_rate = (low_inhibition['bleeding_events'] > 0).mean() * 100
    print(f'   Low group bleeding rate: {(low_inhibition["bleeding_events"] > 0).sum()}/{len(low_inhibition)} ({low_bleeding_rate:.1f}%)')

print()
print('=== PATHWAY-SPECIFIC ANALYSIS ===')
print()

# Analyze each pathway for ibrutinib patients
inhibition_cols = ['collagen_inhibition', 'adp_inhibition', 'arachidonic_inhibition', 
                   'thrombin_inhibition', 'epinephrine_inhibition', 'ristocetin_inhibition']

for col in inhibition_cols:
    if col in df.columns:
        data = ibrutinib_patients[col].dropna()
        pathway_name = col.replace('_', ' ').title()
        
        # Count patients with >5% inhibition (mild threshold)
        mild_inhibition = len(data[data > 5])
        
        print(f'{pathway_name}:')
        print(f'  Mean: {data.mean():.1f}% (Range: {data.min():.1f}% to {data.max():.1f}%)')
        print(f'  Patients with >5% inhibition: {mild_inhibition}/{len(data)} ({mild_inhibition/len(data)*100:.1f}%)')
        print()
